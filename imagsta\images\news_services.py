"""
News aggregation services for fetching content from various sources.
"""

import requests
import logging
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from django.utils import timezone
from django.conf import settings
from decouple import config

from .models import NewsSource, NewsArticle, Category

logger = logging.getLogger(__name__)


class NewsSourceError(Exception):
    """Custom exception for news source errors."""
    pass


class BaseNewsFetcher:
    """Base class for news fetchers."""
    
    def __init__(self, source: NewsSource):
        self.source = source
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Imagsta News Aggregator 1.0',
            'Accept': 'application/json, text/html, application/xml, */*',
        })
    
    def fetch_articles(self) -> List[Dict[str, Any]]:
        """Fetch articles from the source. Must be implemented by subclasses."""
        raise NotImplementedError
    
    def parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime object."""
        if not date_str:
            return None
        
        try:
            # Try common date formats
            formats = [
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%d %H:%M:%S',
                '%a, %d %b %Y %H:%M:%S %Z',
                '%a, %d %b %Y %H:%M:%S %z',
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            
            # Try parsing with timezone info
            import re
            # Remove timezone info for simple parsing
            clean_date = re.sub(r'\s*[+-]\d{4}$', '', date_str)
            clean_date = re.sub(r'\s*[A-Z]{3,4}$', '', clean_date)

            for fmt in ['%a, %d %b %Y %H:%M:%S', '%Y-%m-%d %H:%M:%S']:
                try:
                    return datetime.strptime(clean_date.strip(), fmt)
                except ValueError:
                    continue
                
        except Exception as e:
            logger.warning(f"Failed to parse date '{date_str}': {e}")
        
        return None
    
    def clean_content(self, content: str) -> str:
        """Clean and normalize content."""
        if not content:
            return ""
        
        # Remove HTML tags
        soup = BeautifulSoup(content, 'html.parser')
        text = soup.get_text()
        
        # Clean up whitespace
        text = ' '.join(text.split())
        
        return text[:5000]  # Limit content length


class RSSFetcher(BaseNewsFetcher):
    """Fetcher for RSS feeds using simple XML parsing."""

    def fetch_articles(self) -> List[Dict[str, Any]]:
        """Fetch articles from RSS feed."""
        try:
            response = self.session.get(self.source.url, timeout=30)
            response.raise_for_status()

            # Parse XML
            root = ET.fromstring(response.content)

            # Find all item elements (RSS) or entry elements (Atom)
            items = root.findall('.//item') or root.findall('.//{http://www.w3.org/2005/Atom}entry')

            articles = []
            for item in items[:self.source.max_articles_per_fetch]:
                article_data = self._parse_rss_item(item)

                if article_data['title'] and article_data['url']:
                    articles.append(article_data)

            return articles

        except requests.RequestException as e:
            raise NewsSourceError(f"Failed to fetch RSS feed: {e}")
        except ET.ParseError as e:
            raise NewsSourceError(f"Failed to parse RSS XML: {e}")
        except Exception as e:
            raise NewsSourceError(f"Failed to parse RSS feed: {e}")

    def _parse_rss_item(self, item) -> Dict[str, Any]:
        """Parse a single RSS item or Atom entry."""
        # Handle both RSS and Atom formats
        title_elem = item.find('title') or item.find('.//{http://www.w3.org/2005/Atom}title')
        link_elem = item.find('link') or item.find('.//{http://www.w3.org/2005/Atom}link')
        desc_elem = item.find('description') or item.find('.//{http://www.w3.org/2005/Atom}summary')
        author_elem = item.find('author') or item.find('.//{http://www.w3.org/2005/Atom}author')
        date_elem = item.find('pubDate') or item.find('.//{http://www.w3.org/2005/Atom}published')

        # Extract text content
        title = title_elem.text if title_elem is not None else ''

        # Handle link element (might be an attribute in Atom)
        if link_elem is not None:
            url = link_elem.text or link_elem.get('href', '')
        else:
            url = ''

        description = desc_elem.text if desc_elem is not None else ''
        author = author_elem.text if author_elem is not None else ''
        pub_date = date_elem.text if date_elem is not None else ''

        return {
            'title': title,
            'content': self.clean_content(description),
            'url': url,
            'author': author,
            'published_date': self.parse_date(pub_date),
            'image_url': self._extract_image_from_item(item, description),
        }

    def _extract_image_from_item(self, item, description) -> str:
        """Extract image URL from RSS item."""
        # Try to find image in description/content
        if description:
            soup = BeautifulSoup(description, 'html.parser')
            img = soup.find('img')
            if img and img.get('src'):
                return img['src']

        # Try enclosure
        enclosure = item.find('enclosure')
        if enclosure is not None and enclosure.get('type', '').startswith('image/'):
            return enclosure.get('url', '')

        return ''


class NewsAPIFetcher(BaseNewsFetcher):
    """Fetcher for NewsAPI.org."""
    
    def __init__(self, source: NewsSource):
        super().__init__(source)
        self.api_key = config('NEWSAPI_KEY', default='')
        if not self.api_key:
            raise NewsSourceError("NewsAPI key not configured")
    
    def fetch_articles(self) -> List[Dict[str, Any]]:
        """Fetch articles from NewsAPI."""
        try:
            # Parse query parameters from source URL
            params = {
                'apiKey': self.api_key,
                'pageSize': min(self.source.max_articles_per_fetch, 100),
                'sortBy': 'publishedAt',
            }
            
            # Add custom parameters from source configuration
            if self.source.api_config:
                params.update(self.source.api_config)
            
            response = self.session.get(
                'https://newsapi.org/v2/everything',
                params=params,
                timeout=30
            )
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != 'ok':
                raise NewsSourceError(f"NewsAPI error: {data.get('message', 'Unknown error')}")
            
            articles = []
            for article in data.get('articles', []):
                article_data = {
                    'title': article.get('title', ''),
                    'content': self.clean_content(article.get('content', '') or article.get('description', '')),
                    'url': article.get('url', ''),
                    'author': article.get('author', ''),
                    'published_date': self.parse_date(article.get('publishedAt', '')),
                    'image_url': article.get('urlToImage', ''),
                }
                
                if article_data['title'] and article_data['url']:
                    articles.append(article_data)
            
            return articles
            
        except requests.RequestException as e:
            raise NewsSourceError(f"Failed to fetch from NewsAPI: {e}")
        except Exception as e:
            raise NewsSourceError(f"Failed to parse NewsAPI response: {e}")


class RedditFetcher(BaseNewsFetcher):
    """Fetcher for Reddit posts."""
    
    def fetch_articles(self) -> List[Dict[str, Any]]:
        """Fetch posts from Reddit."""
        try:
            # Extract subreddit from URL
            parsed_url = urlparse(self.source.url)
            if 'reddit.com' not in parsed_url.netloc:
                raise NewsSourceError("Invalid Reddit URL")
            
            # Convert to JSON API URL
            json_url = self.source.url.rstrip('/') + '.json'
            
            params = {
                'limit': min(self.source.max_articles_per_fetch, 100),
                'sort': 'hot',
            }
            
            response = self.session.get(json_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            articles = []
            posts = data.get('data', {}).get('children', [])
            
            for post in posts:
                post_data = post.get('data', {})
                
                # Skip stickied posts and ads
                if post_data.get('stickied') or post_data.get('is_sponsored'):
                    continue
                
                article_data = {
                    'title': post_data.get('title', ''),
                    'content': self.clean_content(post_data.get('selftext', '')),
                    'url': f"https://reddit.com{post_data.get('permalink', '')}",
                    'author': post_data.get('author', ''),
                    'published_date': datetime.fromtimestamp(post_data.get('created_utc', 0)),
                    'image_url': self._extract_reddit_image(post_data),
                    'social_shares': post_data.get('num_comments', 0),
                    'social_likes': post_data.get('ups', 0),
                }
                
                if article_data['title'] and article_data['url']:
                    articles.append(article_data)
            
            return articles
            
        except requests.RequestException as e:
            raise NewsSourceError(f"Failed to fetch from Reddit: {e}")
        except Exception as e:
            raise NewsSourceError(f"Failed to parse Reddit response: {e}")
    
    def _extract_reddit_image(self, post_data) -> str:
        """Extract image URL from Reddit post."""
        # Try preview images
        preview = post_data.get('preview', {})
        if preview and 'images' in preview:
            images = preview['images']
            if images and 'source' in images[0]:
                return images[0]['source'].get('url', '').replace('&amp;', '&')
        
        # Try thumbnail
        thumbnail = post_data.get('thumbnail', '')
        if thumbnail and thumbnail.startswith('http'):
            return thumbnail
        
        # Try URL if it's an image
        url = post_data.get('url', '')
        if url and any(url.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
            return url
        
        return ''


class NewsAggregatorService:
    """Main service for aggregating news from all sources."""
    
    FETCHER_CLASSES = {
        'rss': RSSFetcher,
        'api': NewsAPIFetcher,
        'reddit': RedditFetcher,
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def fetch_from_source(self, source: NewsSource) -> int:
        """Fetch articles from a single news source."""
        if not source.is_active:
            return 0
        
        fetcher_class = self.FETCHER_CLASSES.get(source.source_type)
        if not fetcher_class:
            self.logger.error(f"No fetcher available for source type: {source.source_type}")
            return 0
        
        try:
            fetcher = fetcher_class(source)
            articles_data = fetcher.fetch_articles()
            
            created_count = 0
            for article_data in articles_data:
                article, created = self._create_or_update_article(source, article_data)
                if created:
                    created_count += 1
            
            source.mark_fetch_attempt(success=True)
            self.logger.info(f"Fetched {created_count} new articles from {source.name}")
            return created_count
            
        except NewsSourceError as e:
            source.mark_fetch_attempt(success=False, error_message=str(e))
            self.logger.error(f"Failed to fetch from {source.name}: {e}")
            return 0
        except Exception as e:
            source.mark_fetch_attempt(success=False, error_message=str(e))
            self.logger.error(f"Unexpected error fetching from {source.name}: {e}")
            return 0
    
    def _create_or_update_article(self, source: NewsSource, article_data: Dict[str, Any]) -> tuple:
        """Create or update a news article."""
        # Check if article already exists
        existing = NewsArticle.objects.filter(url=article_data['url']).first()
        if existing:
            return existing, False
        
        # Create new article
        article = NewsArticle.objects.create(
            source=source,
            title=article_data['title'],
            content=article_data['content'],
            url=article_data['url'],
            author=article_data.get('author', ''),
            published_date=article_data.get('published_date') or timezone.now(),
            image_url=article_data.get('image_url', ''),
            social_shares=article_data.get('social_shares', 0),
            social_likes=article_data.get('social_likes', 0),
            social_comments=article_data.get('social_comments', 0),
        )
        
        # Add categories from source
        if source.categories.exists():
            article.categories.set(source.categories.all())
        
        return article, True
    
    def fetch_from_all_sources(self) -> Dict[str, int]:
        """Fetch articles from all active sources."""
        results = {}
        sources = NewsSource.objects.filter(is_active=True)
        
        for source in sources:
            # Check if it's time to fetch from this source
            if self._should_fetch_from_source(source):
                count = self.fetch_from_source(source)
                results[source.name] = count
        
        return results
    
    def _should_fetch_from_source(self, source: NewsSource) -> bool:
        """Check if it's time to fetch from a source based on its interval."""
        if not source.last_fetch:
            return True
        
        time_since_fetch = timezone.now() - source.last_fetch
        fetch_interval = timedelta(minutes=source.fetch_interval)
        
        return time_since_fetch >= fetch_interval
