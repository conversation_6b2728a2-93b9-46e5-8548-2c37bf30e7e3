#!/usr/bin/env python
"""
Demo script to showcase the News Aggregation System functionality
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'imagsta.settings')
django.setup()

from django.core.management import call_command
from django.utils import timezone
from datetime import timedelta
from images.models import NewsSource, NewsArticle, Category, AIHighlight
from images.news_services import NewsAggregatorService
from images.ai_services import ai_service


def print_header(title):
    """Print a formatted header."""
    print("\n" + "="*60)
    print(f"🔥 {title}")
    print("="*60)


def print_section(title):
    """Print a formatted section header."""
    print(f"\n📋 {title}")
    print("-" * 40)


def demo_database_status():
    """Show current database status."""
    print_section("DATABASE STATUS")
    
    # Count models
    sources_count = NewsSource.objects.count()
    articles_count = NewsArticle.objects.count()
    categories_count = Category.objects.count()
    highlights_count = AIHighlight.objects.count()
    
    print(f"📊 News Sources: {sources_count}")
    print(f"📰 Articles: {articles_count}")
    print(f"🏷️  Categories: {categories_count}")
    print(f"⭐ AI Highlights: {highlights_count}")
    
    # Show recent activity
    recent_articles = NewsArticle.objects.filter(
        created__gte=timezone.now() - timedelta(hours=24)
    ).count()
    
    recent_highlights = AIHighlight.objects.filter(
        created__gte=timezone.now() - timedelta(hours=24)
    ).count()
    
    print(f"📈 Articles (24h): {recent_articles}")
    print(f"✨ Highlights (24h): {recent_highlights}")


def demo_news_sources():
    """Show news sources status."""
    print_section("NEWS SOURCES STATUS")
    
    sources = NewsSource.objects.all()
    
    if not sources:
        print("❌ No news sources configured")
        print("💡 Run: python manage.py fetch_news --create-sample-sources")
        return
    
    for source in sources:
        status_icon = "✅" if source.is_active else "❌"
        health = source.get_health_status()
        health_icons = {
            'healthy': '💚',
            'stale': '🟡',
            'unhealthy': '🔴',
            'never_fetched': '⚪'
        }
        
        print(f"{status_icon} {source.name}")
        print(f"   Type: {source.source_type} | Health: {health_icons.get(health, '❓')} {health}")
        print(f"   Articles: {NewsArticle.objects.filter(source=source).count()} | Errors: {source.error_count}")
        
        if source.last_fetch:
            time_ago = timezone.now() - source.last_fetch
            hours_ago = int(time_ago.total_seconds() / 3600)
            print(f"   Last fetch: {hours_ago}h ago")
        else:
            print(f"   Last fetch: Never")


def demo_categories():
    """Show categories and their article counts."""
    print_section("CATEGORIES")
    
    categories = Category.objects.filter(is_active=True)
    
    for category in categories:
        article_count = NewsArticle.objects.filter(categories=category).count()
        recent_count = NewsArticle.objects.filter(
            categories=category,
            created__gte=timezone.now() - timedelta(hours=24)
        ).count()

        print(f"🏷️  {category.display_name}: {article_count} articles ({recent_count} recent)")


def demo_highlights():
    """Show top AI highlights."""
    print_section("TOP AI HIGHLIGHTS")
    
    highlights = AIHighlight.objects.select_related('article').order_by('-highlight_score')[:5]
    
    if not highlights:
        print("❌ No highlights found")
        print("💡 Run: python manage.py analyze_news --limit 20")
        return
    
    for i, highlight in enumerate(highlights, 1):
        article = highlight.article
        print(f"\n{i}. 🌟 Score: {highlight.highlight_score:.1f}")
        print(f"   📰 {article.title[:80]}...")
        print(f"   🔗 {article.source.name}")
        print(f"   📅 {article.published_date.strftime('%Y-%m-%d %H:%M')}")
        
        if highlight.highlight_reasons:
            print(f"   💡 Reasons: {', '.join(highlight.highlight_reasons[:2])}")
        
        if highlight.recommended_platforms:
            platforms = ', '.join(highlight.recommended_platforms)
            print(f"   📱 Platforms: {platforms}")


def demo_ai_analysis():
    """Demonstrate AI analysis on a sample article."""
    print_section("AI ANALYSIS DEMO")
    
    # Get a recent article
    article = NewsArticle.objects.filter(is_processed=True).first()
    
    if not article:
        print("❌ No processed articles found")
        print("💡 Run: python manage.py analyze_news --limit 5")
        return
    
    print(f"📰 Analyzing: {article.title[:60]}...")
    print(f"🔗 Source: {article.source.name}")
    
    # Show AI analysis results
    print(f"\n🤖 AI ANALYSIS RESULTS:")
    print(f"   😊 Sentiment: {article.sentiment_score:.2f} (-1 to 1)")
    print(f"   📈 Engagement: {article.engagement_prediction:.1f}/100")
    print(f"   🔥 Trending: {article.trending_score:.1f}/100")
    
    if article.keywords:
        keywords = ', '.join(article.keywords[:5])
        print(f"   🔑 Keywords: {keywords}")
    
    if article.topics:
        topics = ', '.join(article.topics)
        print(f"   📂 Topics: {topics}")
    
    # Show highlight status
    if hasattr(article, 'highlight'):
        highlight = article.highlight
        print(f"\n⭐ HIGHLIGHT STATUS:")
        print(f"   Score: {highlight.highlight_score:.1f}")
        print(f"   Posted: {'Yes' if highlight.was_posted else 'No'}")


def demo_management_commands():
    """Show available management commands."""
    print_section("MANAGEMENT COMMANDS")
    
    commands = [
        ("fetch_news", "Fetch news from all configured sources"),
        ("analyze_news", "Analyze articles with AI and create highlights"),
        ("auto_post_news", "Automatically post highlighted articles"),
        ("news_pipeline", "Run complete pipeline (fetch + analyze + post)"),
    ]
    
    for command, description in commands:
        print(f"📝 python manage.py {command}")
        print(f"   {description}")


def demo_web_interface():
    """Show web interface URLs."""
    print_section("WEB INTERFACE")
    
    urls = [
        ("/news-dashboard/", "Main news dashboard with highlights"),
        ("/news-highlights/", "Browse and manage AI highlights"),
        ("/news-sources/", "Manage news sources"),
        ("/account-categories/", "Configure account-category mappings"),
        ("/admin/", "Django admin panel"),
    ]
    
    print("🌐 Available URLs (when server is running):")
    for url, description in urls:
        print(f"   http://localhost:8000{url}")
        print(f"   {description}")


def run_quick_demo():
    """Run a quick demonstration of the system."""
    print_header("IMAGSTA NEWS AGGREGATION SYSTEM DEMO")
    
    try:
        demo_database_status()
        demo_news_sources()
        demo_categories()
        demo_highlights()
        demo_ai_analysis()
        demo_management_commands()
        demo_web_interface()
        
        print_header("DEMO COMPLETED")
        print("🎉 The News Aggregation System is working properly!")
        print("\n💡 NEXT STEPS:")
        print("1. Start the server: python manage.py runserver")
        print("2. Visit: http://localhost:8000/news-dashboard/")
        print("3. Set up account categories: http://localhost:8000/account-categories/")
        print("4. Run the pipeline: python manage.py news_pipeline")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        print("💡 Make sure the database is set up and migrations are applied")
        return False
    
    return True


if __name__ == "__main__":
    success = run_quick_demo()
    sys.exit(0 if success else 1)
