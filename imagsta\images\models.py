from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import URLValidator, MinLengthValidator
from django.utils import timezone
import uuid
import json


class User(AbstractUser):
    """Extended user model with additional fields for social media management."""
    pass


class Search(models.Model):
    """Model to store search queries and track search history."""
    query = models.CharField(
        max_length=128,
        unique=True,
        validators=[MinLengthValidator(2)],
        help_text="Search query for images"
    )
    search_count = models.PositiveIntegerField(default=1, help_text="Number of times this query was searched")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated']
        indexes = [
            models.Index(fields=['query']),
            models.Index(fields=['-updated']),
        ]

    def __str__(self):
        return f"{self.query} ({self.search_count} searches)"

    def increment_search_count(self):
        """Increment the search count and update timestamp."""
        self.search_count += 1
        self.updated = timezone.now()
        self.save(update_fields=['search_count', 'updated'])


class Result(models.Model):
    """Model to store image search results from external APIs."""
    query = models.ForeignKey(Search, on_delete=models.CASCADE, related_name='results')
    image = models.URLField()
    dis_image = models.URLField(blank=True)
    image_alt = models.CharField(max_length=255, blank=True, help_text="Alt text for the image")
    source = models.CharField(max_length=50, blank=True, help_text="Source of the image (e.g., unsplash, pixabay)")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.image


class Post(models.Model):
    """Model to store user-created posts for social media."""
    user = models.ForeignKey(User, on_delete=models.PROTECT, related_name='posts')
    image = models.ImageField(upload_to='images/posts')
    caption = models.TextField(blank=True)
    post_text = models.TextField(max_length=130, blank=True)
    hashtags = models.TextField(blank=True)
    posted = models.BooleanField(default=False)
    published_image_id = models.CharField(blank=True, max_length=200)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.pk} - {self.user}"


class PostAnalytics(models.Model):
    """Model to track post performance analytics."""
    post = models.OneToOneField(Post, on_delete=models.CASCADE, related_name='analytics')
    likes_count = models.PositiveIntegerField(default=0)
    comments_count = models.PositiveIntegerField(default=0)
    shares_count = models.PositiveIntegerField(default=0)
    saves_count = models.PositiveIntegerField(default=0)
    reach = models.PositiveIntegerField(default=0)
    impressions = models.PositiveIntegerField(default=0)
    engagement_rate = models.FloatField(default=0.0)
    click_through_rate = models.FloatField(default=0.0)

    # Platform-specific metrics
    platform_data = models.JSONField(default=dict, blank=True)

    # Tracking timestamps
    last_updated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Post Analytics"
        indexes = [
            models.Index(fields=['post', '-last_updated']),
            models.Index(fields=['engagement_rate']),
        ]

    def __str__(self):
        return f"Analytics for {self.post}"

    @property
    def total_engagement(self):
        """Calculate total engagement."""
        return self.likes_count + self.comments_count + self.shares_count + self.saves_count

    def update_engagement_rate(self):
        """Calculate and update engagement rate."""
        if self.impressions > 0:
            self.engagement_rate = (self.total_engagement / self.impressions) * 100
        else:
            self.engagement_rate = 0.0
        self.save(update_fields=['engagement_rate'])

    def get_performance_score(self):
        """Calculate overall performance score (0-100)."""
        # Weighted scoring based on different metrics
        engagement_weight = 0.4
        reach_weight = 0.3
        ctr_weight = 0.3

        # Normalize metrics (assuming benchmarks)
        engagement_score = min(self.engagement_rate * 10, 100)  # 10% engagement = 100 score
        reach_score = min((self.reach / max(self.impressions, 1)) * 100, 100)
        ctr_score = min(self.click_through_rate * 20, 100)  # 5% CTR = 100 score

        return (engagement_score * engagement_weight +
                reach_score * reach_weight +
                ctr_score * ctr_weight)


class UserAnalytics(models.Model):
    """Model to track user-level analytics and insights."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='analytics')

    # Aggregate metrics
    total_posts = models.PositiveIntegerField(default=0)
    total_likes = models.PositiveIntegerField(default=0)
    total_comments = models.PositiveIntegerField(default=0)
    total_shares = models.PositiveIntegerField(default=0)
    total_followers = models.PositiveIntegerField(default=0)
    total_following = models.PositiveIntegerField(default=0)

    # Calculated metrics
    avg_engagement_rate = models.FloatField(default=0.0)
    avg_likes_per_post = models.FloatField(default=0.0)
    avg_comments_per_post = models.FloatField(default=0.0)

    # Optimal timing data
    best_posting_hour = models.PositiveIntegerField(default=12)  # 0-23
    best_posting_day = models.PositiveIntegerField(default=1)   # 1-7 (Monday-Sunday)

    # Growth metrics
    follower_growth_rate = models.FloatField(default=0.0)  # Monthly growth rate
    engagement_growth_rate = models.FloatField(default=0.0)

    # Content insights
    top_performing_hashtags = models.JSONField(default=list, blank=True)
    content_type_performance = models.JSONField(default=dict, blank=True)

    # Timestamps
    last_calculated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "User Analytics"

    def __str__(self):
        return f"Analytics for {self.user.username}"

    def update_totals(self):
        """Update total counts from post analytics."""
        post_analytics = PostAnalytics.objects.filter(post__user=self.user)

        self.total_posts = self.user.posts.count()
        self.total_likes = sum(pa.likes_count for pa in post_analytics)
        self.total_comments = sum(pa.comments_count for pa in post_analytics)
        self.total_shares = sum(pa.shares_count for pa in post_analytics)

        # Calculate averages
        if self.total_posts > 0:
            self.avg_likes_per_post = self.total_likes / self.total_posts
            self.avg_comments_per_post = self.total_comments / self.total_posts

        # Calculate average engagement rate
        if post_analytics.exists():
            self.avg_engagement_rate = sum(pa.engagement_rate for pa in post_analytics) / post_analytics.count()
        else:
            self.avg_engagement_rate = 0.0

        self.save()

    def get_top_posts(self, limit=5):
        """Get top performing posts."""
        return PostAnalytics.objects.filter(
            post__user=self.user
        ).order_by('-engagement_rate')[:limit]

    def get_engagement_trend(self, days=30):
        """Get engagement trend over specified days."""
        from datetime import datetime, timedelta

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        analytics = PostAnalytics.objects.filter(
            post__user=self.user,
            post__created__range=[start_date, end_date]
        ).order_by('post__created')

        return [
            {
                'date': a.post.created.strftime('%Y-%m-%d'),
                'engagement_rate': a.engagement_rate,
                'total_engagement': a.total_engagement
            }
            for a in analytics
        ]


class ContentTemplate(models.Model):
    """Model for reusable content templates."""
    TEMPLATE_TYPES = [
        ('caption', 'Caption Template'),
        ('hashtag_set', 'Hashtag Set'),
        ('post_series', 'Post Series'),
        ('campaign', 'Campaign Template'),
    ]

    INDUSTRIES = [
        ('technology', 'Technology'),
        ('fashion', 'Fashion'),
        ('food', 'Food & Beverage'),
        ('travel', 'Travel'),
        ('fitness', 'Fitness & Health'),
        ('business', 'Business'),
        ('lifestyle', 'Lifestyle'),
        ('education', 'Education'),
        ('entertainment', 'Entertainment'),
        ('other', 'Other'),
    ]

    TONES = [
        ('professional', 'Professional'),
        ('casual', 'Casual'),
        ('humorous', 'Humorous'),
        ('inspirational', 'Inspirational'),
        ('educational', 'Educational'),
        ('promotional', 'Promotional'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='templates')
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    content = models.TextField(help_text="Template content with placeholders like {product_name}")
    industry = models.CharField(max_length=50, choices=INDUSTRIES, blank=True)
    tone = models.CharField(max_length=20, choices=TONES, blank=True)
    hashtags = models.TextField(blank=True, help_text="Default hashtags for this template")

    # Template metadata
    variables = models.JSONField(default=list, blank=True, help_text="List of variables used in template")
    usage_count = models.PositiveIntegerField(default=0)
    is_public = models.BooleanField(default=False, help_text="Make template available to other users")
    is_featured = models.BooleanField(default=False, help_text="Featured template")

    # Performance tracking
    avg_engagement_rate = models.FloatField(default=0.0)
    total_uses = models.PositiveIntegerField(default=0)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usage_count', '-created']
        indexes = [
            models.Index(fields=['user', 'template_type']),
            models.Index(fields=['industry', 'tone']),
            models.Index(fields=['is_public', 'is_featured']),
        ]

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.total_uses += 1
        self.save(update_fields=['usage_count', 'total_uses'])

    def render_content(self, variables=None):
        """Render template content with provided variables."""
        if not variables:
            return self.content

        try:
            return self.content.format(**variables)
        except KeyError as e:
            return f"Missing variable: {e}"

    def extract_variables(self):
        """Extract variable names from template content."""
        import re
        variables = re.findall(r'\{(\w+)\}', self.content)
        return list(set(variables))

    def save(self, *args, **kwargs):
        """Override save to auto-extract variables."""
        self.variables = self.extract_variables()
        super().save(*args, **kwargs)


class SavedHashtagSet(models.Model):
    """Model for saving hashtag sets for reuse."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='hashtag_sets')
    name = models.CharField(max_length=100)
    hashtags = models.TextField(help_text="Comma-separated hashtags")
    industry = models.CharField(max_length=50, blank=True)
    description = models.TextField(blank=True)

    # Performance metrics
    usage_count = models.PositiveIntegerField(default=0)
    avg_engagement_rate = models.FloatField(default=0.0)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usage_count', '-created']
        indexes = [
            models.Index(fields=['user', 'industry']),
        ]

    def __str__(self):
        return self.name

    def get_hashtags_list(self):
        """Return hashtags as a list."""
        return [tag.strip() for tag in self.hashtags.split(',') if tag.strip()]

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

    @property
    def hashtag_count(self):
        """Return number of hashtags in this set."""
        return len(self.get_hashtags_list())


class ScheduledPost(models.Model):
    """Model for scheduling posts for future publication."""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('posted', 'Posted'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scheduled_posts')
    post = models.OneToOneField(Post, on_delete=models.CASCADE, related_name='schedule')

    # Scheduling details
    scheduled_for = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Platform targeting
    target_platforms = models.JSONField(default=list, help_text="List of platforms to post to")

    # Retry logic
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)
    last_attempt = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['scheduled_for']
        indexes = [
            models.Index(fields=['user', 'status', 'scheduled_for']),
            models.Index(fields=['status', 'scheduled_for']),
        ]

    def __str__(self):
        return f"Scheduled post for {self.scheduled_for}"

    def can_retry(self):
        """Check if post can be retried."""
        return self.status == 'failed' and self.retry_count < self.max_retries

    def mark_as_processing(self):
        """Mark post as being processed."""
        self.status = 'processing'
        self.save(update_fields=['status'])

    def mark_as_posted(self):
        """Mark post as successfully posted."""
        self.status = 'posted'
        self.save(update_fields=['status'])

    def mark_as_failed(self, error_message=''):
        """Mark post as failed with optional error message."""
        self.status = 'failed'
        self.retry_count += 1
        self.error_message = error_message
        self.last_attempt = timezone.now()
        self.save(update_fields=['status', 'retry_count', 'error_message', 'last_attempt'])


class SocialMediaAccount(models.Model):
    """Model for storing connected social media accounts."""
    PLATFORM_CHOICES = [
        ('instagram', 'Instagram'),
        ('twitter', 'Twitter'),
        ('linkedin', 'LinkedIn'),
        ('facebook', 'Facebook'),
        ('tiktok', 'TikTok'),
        ('youtube', 'YouTube'),
    ]

    STATUS_CHOICES = [
        ('connected', 'Connected'),
        ('disconnected', 'Disconnected'),
        ('expired', 'Token Expired'),
        ('error', 'Error'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='social_accounts')
    platform = models.CharField(max_length=20, choices=PLATFORM_CHOICES)
    username = models.CharField(max_length=100)
    display_name = models.CharField(max_length=100, blank=True)
    profile_picture_url = models.URLField(blank=True)

    # OAuth tokens and credentials
    access_token = models.TextField(blank=True)
    refresh_token = models.TextField(blank=True)
    token_expires_at = models.DateTimeField(null=True, blank=True)

    # Platform-specific data
    platform_user_id = models.CharField(max_length=100, blank=True)
    platform_data = models.JSONField(default=dict, blank=True)

    # Account status and metrics
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='connected')
    follower_count = models.PositiveIntegerField(default=0)
    following_count = models.PositiveIntegerField(default=0)
    posts_count = models.PositiveIntegerField(default=0)

    # Posting permissions
    can_post = models.BooleanField(default=True)
    can_read = models.BooleanField(default=True)

    # Timestamps
    connected_at = models.DateTimeField(auto_now_add=True)
    last_sync = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'platform', 'platform_user_id']
        ordering = ['-connected_at']
        indexes = [
            models.Index(fields=['user', 'platform']),
            models.Index(fields=['status', 'platform']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.platform} (@{self.username})"

    def is_token_valid(self):
        """Check if the access token is still valid."""
        if not self.token_expires_at:
            return True  # No expiration set
        return timezone.now() < self.token_expires_at

    def needs_refresh(self):
        """Check if token needs to be refreshed soon."""
        if not self.token_expires_at:
            return False
        # Refresh if expires within 1 hour
        return timezone.now() + timezone.timedelta(hours=1) >= self.token_expires_at

    def mark_as_expired(self):
        """Mark account as having expired token."""
        self.status = 'expired'
        self.save(update_fields=['status'])

    def mark_as_error(self, error_message=''):
        """Mark account as having an error."""
        self.status = 'error'
        if error_message:
            self.platform_data['last_error'] = error_message
        self.save(update_fields=['status', 'platform_data'])

    def update_metrics(self, follower_count=None, following_count=None, posts_count=None):
        """Update account metrics."""
        if follower_count is not None:
            self.follower_count = follower_count
        if following_count is not None:
            self.following_count = following_count
        if posts_count is not None:
            self.posts_count = posts_count
        self.last_sync = timezone.now()
        self.save(update_fields=['follower_count', 'following_count', 'posts_count', 'last_sync'])

    def get_platform_icon(self):
        """Get Bootstrap icon class for platform."""
        icons = {
            'instagram': 'bi-instagram',
            'twitter': 'bi-twitter',
            'linkedin': 'bi-linkedin',
            'facebook': 'bi-facebook',
            'tiktok': 'bi-tiktok',
            'youtube': 'bi-youtube',
        }
        return icons.get(self.platform, 'bi-share')

    def get_platform_color(self):
        """Get color class for platform."""
        colors = {
            'instagram': 'text-danger',
            'twitter': 'text-info',
            'linkedin': 'text-primary',
            'facebook': 'text-primary',
            'tiktok': 'text-dark',
            'youtube': 'text-danger',
        }
        return colors.get(self.platform, 'text-secondary')


class PostPublication(models.Model):
    """Model to track posts published to social media platforms."""
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='publications')
    account = models.ForeignKey(SocialMediaAccount, on_delete=models.CASCADE, related_name='publications')

    # Platform-specific post data
    platform_post_id = models.CharField(max_length=100, blank=True)
    platform_url = models.URLField(blank=True)

    # Publication status
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('published', 'Published'),
        ('failed', 'Failed'),
        ('deleted', 'Deleted'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Metrics from platform
    likes_count = models.PositiveIntegerField(default=0)
    comments_count = models.PositiveIntegerField(default=0)
    shares_count = models.PositiveIntegerField(default=0)
    views_count = models.PositiveIntegerField(default=0)

    # Error handling
    error_message = models.TextField(blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)

    # Timestamps
    published_at = models.DateTimeField(null=True, blank=True)
    last_sync = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['post', 'account']
        ordering = ['-published_at', '-created']
        indexes = [
            models.Index(fields=['account', 'status']),
            models.Index(fields=['post', 'status']),
        ]

    def __str__(self):
        return f"{self.post} -> {self.account.platform} ({self.status})"

    def mark_as_published(self, platform_post_id='', platform_url=''):
        """Mark publication as successful."""
        self.status = 'published'
        self.published_at = timezone.now()
        if platform_post_id:
            self.platform_post_id = platform_post_id
        if platform_url:
            self.platform_url = platform_url
        self.save(update_fields=['status', 'published_at', 'platform_post_id', 'platform_url'])

    def mark_as_failed(self, error_message=''):
        """Mark publication as failed."""
        self.status = 'failed'
        self.error_message = error_message
        self.retry_count += 1
        self.save(update_fields=['status', 'error_message', 'retry_count'])

    def can_retry(self):
        """Check if publication can be retried."""
        return self.status == 'failed' and self.retry_count < self.max_retries





# News Aggregation Models

class Category(models.Model):
    """Model for categorizing news articles and social media accounts."""
    CATEGORY_TYPES = [
        ('technology', 'Technology'),
        ('business', 'Business'),
        ('entertainment', 'Entertainment'),
        ('sports', 'Sports'),
        ('politics', 'Politics'),
        ('health', 'Health'),
        ('science', 'Science'),
        ('lifestyle', 'Lifestyle'),
        ('travel', 'Travel'),
        ('food', 'Food & Beverage'),
        ('fashion', 'Fashion'),
        ('gaming', 'Gaming'),
        ('education', 'Education'),
        ('finance', 'Finance'),
        ('automotive', 'Automotive'),
        ('real_estate', 'Real Estate'),
        ('environment', 'Environment'),
        ('other', 'Other'),
    ]

    name = models.CharField(max_length=50, choices=CATEGORY_TYPES, unique=True)
    display_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    color = models.CharField(max_length=7, default='#007bff', help_text="Hex color code for UI display")
    icon = models.CharField(max_length=50, default='bi-tag', help_text="Bootstrap icon class")

    # AI preferences for this category
    keywords = models.JSONField(default=list, blank=True, help_text="Keywords for auto-categorization")
    priority_score = models.FloatField(default=1.0, help_text="Priority multiplier for AI highlighting")

    is_active = models.BooleanField(default=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['display_name']

    def __str__(self):
        return self.display_name


class NewsSource(models.Model):
    """Model for managing news sources and their configurations."""
    SOURCE_TYPES = [
        ('rss', 'RSS Feed'),
        ('api', 'API Integration'),
        ('scraper', 'Web Scraper'),
        ('manual', 'Manual Entry'),
    ]

    name = models.CharField(max_length=100)
    source_type = models.CharField(max_length=20, choices=SOURCE_TYPES)
    url = models.URLField(help_text="RSS feed URL or API endpoint")

    # Configuration
    categories = models.ManyToManyField(Category, blank=True, help_text="Categories this source covers")
    fetch_interval = models.PositiveIntegerField(default=60, help_text="Fetch interval in minutes")
    max_articles_per_fetch = models.PositiveIntegerField(default=50)

    # API configuration (stored as JSON)
    api_config = models.JSONField(default=dict, blank=True, help_text="API keys, headers, parameters")

    # Parsing configuration
    title_selector = models.CharField(max_length=200, blank=True, help_text="CSS selector for title")
    content_selector = models.CharField(max_length=200, blank=True, help_text="CSS selector for content")
    image_selector = models.CharField(max_length=200, blank=True, help_text="CSS selector for image")

    # Status and metrics
    is_active = models.BooleanField(default=True)
    last_fetch = models.DateTimeField(null=True, blank=True)
    last_success = models.DateTimeField(null=True, blank=True)
    fetch_count = models.PositiveIntegerField(default=0)
    error_count = models.PositiveIntegerField(default=0)
    last_error = models.TextField(blank=True)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['source_type', 'is_active']),
            models.Index(fields=['last_fetch']),
        ]

    def __str__(self):
        return f"{self.name} ({self.source_type})"

    def mark_fetch_attempt(self, success=True, error_message=''):
        """Mark a fetch attempt and update statistics."""
        self.last_fetch = timezone.now()
        self.fetch_count += 1

        if success:
            self.last_success = timezone.now()
            self.last_error = ''
        else:
            self.error_count += 1
            self.last_error = error_message

        self.save(update_fields=['last_fetch', 'fetch_count', 'last_success', 'error_count', 'last_error'])

    def get_health_status(self):
        """Get health status of the news source."""
        if not self.last_fetch:
            return 'never_fetched'

        if self.error_count > 5:
            return 'unhealthy'

        # Check if last fetch was recent
        time_since_fetch = timezone.now() - self.last_fetch
        expected_interval = timezone.timedelta(minutes=self.fetch_interval * 2)

        if time_since_fetch > expected_interval:
            return 'stale'

        return 'healthy'


class NewsArticle(models.Model):
    """Model for storing news articles from various sources."""
    source = models.ForeignKey(NewsSource, on_delete=models.CASCADE, related_name='articles')
    categories = models.ManyToManyField(Category, blank=True)

    # Article content
    title = models.CharField(max_length=500)
    content = models.TextField()
    summary = models.TextField(blank=True, help_text="AI-generated summary")
    url = models.URLField(unique=True)
    image_url = models.URLField(blank=True)
    author = models.CharField(max_length=200, blank=True)

    # Metadata
    published_date = models.DateTimeField()
    fetched_date = models.DateTimeField(auto_now_add=True)

    # AI analysis results
    sentiment_score = models.FloatField(null=True, blank=True, help_text="Sentiment analysis score (-1 to 1)")
    engagement_prediction = models.FloatField(null=True, blank=True, help_text="Predicted engagement score (0-100)")
    trending_score = models.FloatField(null=True, blank=True, help_text="Trending potential score (0-100)")

    # Content analysis
    keywords = models.JSONField(default=list, blank=True)
    entities = models.JSONField(default=list, blank=True, help_text="Named entities extracted from content")
    topics = models.JSONField(default=list, blank=True, help_text="Topic classification results")

    # Social media metrics (if available)
    social_shares = models.PositiveIntegerField(default=0)
    social_comments = models.PositiveIntegerField(default=0)
    social_likes = models.PositiveIntegerField(default=0)

    # Processing status
    is_processed = models.BooleanField(default=False)
    is_highlighted = models.BooleanField(default=False)
    processing_error = models.TextField(blank=True)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-published_date']
        indexes = [
            models.Index(fields=['source', '-published_date']),
            models.Index(fields=['is_highlighted', '-engagement_prediction']),
            models.Index(fields=['is_processed']),
            models.Index(fields=['-trending_score']),
        ]

    def __str__(self):
        return self.title[:100]

    def get_engagement_score(self):
        """Calculate overall engagement score."""
        base_score = self.engagement_prediction or 0

        # Boost score based on social metrics
        social_boost = min((self.social_shares + self.social_likes + self.social_comments) / 100, 20)

        # Boost score based on trending
        trending_boost = (self.trending_score or 0) * 0.2

        return min(base_score + social_boost + trending_boost, 100)

    def should_highlight(self, threshold=70):
        """Determine if article should be highlighted."""
        return self.get_engagement_score() >= threshold

    def get_category_names(self):
        """Get list of category names."""
        return [cat.display_name for cat in self.categories.all()]


class AIHighlight(models.Model):
    """Model for tracking AI-highlighted articles and their reasoning."""
    article = models.OneToOneField(NewsArticle, on_delete=models.CASCADE, related_name='highlight')

    # Highlight reasoning
    highlight_score = models.FloatField(help_text="Overall highlight score (0-100)")
    highlight_reasons = models.JSONField(default=list, help_text="List of reasons for highlighting")

    # Recommended actions
    recommended_platforms = models.JSONField(default=list, help_text="Recommended social media platforms")
    suggested_posting_time = models.DateTimeField(null=True, blank=True)
    suggested_hashtags = models.JSONField(default=list, blank=True)
    suggested_caption = models.TextField(blank=True)

    # Performance tracking
    was_posted = models.BooleanField(default=False)
    actual_engagement = models.FloatField(null=True, blank=True, help_text="Actual engagement if posted")
    prediction_accuracy = models.FloatField(null=True, blank=True, help_text="How accurate was the prediction")

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-highlight_score', '-created']
        indexes = [
            models.Index(fields=['-highlight_score']),
            models.Index(fields=['was_posted']),
        ]

    def __str__(self):
        return f"Highlight: {self.article.title[:50]} (Score: {self.highlight_score})"

    def mark_as_posted(self, engagement_score=None):
        """Mark highlight as posted and optionally record engagement."""
        self.was_posted = True
        if engagement_score is not None:
            self.actual_engagement = engagement_score
            # Calculate prediction accuracy
            predicted = self.highlight_score
            actual = engagement_score
            self.prediction_accuracy = 100 - abs(predicted - actual)

        self.save(update_fields=['was_posted', 'actual_engagement', 'prediction_accuracy'])


class AccountCategoryMapping(models.Model):
    """Model for mapping social media accounts to categories."""
    account = models.ForeignKey(SocialMediaAccount, on_delete=models.CASCADE, related_name='category_mappings')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='account_mappings')

    # Mapping preferences
    priority = models.PositiveIntegerField(default=1, help_text="Priority level (1=highest)")
    auto_post = models.BooleanField(default=False, help_text="Automatically post highlighted articles")
    min_highlight_score = models.FloatField(default=70, help_text="Minimum score for auto-posting")

    # Posting preferences
    posting_schedule = models.JSONField(default=dict, blank=True, help_text="Preferred posting times")
    custom_hashtags = models.TextField(blank=True, help_text="Additional hashtags for this category")
    caption_template = models.TextField(blank=True, help_text="Custom caption template")

    # Statistics
    articles_posted = models.PositiveIntegerField(default=0)
    avg_engagement = models.FloatField(default=0.0)
    last_posted = models.DateTimeField(null=True, blank=True)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['account', 'category']
        ordering = ['account', 'priority']
        indexes = [
            models.Index(fields=['account', 'auto_post']),
            models.Index(fields=['category', 'priority']),
        ]

    def __str__(self):
        return f"{self.account} -> {self.category}"

    def increment_posted_count(self, engagement_score=None):
        """Increment posted count and update average engagement."""
        self.articles_posted += 1
        self.last_posted = timezone.now()

        if engagement_score is not None:
            # Update rolling average
            current_avg = self.avg_engagement
            count = self.articles_posted
            self.avg_engagement = ((current_avg * (count - 1)) + engagement_score) / count

        self.save(update_fields=['articles_posted', 'last_posted', 'avg_engagement'])

    def should_auto_post(self, article):
        """Check if article should be auto-posted to this account."""
        if not self.auto_post:
            return False

        if not hasattr(article, 'highlight'):
            return False

        return article.highlight.highlight_score >= self.min_highlight_score


class NewsFeedPreference(models.Model):
    """Model for user preferences regarding news feeds."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='news_preferences')

    # General preferences
    preferred_categories = models.ManyToManyField(Category, blank=True)
    max_articles_per_day = models.PositiveIntegerField(default=100)
    min_highlight_score = models.FloatField(default=60)

    # AI preferences
    enable_auto_highlighting = models.BooleanField(default=True)
    enable_auto_posting = models.BooleanField(default=False)
    enable_sentiment_filter = models.BooleanField(default=True)
    sentiment_threshold = models.FloatField(default=0.0, help_text="Minimum sentiment score (-1 to 1)")

    # Notification preferences
    notify_highlights = models.BooleanField(default=True)
    notify_auto_posts = models.BooleanField(default=True)
    notification_frequency = models.CharField(
        max_length=20,
        choices=[
            ('immediate', 'Immediate'),
            ('hourly', 'Hourly'),
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
        ],
        default='daily'
    )

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "News Feed Preference"
        verbose_name_plural = "News Feed Preferences"

    def __str__(self):
        return f"News preferences for {self.user.username}"

