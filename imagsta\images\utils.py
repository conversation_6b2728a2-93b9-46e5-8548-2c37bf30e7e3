import json
import os
import textwrap
import time
import urllib
import urllib.request
import requests
import logging
from PIL import Image, ImageDraw, ImageFont
from bs4 import BeautifulSoup
try:
    from googleapiclient.discovery import build
except ImportError:
    build = None
try:
    from selenium import webdriver
    from selenium.webdriver.firefox.options import Options
except ImportError:
    webdriver = None
    Options = None
# from instabot import Bot  # Commented out due to potential issues
from django.templatetags.static import static
from django.conf import settings
import pyimgur
from decouple import config

# Set up logging
logger = logging.getLogger(__name__)



def download(urls, download_dir=None):
    """Download images from URLs to a specified directory."""
    if download_dir is None:
        download_dir = os.path.join(settings.MEDIA_ROOT, "downloaded")

    if not os.path.exists(download_dir):
        os.makedirs(download_dir)

    # Clean existing files
    for f in os.listdir(download_dir):
        try:
            os.remove(os.path.join(download_dir, f))
        except OSError as e:
            logger.error(f"Error removing file {f}: {e}")

    pic_num = 1

    for url in urls:
        try:
            logger.info(f'Downloading => pic {pic_num}, {url}')
            file_path = os.path.join(download_dir, f'{pic_num}.PNG')
            urllib.request.urlretrieve(url, file_path)
            pic_num += 1
            time.sleep(1.5)

        except TypeError as e:
            logger.error(f"Image was not compatible to be downloaded: {e}")
        except urllib.error.HTTPError as e:
            logger.error(f"HTTP error downloading image: {e}")
        except Exception as e:
            logger.error(f"Unexpected error downloading image: {e}")


# Add the post's logo, text and shadow
def make_post(image, text):
    """Create a social media post by overlaying text on an image."""
    try:
        width, height = (1080, 1080)
        font_size = 54
        image = crop_max_square(image).resize((width, width), Image.LANCZOS)

        # Use Django's static file handling
        fade_path = os.path.join(settings.BASE_DIR, 'static', 'images', 'fade.png')
        rec_path = os.path.join(settings.BASE_DIR, 'static', 'images', 'V1.png')

        if os.path.exists(fade_path) and os.path.exists(rec_path):
            fade = Image.open(fade_path)
            rec = Image.open(rec_path)
            image.paste(fade, fade)
            image.paste(rec, rec)
        else:
            logger.warning("Overlay images not found, proceeding without them")

        # Adjust font size based on text length
        height = 880
        if len(text) > 37:
            height = 820
            font_size = 73
        if len(text) > 74:
            height = 810
            font_size = 71
        if len(text) >= 111:
            height = 790
            font_size = 70
        if len(text) >= 115:
            height = 785
            font_size = 65

        # Use Django's static file handling for fonts
        font_path = os.path.join(settings.BASE_DIR, 'static', 'fonts', 'Dongle-Bold.ttf')
        if os.path.exists(font_path):
            font = ImageFont.truetype(font_path, font_size)
        else:
            logger.warning("Font file not found, using default font")
            font = ImageFont.load_default()

        draw_multiple_line_text(image, text, font, height)
        return image

    except Exception as e:
        logger.error(f"Error creating post: {e}")
        return image  # Return original image if processing fails





# draws multiple lines text into an image
def draw_multiple_line_text(image, text, font, text_start_height):
    """Draw multi-line text on an image with proper formatting."""
    try:
        draw = ImageDraw.Draw(image)
        image_width, image_height = image.size
        y_text = text_start_height
        lines = textwrap.wrap(text, width=38)

        for line in lines:
            # Use textbbox for newer Pillow versions, fallback to textsize
            try:
                bbox = draw.textbbox((0, 0), line, font=font)
                line_width = bbox[2] - bbox[0]
                line_height = bbox[3] - bbox[1]
            except AttributeError:
                # Fallback for older Pillow versions
                line_width, line_height = font.getsize(line)

            draw.text(((image_width - line_width) / 2, y_text),
                      line, font=font, fill="white", stroke_fill="black", stroke_width=4)
            y_text += line_height - 12

    except Exception as e:
        logger.error(f"Error drawing text on image: {e}")


# crops the biggest square in an image
def crop_max_square(pil_img):
    return crop_square(pil_img, min(pil_img.size), min(pil_img.size))


# crops a square from the center of an image
def crop_square(pil_img, width, height):
    img_width, img_height = pil_img.size
    return pil_img.crop(((img_width - width) // 2,
                         (img_height - height) // 2,
                         (img_width + width) // 2,
                         (img_height + height) // 2))


# EAAibYPEaHzEBABImE2uuqdLudhCVkkWGMWAnX5n8XWc250LPZB6HOC97PqTQgRcLXcOnx9FZAWh35GCZAGGbdpGHWf10MSZA3lPDe94xZAlZAPEM2U8OZAcrxtZATzJFwnIl1pCKFxhRF1paPJlTKCjHpRH19tZBqZAwFZCHpVBveZA7XU6vDd1lF6W1PrbH1aZAb6BVDigML9d0NFXM613aA4qVd


def image_to_url(image_path):
    """Upload image to Imgur and return the URL."""
    try:
        client_id = config('IMGUR_CLIENT_ID', default='')
        if not client_id:
            logger.error("Imgur client ID not configured")
            return None

        # Handle relative paths
        if not os.path.isabs(image_path):
            full_path = os.path.join(settings.BASE_DIR, image_path.lstrip('/'))
        else:
            full_path = image_path

        if not os.path.exists(full_path):
            logger.error(f"Image file not found: {full_path}")
            return None

        im = pyimgur.Imgur(client_id)
        uploaded_image = im.upload_image(full_path)
        return uploaded_image.link

    except Exception as e:
        logger.error(f"Error uploading image to Imgur: {e}")
        return None


def getCreds():
    """Get credentials required for Instagram API calls.

    Returns:
        dict: credentials needed for Instagram API

    """
    try:
        access_token = config('FACEBOOK_ACCESS_TOKEN', default='')
        instagram_account_id = config('INSTAGRAM_ACCOUNT_ID', default='')
        graph_version = config('FACEBOOK_GRAPH_VERSION', default='v18.0')

        if not access_token or not instagram_account_id:
            logger.error("Facebook/Instagram credentials not configured")
            return None

        creds = {
            'access_token': access_token,
            'graph_domain': 'https://graph.facebook.com/',
            'graph_version': graph_version,
            'instagram_account_id': instagram_account_id
        }
        creds['endpoint_base'] = f"{creds['graph_domain']}{creds['graph_version']}/"

        return creds

    except Exception as e:
        logger.error(f"Error getting credentials: {e}")
        return None

def makeApiCall(url, endpointParams, request_type):
    """Request data from endpoint with params

    Args:
        url: string of the url endpoint to make request from
        endpointParams: dictionary keyed by the names of the url parameters
        request_type: 'POST' or 'GET'

    Returns:
        dict: response data from the endpoint

    """
    try:
        if request_type == 'POST':
            data = requests.post(url, data=endpointParams, timeout=30)
        else:
            data = requests.get(url, params=endpointParams, timeout=30)

        data.raise_for_status()  # Raise an exception for bad status codes

        response = {
            'url': url,
            'endpoint_params': endpointParams,
            'endpoint_params_pretty': json.dumps(endpointParams, indent=4),
            'json_data': data.json(),
            'status_code': data.status_code
        }
        response['json_data_pretty'] = json.dumps(response['json_data'], indent=4)

        return response

    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed: {e}")
        return {
            'error': str(e),
            'url': url,
            'endpoint_params': endpointParams
        }
    except json.JSONDecodeError as e:
        logger.error(f"Failed to decode JSON response: {e}")
        return {
            'error': f"JSON decode error: {e}",
            'url': url,
            'endpoint_params': endpointParams
        }


def createMediaObject( params ) :
	""" Create media object

	Args:
		params: dictionary of params
	
	API Endpoint:
		https://graph.facebook.com/v5.0/{ig-user-id}/media?image_url={image-url}&caption={caption}&access_token={access-token}
		https://graph.facebook.com/v5.0/{ig-user-id}/media?video_url={video-url}&caption={caption}&access_token={access-token}

	Returns:
		object: data from the endpoint

	"""

	url = params['endpoint_base'] + params['instagram_account_id'] + '/media' # endpoint url

	endpointParams = dict() # parameter to send to the endpoint
	endpointParams['access_token'] = params['access_token'] # access token
	endpointParams['caption'] = params['caption']  # caption for the post

	if 'IMAGE' == params['media_type'] : # posting image
		endpointParams['image_url'] = params['media_url']  # url to the asset
	else : # posting video
		endpointParams['media_type'] = params['media_type']  # specify media type
		endpointParams['video_url'] = params['media_url']  # url to the asset
	
	return makeApiCall( url, endpointParams, 'POST' ) # make the api call

def getMediaObjectStatus( mediaObjectId, params ) :
	""" Check the status of a media object

	Args:
		mediaObjectId: id of the media object
		params: dictionary of params
	
	API Endpoint:
		https://graph.facebook.com/v5.0/{ig-container-id}?fields=status_code

	Returns:
		object: data from the endpoint

	"""

	url = params['endpoint_base'] + '/' + mediaObjectId # endpoint url

	endpointParams = dict() # parameter to send to the endpoint
	endpointParams['fields'] = 'status_code' # fields to get back
	endpointParams['access_token'] = params['access_token'] # access token

	return makeApiCall( url, endpointParams, 'GET' ) # make the api call

def publishMedia( mediaObjectId, params ) :
	""" Publish content

	Args:
		mediaObjectId: id of the media object
		params: dictionary of params
	
	API Endpoint:
		https://graph.facebook.com/v5.0/{ig-user-id}/media_publish?creation_id={creation-id}&access_token={access-token}

	Returns:
		object: data from the endpoint

	"""

	url = params['endpoint_base'] + params['instagram_account_id'] + '/media_publish' # endpoint url
    
	endpointParams = dict() # parameter to send to the endpoint
	endpointParams['creation_id'] = mediaObjectId # fields to get back
	endpointParams['access_token'] = params['access_token'] # access token

	return makeApiCall( url, endpointParams, 'POST' ) # make the api call

def getContentPublishingLimit( params ) :
	""" Get the api limit for the user

	Args:
		params: dictionary of params
	
	API Endpoint:
		https://graph.facebook.com/v5.0/{ig-user-id}/content_publishing_limit?fields=config,quota_usage

	Returns:
		object: data from the endpoint

	"""

	url = params['endpoint_base'] + params['instagram_account_id'] + '/content_publishing_limit' # endpoint url

	endpointParams = dict() # parameter to send to the endpoint
	endpointParams['fields'] = 'config,quota_usage' # fields to get back
	endpointParams['access_token'] = params['access_token'] # access token

	return makeApiCall( url, endpointParams, 'GET' ) # make the api call

