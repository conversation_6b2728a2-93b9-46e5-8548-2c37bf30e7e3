{% extends 'base.html' %}
{% load static %}

{% block title %}Connect Social Media Accounts - Imagsta{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-share text-primary"></i> Connect Your Accounts
                    </h2>
                    <p class="text-muted">Connect your social media accounts to start posting and managing your content</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary fs-6">{{ total_connected }} Connected</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Connected Accounts -->
    {% if connected_accounts %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-check-circle text-success"></i> Connected Accounts
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for account in connected_accounts %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="connected-account-card p-3 border rounded">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="platform-icon me-3">
                                        <i class="bi {{ account.get_platform_icon }} {{ account.get_platform_color }} fs-2"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ account.platform|title }}</h6>
                                        <small class="text-muted">@{{ account.username }}</small>
                                        <br>
                                        <span class="badge bg-success">{{ account.status|title }}</span>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="refreshAccount({{ account.id }})">
                                                    <i class="bi bi-arrow-clockwise"></i> Refresh Data
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item text-danger" href="#" onclick="disconnectAccount({{ account.id }}, '{{ account.platform|title }}')">
                                                    <i class="bi bi-x-circle"></i> Disconnect
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <!-- Account Stats -->
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h6 class="text-primary mb-0">{{ account.follower_count|floatformat:0 }}</h6>
                                            <small class="text-muted">Followers</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h6 class="text-info mb-0">{{ account.following_count|floatformat:0 }}</h6>
                                            <small class="text-muted">Following</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h6 class="text-success mb-0">{{ account.posts_count|floatformat:0 }}</h6>
                                            <small class="text-muted">Posts</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> Last sync: {{ account.last_sync|timesince }} ago
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Available Platforms -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-plus-circle text-primary"></i> Available Platforms
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for platform in available_platforms %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="platform-card h-100 p-4 border rounded {% if platform.connected %}border-success bg-light{% endif %}">
                                <div class="text-center mb-3">
                                    <i class="bi {{ platform.icon }} {{ platform.color }} fs-1"></i>
                                    <h5 class="mt-2 mb-1">{{ platform.name }}</h5>
                                    <p class="text-muted small">{{ platform.description }}</p>
                                </div>
                                
                                <!-- Features -->
                                <div class="features mb-3">
                                    <h6 class="small text-muted mb-2">Features:</h6>
                                    <div class="d-flex flex-wrap gap-1">
                                        {% for feature in platform.features %}
                                        <span class="badge bg-light text-dark">{{ feature }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                
                                <!-- Connect Button -->
                                <div class="text-center">
                                    {% if platform.connected %}
                                        <button class="btn btn-success" disabled>
                                            <i class="bi bi-check-circle"></i> Connected
                                        </button>
                                    {% else %}
                                        <button class="btn btn-primary" onclick="connectPlatform('{{ platform.id }}', '{{ platform.name }}')">
                                            <i class="bi bi-plus-circle"></i> Connect {{ platform.name }}
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-info-circle text-info"></i> Need Help?
                    </h6>
                    <p class="card-text small text-muted mb-2">
                        Connecting your social media accounts allows Imagsta to post content directly to your profiles. 
                        Your login credentials are never stored - we use secure OAuth authentication.
                    </p>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="small">What you can do:</h6>
                            <ul class="small text-muted">
                                <li>Post content directly to connected platforms</li>
                                <li>Schedule posts for optimal engagement</li>
                                <li>Track performance across all platforms</li>
                                <li>Manage multiple accounts from one dashboard</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="small">Security & Privacy:</h6>
                            <ul class="small text-muted">
                                <li>OAuth 2.0 secure authentication</li>
                                <li>No passwords stored on our servers</li>
                                <li>Revoke access anytime from your account</li>
                                <li>Read-only access for analytics (optional)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.connected-account-card {
    transition: all 0.2s;
    background: #f8f9fa;
}

.connected-account-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.platform-card {
    transition: all 0.2s;
}

.platform-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.stat-item h6 {
    font-size: 1.1rem;
    font-weight: 600;
}

.features .badge {
    font-size: 0.7rem;
}

.platform-icon {
    transition: transform 0.2s;
}

.platform-icon:hover {
    transform: scale(1.05);
}
</style>

<script>
function connectPlatform(platformId, platformName) {
    const button = event.target;
    const originalText = button.innerHTML;
    
    // Show loading state
    button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Connecting...';
    button.disabled = true;
    
    fetch(`/connect/${platformId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Reload page to update UI
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(data.error || 'Connection failed', 'error');
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Connection failed. Please try again.', 'error');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function disconnectAccount(accountId, platformName) {
    if (!confirm(`Are you sure you want to disconnect your ${platformName} account?`)) {
        return;
    }
    
    fetch(`/disconnect/${accountId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Reload page to update UI
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(data.error || 'Disconnection failed', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Disconnection failed. Please try again.', 'error');
    });
}

function refreshAccount(accountId) {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Refreshing...';
    
    fetch(`/refresh-account/${accountId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Reload page to update data
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(data.error || 'Refresh failed', 'error');
        }
        button.innerHTML = originalText;
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Refresh failed. Please try again.', 'error');
        button.innerHTML = originalText;
    });
}

function showNotification(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.innerHTML = `
        <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : 'check-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 5000);
}

// Add spinning animation
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>

{% endblock %}
