"""
AI-powered content generation services for Imagsta.
This module provides intelligent content creation capabilities including
caption generation, hashtag suggestions, and content optimization.
"""

import logging
import requests
import json
import re
from typing import List, Dict, Optional, Tuple
from django.conf import settings
from django.utils import timezone
from decouple import config

# Try to import OpenAI library, fall back to requests if not available
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

logger = logging.getLogger(__name__)


class AIContentService:
    """Service for AI-powered content generation and optimization."""

    def __init__(self):
        self.openai_api_key = config('OPENAI_API_KEY', default='')
        self.openai_base_url = 'https://api.openai.com/v1'

        # Initialize OpenAI client if available
        if OPENAI_AVAILABLE and self.openai_api_key and not self.openai_api_key.startswith('demo'):
            try:
                self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
                self.use_openai_library = True
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")
                self.use_openai_library = False
        else:
            self.use_openai_library = False
        
    def generate_caption(self, 
                        image_description: str, 
                        news_context: str, 
                        tone: str = 'professional',
                        max_length: int = 2200) -> Optional[str]:
        """
        Generate an engaging caption using AI based on image and news context.
        
        Args:
            image_description: Description of the image content
            news_context: Related news article or context
            tone: Desired tone (professional, casual, humorous, inspirational)
            max_length: Maximum caption length (Instagram limit is 2200)
            
        Returns:
            Generated caption or None if generation fails
        """
        if not self.openai_api_key or self.openai_api_key.startswith('demo'):
            logger.warning("OpenAI API key not configured or using demo key")
            return self._generate_fallback_caption(news_context, tone)

        try:
            prompt = self._build_caption_prompt(image_description, news_context, tone)

            if self.use_openai_library:
                # Use modern OpenAI library
                response = self.openai_client.chat.completions.create(
                    model='gpt-3.5-turbo',
                    messages=[
                        {'role': 'system', 'content': 'You are a social media expert who creates engaging Instagram captions.'},
                        {'role': 'user', 'content': prompt}
                    ],
                    max_tokens=min(max_length // 4, 500),
                    temperature=0.7
                )
                caption = response.choices[0].message.content.strip()
                return caption[:max_length]
            else:
                # Fallback to requests
                response = requests.post(
                    f"{self.openai_base_url}/chat/completions",
                    headers={
                        'Authorization': f'Bearer {self.openai_api_key}',
                        'Content-Type': 'application/json'
                    },
                    json={
                        'model': 'gpt-3.5-turbo',
                        'messages': [
                            {'role': 'system', 'content': 'You are a social media expert who creates engaging Instagram captions.'},
                            {'role': 'user', 'content': prompt}
                        ],
                        'max_tokens': min(max_length // 4, 500),
                        'temperature': 0.7
                    },
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    caption = data['choices'][0]['message']['content'].strip()
                    return caption[:max_length]
                else:
                    logger.error(f"OpenAI API error: {response.status_code} - {response.text}")
                    return self._generate_fallback_caption(news_context, tone)

        except Exception as e:
            logger.error(f"Error generating caption: {e}")
            return self._generate_fallback_caption(news_context, tone)
    
    def suggest_hashtags(self, 
                        content: str, 
                        industry: str = 'general',
                        max_hashtags: int = 30) -> List[str]:
        """
        Generate relevant hashtags for the content.
        
        Args:
            content: The post content/caption
            industry: Industry or niche (tech, business, lifestyle, etc.)
            max_hashtags: Maximum number of hashtags to return
            
        Returns:
            List of suggested hashtags
        """
        if not self.openai_api_key or self.openai_api_key.startswith('demo'):
            return self._generate_fallback_hashtags(content, industry)

        try:
            prompt = f"""
            Generate relevant Instagram hashtags for this content:

            Content: {content[:500]}
            Industry: {industry}

            Requirements:
            - Return {max_hashtags} hashtags
            - Mix of popular and niche hashtags
            - Include industry-specific tags
            - Format as comma-separated list without # symbol
            - No explanations, just the hashtags
            """

            if self.use_openai_library:
                # Use modern OpenAI library
                response = self.openai_client.chat.completions.create(
                    model='gpt-3.5-turbo',
                    messages=[
                        {'role': 'system', 'content': 'You are a social media hashtag expert.'},
                        {'role': 'user', 'content': prompt}
                    ],
                    max_tokens=200,
                    temperature=0.5
                )
                hashtags_text = response.choices[0].message.content.strip()
            else:
                # Fallback to requests
                response = requests.post(
                    f"{self.openai_base_url}/chat/completions",
                    headers={
                        'Authorization': f'Bearer {self.openai_api_key}',
                        'Content-Type': 'application/json'
                },
                json={
                    'model': 'gpt-3.5-turbo',
                    'messages': [
                        {'role': 'system', 'content': 'You are a social media hashtag expert.'},
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 200,
                    'temperature': 0.5
                },
                timeout=30
            )

                if response.status_code == 200:
                    data = response.json()
                    hashtags_text = data['choices'][0]['message']['content'].strip()
                else:
                    logger.error(f"OpenAI API error for hashtags: {response.status_code}")
                    return self._generate_fallback_hashtags(content, industry)

            # Parse hashtags from response
            hashtags = [f"#{tag.strip()}" for tag in hashtags_text.split(',') if tag.strip()]
            return hashtags[:max_hashtags]

        except Exception as e:
            logger.error(f"Error generating hashtags: {e}")
            return self._generate_fallback_hashtags(content, industry)

    def generate_hashtags(self, query: str, max_hashtags: int = 10) -> List[str]:
        """
        Generate hashtags for a given query/topic.

        Args:
            query: The topic or keyword to generate hashtags for
            max_hashtags: Maximum number of hashtags to return

        Returns:
            List of hashtags
        """
        return self.suggest_hashtags(query, max_hashtags=max_hashtags)

    def optimize_posting_time(self, user_analytics: Dict) -> Dict[str, str]:
        """
        Suggest optimal posting times based on user analytics.
        
        Args:
            user_analytics: Dictionary containing user engagement data
            
        Returns:
            Dictionary with suggested posting times
        """
        # This would integrate with actual analytics data
        # For now, return general best practices
        return {
            'best_day': 'Tuesday',
            'best_time': '11:00 AM',
            'alternative_times': ['2:00 PM', '5:00 PM', '8:00 PM'],
            'reasoning': 'Based on general engagement patterns for your audience'
        }
    
    def analyze_content_performance(self, content: str) -> Dict[str, any]:
        """
        Predict content performance using AI analysis.
        
        Args:
            content: The content to analyze
            
        Returns:
            Dictionary with performance predictions
        """
        # Placeholder for ML model that would predict engagement
        return {
            'engagement_score': 7.5,  # Out of 10
            'predicted_likes': '150-300',
            'predicted_comments': '10-25',
            'virality_potential': 'Medium',
            'suggestions': [
                'Consider adding more emojis for better engagement',
                'The caption length is optimal for Instagram',
                'Adding a call-to-action could increase comments'
            ]
        }
    
    def _build_caption_prompt(self, image_description: str, news_context: str, tone: str) -> str:
        """Build the prompt for caption generation."""
        tone_instructions = {
            'professional': 'Use professional, informative language suitable for business audiences.',
            'casual': 'Use friendly, conversational language that feels natural and approachable.',
            'humorous': 'Add humor and wit while keeping it appropriate and engaging.',
            'inspirational': 'Use motivational and uplifting language that inspires action.'
        }
        
        return f"""
        Create an engaging Instagram caption based on:
        
        Image: {image_description}
        News Context: {news_context}
        Tone: {tone_instructions.get(tone, tone_instructions['professional'])}
        
        Requirements:
        - 50-150 words
        - Include relevant emojis
        - End with a call-to-action question
        - Make it engaging and shareable
        - Don't include hashtags (they'll be added separately)
        """
    
    def _generate_fallback_caption(self, news_context: str, tone: str) -> str:
        """Generate a simple fallback caption when AI is unavailable."""
        templates = {
            'professional': f"Stay informed about the latest developments. {news_context[:100]}... What are your thoughts on this?",
            'casual': f"Just saw this interesting news! {news_context[:100]}... What do you think?",
            'humorous': f"Well, this is interesting! 😄 {news_context[:100]}... Anyone else find this amusing?",
            'inspirational': f"Every challenge brings opportunity. {news_context[:100]}... How will you turn this into success?"
        }
        return templates.get(tone, templates['professional'])
    
    def _generate_fallback_hashtags(self, content: str, industry: str) -> List[str]:
        """Generate basic hashtags when AI is unavailable."""
        base_hashtags = ['#news', '#trending', '#update', '#socialmedia', '#content']
        
        industry_hashtags = {
            'tech': ['#technology', '#innovation', '#startup', '#digital', '#ai'],
            'business': ['#business', '#entrepreneur', '#marketing', '#growth', '#success'],
            'lifestyle': ['#lifestyle', '#inspiration', '#motivation', '#wellness', '#life'],
            'general': ['#daily', '#thoughts', '#share', '#community', '#discussion']
        }
        
        return base_hashtags + industry_hashtags.get(industry, industry_hashtags['general'])

    def analyze_news_article(self, article) -> Dict[str, any]:
        """
        Analyze a news article for engagement potential and content insights.

        Args:
            article: NewsArticle model instance

        Returns:
            Dictionary with analysis results
        """
        try:
            # Basic content analysis
            content_analysis = self._analyze_content_metrics(article)

            # Sentiment analysis
            sentiment_score = self._analyze_sentiment(article.content)

            # Trending potential
            trending_score = self._calculate_trending_score(article)

            # Engagement prediction
            engagement_prediction = self._predict_engagement(article, content_analysis)

            # Extract keywords and entities
            keywords = self._extract_keywords(article.content)
            entities = self._extract_entities(article.content)

            # Topic classification
            topics = self._classify_topics(article.content)

            return {
                'sentiment_score': sentiment_score,
                'engagement_prediction': engagement_prediction,
                'trending_score': trending_score,
                'keywords': keywords,
                'entities': entities,
                'topics': topics,
                'content_metrics': content_analysis,
                'processed_at': timezone.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error analyzing article {article.id}: {e}")
            return self._get_fallback_analysis()

    def should_highlight_article(self, article, threshold: float = 70.0) -> Tuple[bool, Dict[str, any]]:
        """
        Determine if an article should be highlighted based on AI analysis.

        Args:
            article: NewsArticle model instance
            threshold: Minimum score for highlighting

        Returns:
            Tuple of (should_highlight, highlight_data)
        """
        analysis = self.analyze_news_article(article)

        # Calculate overall highlight score
        highlight_score = self._calculate_highlight_score(article, analysis)

        # Generate highlight reasons
        reasons = self._generate_highlight_reasons(article, analysis, highlight_score)

        # Generate recommendations
        recommendations = self._generate_posting_recommendations(article, analysis)

        highlight_data = {
            'highlight_score': highlight_score,
            'highlight_reasons': reasons,
            'recommended_platforms': recommendations['platforms'],
            'suggested_posting_time': recommendations['posting_time'],
            'suggested_hashtags': recommendations['hashtags'],
            'suggested_caption': recommendations['caption'],
            'analysis_data': analysis
        }

        should_highlight = highlight_score >= threshold

        return should_highlight, highlight_data

    def _analyze_content_metrics(self, article) -> Dict[str, any]:
        """Analyze basic content metrics."""
        content = article.content
        title = article.title

        return {
            'title_length': len(title),
            'content_length': len(content),
            'word_count': len(content.split()),
            'sentence_count': len(re.split(r'[.!?]+', content)),
            'has_image': bool(article.image_url),
            'has_author': bool(article.author),
            'readability_score': self._calculate_readability(content),
            'urgency_indicators': self._detect_urgency_indicators(title + ' ' + content),
        }

    def _analyze_sentiment(self, content: str) -> float:
        """Analyze sentiment of content. Returns score between -1 and 1."""
        if not self.openai_api_key:
            return self._basic_sentiment_analysis(content)

        try:
            prompt = f"""
            Analyze the sentiment of this text and return only a number between -1 and 1:
            -1 = very negative
            0 = neutral
            1 = very positive

            Text: {content[:1000]}

            Return only the number:
            """

            response = requests.post(
                f"{self.openai_base_url}/chat/completions",
                headers={
                    'Authorization': f'Bearer {self.openai_api_key}',
                    'Content-Type': 'application/json'
                },
                json={
                    'model': 'gpt-3.5-turbo',
                    'messages': [{'role': 'user', 'content': prompt}],
                    'max_tokens': 10,
                    'temperature': 0.1
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                sentiment_text = result['choices'][0]['message']['content'].strip()
                try:
                    return float(sentiment_text)
                except ValueError:
                    return 0.0
            else:
                return self._basic_sentiment_analysis(content)

        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            return self._basic_sentiment_analysis(content)

    def _basic_sentiment_analysis(self, content: str) -> float:
        """Basic sentiment analysis using keyword matching."""
        positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'success', 'win', 'breakthrough', 'innovation']
        negative_words = ['bad', 'terrible', 'awful', 'crisis', 'problem', 'fail', 'disaster', 'concern', 'issue']

        content_lower = content.lower()
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)

        total_words = len(content.split())
        if total_words == 0:
            return 0.0

        sentiment = (positive_count - negative_count) / max(total_words / 100, 1)
        return max(-1.0, min(1.0, sentiment))

    def _calculate_trending_score(self, article) -> float:
        """Calculate trending potential score (0-100)."""
        score = 50.0  # Base score

        # Recent articles get higher scores
        hours_since_published = (timezone.now() - article.published_date).total_seconds() / 3600
        if hours_since_published < 1:
            score += 30
        elif hours_since_published < 6:
            score += 20
        elif hours_since_published < 24:
            score += 10

        # Social metrics boost
        social_total = article.social_shares + article.social_likes + article.social_comments
        if social_total > 100:
            score += 20
        elif social_total > 50:
            score += 15
        elif social_total > 10:
            score += 10

        # Trending keywords
        trending_keywords = ['ai', 'artificial intelligence', 'breakthrough', 'new', 'first', 'record', 'major']
        content_lower = (article.title + ' ' + article.content).lower()
        keyword_matches = sum(1 for keyword in trending_keywords if keyword in content_lower)
        score += keyword_matches * 5

        return min(100.0, max(0.0, score))

    def _predict_engagement(self, article, content_analysis) -> float:
        """Predict engagement score (0-100) based on various factors."""
        score = 40.0  # Base score

        # Title quality
        title_len = content_analysis['title_length']
        if 40 <= title_len <= 100:  # Optimal title length
            score += 15
        elif 20 <= title_len <= 140:
            score += 10

        # Content quality
        word_count = content_analysis['word_count']
        if 150 <= word_count <= 800:  # Optimal content length
            score += 15
        elif 100 <= word_count <= 1200:
            score += 10

        # Media presence
        if content_analysis['has_image']:
            score += 10

        # Readability
        readability = content_analysis['readability_score']
        if readability > 0.7:
            score += 10
        elif readability > 0.5:
            score += 5

        # Urgency and timeliness
        if content_analysis['urgency_indicators'] > 0:
            score += 5 * min(content_analysis['urgency_indicators'], 3)

        # Source credibility (based on source metrics)
        if hasattr(article.source, 'fetch_count') and article.source.fetch_count > 10:
            error_rate = article.source.error_count / article.source.fetch_count
            if error_rate < 0.1:  # Low error rate = credible source
                score += 10
            elif error_rate < 0.3:
                score += 5

        return min(100.0, max(0.0, score))

    def _extract_keywords(self, content: str) -> List[str]:
        """Extract important keywords from content."""
        # Simple keyword extraction - in production, use more sophisticated NLP
        words = re.findall(r'\b[a-zA-Z]{4,}\b', content.lower())

        # Remove common stop words
        stop_words = {'this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well', 'were'}

        # Count word frequency
        word_freq = {}
        for word in words:
            if word not in stop_words and len(word) > 3:
                word_freq[word] = word_freq.get(word, 0) + 1

        # Return top keywords
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:10] if freq > 1]

    def _extract_entities(self, content: str) -> List[str]:
        """Extract named entities from content."""
        # Simple entity extraction - look for capitalized words/phrases
        entities = []

        # Find potential company/organization names
        company_patterns = [
            r'\b[A-Z][a-z]+ (?:Inc|Corp|LLC|Ltd|Company|Technologies|Systems|Solutions)\b',
            r'\b[A-Z]{2,}(?:\s+[A-Z]{2,})*\b',  # Acronyms
        ]

        for pattern in company_patterns:
            matches = re.findall(pattern, content)
            entities.extend(matches)

        # Find potential person names (simple heuristic)
        name_pattern = r'\b[A-Z][a-z]+ [A-Z][a-z]+\b'
        names = re.findall(name_pattern, content)
        entities.extend(names)

        return list(set(entities))[:10]  # Remove duplicates and limit

    def _classify_topics(self, content: str) -> List[str]:
        """Classify content into topics."""
        topic_keywords = {
            'technology': ['ai', 'artificial intelligence', 'machine learning', 'software', 'app', 'digital', 'tech', 'innovation', 'startup', 'coding', 'programming'],
            'business': ['business', 'company', 'market', 'economy', 'finance', 'investment', 'revenue', 'profit', 'sales', 'marketing'],
            'health': ['health', 'medical', 'doctor', 'hospital', 'medicine', 'treatment', 'disease', 'wellness', 'fitness'],
            'politics': ['government', 'political', 'election', 'policy', 'law', 'congress', 'senate', 'president', 'vote'],
            'sports': ['sports', 'game', 'team', 'player', 'championship', 'league', 'match', 'tournament', 'athlete'],
            'entertainment': ['movie', 'film', 'music', 'celebrity', 'actor', 'singer', 'entertainment', 'show', 'concert'],
            'science': ['research', 'study', 'scientist', 'discovery', 'experiment', 'science', 'university', 'academic'],
            'environment': ['climate', 'environment', 'green', 'sustainable', 'energy', 'pollution', 'carbon', 'renewable']
        }

        content_lower = content.lower()
        topic_scores = {}

        for topic, keywords in topic_keywords.items():
            score = sum(1 for keyword in keywords if keyword in content_lower)
            if score > 0:
                topic_scores[topic] = score

        # Return topics sorted by relevance
        sorted_topics = sorted(topic_scores.items(), key=lambda x: x[1], reverse=True)
        return [topic for topic, score in sorted_topics[:3]]

    def _calculate_readability(self, content: str) -> float:
        """Calculate readability score (0-1, higher is more readable)."""
        if not content:
            return 0.0

        sentences = len(re.split(r'[.!?]+', content))
        words = len(content.split())

        if sentences == 0 or words == 0:
            return 0.0

        # Simple readability metric based on average sentence length
        avg_sentence_length = words / sentences

        # Optimal sentence length is around 15-20 words
        if 10 <= avg_sentence_length <= 25:
            return 1.0
        elif 5 <= avg_sentence_length <= 35:
            return 0.7
        else:
            return 0.4

    def _detect_urgency_indicators(self, text: str) -> int:
        """Detect urgency indicators in text."""
        urgency_words = ['breaking', 'urgent', 'alert', 'emergency', 'crisis', 'immediate', 'now', 'just in', 'developing', 'live', 'update']
        text_lower = text.lower()
        return sum(1 for word in urgency_words if word in text_lower)

    def _calculate_highlight_score(self, article, analysis) -> float:
        """Calculate overall highlight score."""
        weights = {
            'engagement_prediction': 0.3,
            'trending_score': 0.25,
            'sentiment_score': 0.15,
            'social_metrics': 0.15,
            'recency': 0.15
        }

        # Normalize sentiment score to 0-100
        sentiment_normalized = (analysis['sentiment_score'] + 1) * 50

        # Social metrics score
        social_total = article.social_shares + article.social_likes + article.social_comments
        social_score = min(100, social_total * 2)  # Scale social metrics

        # Recency score
        hours_old = (timezone.now() - article.published_date).total_seconds() / 3600
        recency_score = max(0, 100 - (hours_old * 2))  # Decay over time

        # Calculate weighted score
        score = (
            analysis['engagement_prediction'] * weights['engagement_prediction'] +
            analysis['trending_score'] * weights['trending_score'] +
            sentiment_normalized * weights['sentiment_score'] +
            social_score * weights['social_metrics'] +
            recency_score * weights['recency']
        )

        return min(100.0, max(0.0, score))

    def _generate_highlight_reasons(self, article, analysis, highlight_score) -> List[str]:
        """Generate reasons why an article should be highlighted."""
        reasons = []

        if analysis['engagement_prediction'] > 70:
            reasons.append("High engagement potential based on content analysis")

        if analysis['trending_score'] > 75:
            reasons.append("Strong trending potential")

        if analysis['sentiment_score'] > 0.3:
            reasons.append("Positive sentiment likely to drive engagement")

        social_total = article.social_shares + article.social_likes + article.social_comments
        if social_total > 50:
            reasons.append(f"Already gaining traction with {social_total} social interactions")

        hours_old = (timezone.now() - article.published_date).total_seconds() / 3600
        if hours_old < 2:
            reasons.append("Very recent news - perfect timing for posting")

        if analysis['content_metrics']['has_image']:
            reasons.append("Includes visual content for better engagement")

        if analysis['content_metrics']['urgency_indicators'] > 0:
            reasons.append("Contains urgency indicators suggesting timely relevance")

        if len(analysis['keywords']) > 5:
            reasons.append("Rich keyword content for better discoverability")

        return reasons[:5]  # Limit to top 5 reasons

    def _generate_posting_recommendations(self, article, analysis) -> Dict[str, any]:
        """Generate posting recommendations for the article."""
        # Determine best platforms based on content type and topics
        platforms = []
        topics = analysis.get('topics', [])

        if 'technology' in topics:
            platforms.extend(['twitter', 'linkedin'])
        if 'business' in topics:
            platforms.extend(['linkedin', 'twitter'])
        if 'entertainment' in topics:
            platforms.extend(['instagram', 'facebook', 'twitter'])
        if 'sports' in topics:
            platforms.extend(['twitter', 'facebook', 'instagram'])

        # Default platforms if no specific topics
        if not platforms:
            platforms = ['twitter', 'facebook', 'linkedin']

        # Remove duplicates and limit
        platforms = list(set(platforms))[:3]

        # Suggest posting time (within next 2 hours for trending content)
        suggested_time = timezone.now() + timezone.timedelta(minutes=30)
        if analysis['trending_score'] > 80:
            suggested_time = timezone.now() + timezone.timedelta(minutes=15)

        # Generate hashtags based on topics and keywords
        hashtags = []
        for topic in topics:
            hashtags.append(f"#{topic}")

        for keyword in analysis['keywords'][:3]:
            hashtags.append(f"#{keyword.replace(' ', '')}")

        hashtags.extend(['#news', '#trending', '#update'])
        hashtags = list(set(hashtags))[:10]  # Remove duplicates and limit

        # Generate caption
        caption = self._generate_news_caption(article, analysis)

        return {
            'platforms': platforms,
            'posting_time': suggested_time,
            'hashtags': hashtags,
            'caption': caption
        }

    def _generate_news_caption(self, article, analysis) -> str:
        """Generate a caption for news article posting."""
        # Use AI if available, otherwise use template
        if self.openai_api_key:
            try:
                prompt = f"""
                Create an engaging social media caption for this news article:

                Title: {article.title}
                Content: {article.content[:500]}...
                Topics: {', '.join(analysis.get('topics', []))}

                Requirements:
                - 50-150 words
                - Engaging and shareable
                - Include relevant emojis
                - End with a question to encourage engagement
                - Don't include hashtags
                - Match a professional but approachable tone
                """

                response = requests.post(
                    f"{self.openai_base_url}/chat/completions",
                    headers={
                        'Authorization': f'Bearer {self.openai_api_key}',
                        'Content-Type': 'application/json'
                    },
                    json={
                        'model': 'gpt-3.5-turbo',
                        'messages': [{'role': 'user', 'content': prompt}],
                        'max_tokens': 200,
                        'temperature': 0.7
                    },
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content'].strip()

            except Exception as e:
                logger.error(f"Error generating AI caption: {e}")

        # Fallback template-based caption
        return self._generate_template_caption(article, analysis)

    def _generate_template_caption(self, article, analysis) -> str:
        """Generate caption using templates."""
        topics = analysis.get('topics', [])
        sentiment = analysis.get('sentiment_score', 0)

        if sentiment > 0.3:
            tone = "positive"
        elif sentiment < -0.3:
            tone = "concerning"
        else:
            tone = "neutral"

        templates = {
            'positive': [
                f"🚀 Exciting developments in {topics[0] if topics else 'the news'}! {article.title[:100]}... What's your take on this breakthrough?",
                f"✨ Great news! {article.title[:120]}... How do you think this will impact the industry?",
                f"📈 Positive trends emerging: {article.title[:100]}... What opportunities do you see here?"
            ],
            'concerning': [
                f"⚠️ Important update: {article.title[:120]}... What are your thoughts on addressing this challenge?",
                f"🤔 This development raises important questions: {article.title[:100]}... How should we respond?",
                f"📊 Analyzing the implications: {article.title[:120]}... What's your perspective?"
            ],
            'neutral': [
                f"📰 Latest update: {article.title[:120]}... What's your opinion on this development?",
                f"🔍 Worth discussing: {article.title[:120]}... How do you see this playing out?",
                f"💭 Food for thought: {article.title[:120]}... What are your insights?"
            ]
        }

        import random
        return random.choice(templates[tone])

    def _get_fallback_analysis(self) -> Dict[str, any]:
        """Return fallback analysis when AI processing fails."""
        return {
            'sentiment_score': 0.0,
            'engagement_prediction': 50.0,
            'trending_score': 30.0,
            'keywords': [],
            'entities': [],
            'topics': [],
            'content_metrics': {
                'title_length': 0,
                'content_length': 0,
                'word_count': 0,
                'sentence_count': 0,
                'has_image': False,
                'has_author': False,
                'readability_score': 0.5,
                'urgency_indicators': 0,
            },
            'processed_at': timezone.now().isoformat()
        }


# Singleton instance
ai_service = AIContentService()
