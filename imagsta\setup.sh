#!/bin/bash

echo "========================================"
echo "IMAGSTA NEWS AGGREGATION SYSTEM SETUP"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ ERROR: Python 3 is not installed"
    echo "Please install Python 3.8+ from https://python.org"
    exit 1
fi

echo "✅ Python is installed"
echo

# Create virtual environment
echo "🔄 Creating virtual environment..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi
echo

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "🔄 Installing requirements..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Failed to install requirements"
    exit 1
fi
echo "✅ Requirements installed"
echo

# Run deployment script
echo "🔄 Running deployment script..."
python deploy.py
if [ $? -ne 0 ]; then
    echo "❌ Deployment script failed"
    exit 1
fi

echo
echo "========================================"
echo "🎉 SETUP COMPLETED SUCCESSFULLY!"
echo "========================================"
echo
echo "To start the server:"
echo "1. source venv/bin/activate"
echo "2. python manage.py runserver"
echo
echo "Then visit: http://localhost:8000/news-dashboard/"
echo
