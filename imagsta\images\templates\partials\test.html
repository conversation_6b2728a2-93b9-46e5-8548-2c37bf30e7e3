
<div id="images" class="album py-4 my-4 bg-dark text-white container" >
    <div class="container">

    <h1 class="display-5 fw-bold text-center text-light pt-3">Choose Image</h1>
    <div class="w-50 mx-auto mt-5">
        {% comment %} <form action="">
            <button type="button" 
            hx-vals='{
                "image": "{{i}}",
                "feed": "{{feed}}"
            }
            '
            hx-swap="outerHTML" 
            hx-target="#image-{{forloop.counter}}" 
            hx-post="{% url 'create-post' %}"
            class="btn btn-outline-dark btn-dark text-white p-2">Edit</button>
        </form> {% endcomment %}
        <form class="d-flex" 
        hx-post="{% url 'create-post1' %}" 
        hx-vals='{
            "feed": "{{feed}}"
        }'
        hx-swap="outerHTML"
        hx-target="#edit-btn">
            <input class="form-control me-2" type="url" name="image" placeholder="https://example.com">
            <button id="edit-btn" class="btn btn-outline-secondary" type="submit">Edit</button>
        </form>
    </div>
      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-5 mt-1">
        {% if images %}
            {% for i,d in images %}
                <form class="col">
                    <div class="card" id="image-{{forloop.counter}}">
                        <img src="{{d}}" alt="">
                        <div class="card-body">
                            <div class="d-flex justify-content-center align-items-center">
                                <div id="card" class="btn-group"  role="group">
                                    <button type="button" 
                                    hx-vals='{
                                        "image": "{{i}}",
                                        "feed": "{{feed}}"
                                    }
                                    '
                                    hx-swap="outerHTML" 
                                    hx-target="#image-{{forloop.counter}}" 
                                    hx-post="{% url 'create-post' %}"
                                    class="btn btn-outline-dark btn-dark text-white p-2">Edit</button>
                                    <form action="{{i}}" target="_blank" method="get">
                                        <button type="submit" class="btn btn-outline-dark btn-dark text-white p-2">view</button>
                                    </form>
                                    
                                    
                                    <button type="button" 
                                        {% comment %} hx-delete="{% url 'delete-image' %}"    {% endcomment %}
                                        hx-confirm="Are you sure you want to delete this task?"
                                        hx-target="#images"
                                        hx-swap="innerHtml"
                                        class="btn btn-outline-dark btn-dark text-white p-2"
                                        >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
            {% endfor %}
        {% else %}
            {% for i in "x"|ljust:"10" %}
                <div class="col">
                    <div class="card">
                        {% comment %} <svg class="bd-placeholder-img card-img-top" width="100%" height="300" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Placeholder: Thumbnail" preserveAspectRatio="xMidYMid slice" focusable="false"><title>Placeholder</title><rect width="100%" height="100%" fill="#55595c"/><text x="40%" y="50%" fill="#eceeef" dy=".2em">Thumbnail</text></svg> {% endcomment %}
                        <img src="https://images.unsplash.com/photo-1610103249112-b540391c2ef2?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8cmFufGVufDB8fDB8fA%3D%3D&w=1000&q=80" alt="">
                        <div class="card-body">
                            <div class="d-flex justify-content-center align-items-center">
                                <div class="btn-group">
                                <button type="button" class="btn btn-md btn-outline-secondary">View</button>
                                <button type="button" class="btn btn-md btn-dark">Edit</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            {% endfor %}
        {% endif %}
      </div>
    </div>
</div>


