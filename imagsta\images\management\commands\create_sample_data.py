"""
Management command to create sample data for testing.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import random
import os


class Command(BaseCommand):
    help = 'Create sample data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=3,
            help='Number of sample users to create',
        )
        parser.add_argument(
            '--posts-per-user',
            type=int,
            default=10,
            help='Number of posts per user',
        )

    def handle(self, *args, **options):
        from images.models import (
            Post, PostAnalytics, UserAnalytics, ContentTemplate, 
            SavedHashtagSet, ScheduledPost
        )
        from django.core.files.base import ContentFile
        
        num_users = options['users']
        posts_per_user = options['posts_per_user']
        
        self.stdout.write('Creating sample data...')
        
        # Create sample users
        users = []
        for i in range(num_users):
            username = f'testuser{i+1}'
            email = f'testuser{i+1}@example.com'
            
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': f'Test{i+1}',
                    'last_name': 'User'
                }
            )
            
            if created:
                user.set_password('testpass123')
                user.save()
                self.stdout.write(f'Created user: {username}')
            
            users.append(user)
        
        # Sample captions
        sample_captions = [
            "🌟 Excited to share this amazing moment with you all! What do you think?",
            "💡 Innovation starts with a single idea. What's yours?",
            "🚀 Ready to take on new challenges and reach for the stars!",
            "🎯 Focus on your goals and make them happen. You've got this!",
            "✨ Sometimes the best moments are the unexpected ones.",
            "🌈 Life is full of beautiful surprises. Embrace them all!",
            "💪 Strength comes from overcoming challenges, not avoiding them.",
            "🎨 Creativity is intelligence having fun. Let's create something amazing!",
            "🌱 Growth happens when you step outside your comfort zone.",
            "🔥 Passion is the fuel that drives success. What's your passion?"
        ]
        
        # Sample hashtags
        sample_hashtags = [
            "#motivation #inspiration #success #goals",
            "#creativity #innovation #technology #future",
            "#lifestyle #wellness #mindfulness #growth",
            "#business #entrepreneur #startup #hustle",
            "#art #design #photography #creative",
            "#travel #adventure #explore #wanderlust",
            "#fitness #health #workout #strong",
            "#food #cooking #delicious #foodie",
            "#nature #outdoors #beautiful #peaceful",
            "#learning #education #knowledge #wisdom"
        ]
        
        # Create posts for each user
        for user in users:
            self.stdout.write(f'Creating posts for {user.username}...')
            
            for j in range(posts_per_user):
                # Create post with random data
                caption = random.choice(sample_captions)
                hashtags = random.choice(sample_hashtags)
                
                # Random creation date (last 30 days)
                days_ago = random.randint(0, 30)
                created_date = timezone.now() - timedelta(days=days_ago)
                
                post = Post.objects.create(
                    user=user,
                    caption=caption,
                    hashtags=hashtags,
                    post_text=caption[:130],
                    posted=random.choice([True, False]),
                    created=created_date
                )
                
                # Create analytics for posted posts
                if post.posted:
                    # Generate realistic analytics
                    days_old = (timezone.now() - post.created).days
                    base_multiplier = max(1, 10 - days_old)
                    
                    likes = random.randint(10, 200) * base_multiplier
                    comments = random.randint(1, 50) * base_multiplier
                    shares = random.randint(0, 20) * base_multiplier
                    saves = random.randint(0, 30) * base_multiplier
                    
                    total_engagement = likes + comments + shares + saves
                    reach = int(total_engagement * random.uniform(5, 15))
                    impressions = int(reach * random.uniform(2, 5))
                    
                    PostAnalytics.objects.create(
                        post=post,
                        likes_count=likes,
                        comments_count=comments,
                        shares_count=shares,
                        saves_count=saves,
                        reach=reach,
                        impressions=impressions
                    )
            
            # Create user analytics
            user_analytics, created = UserAnalytics.objects.get_or_create(user=user)
            user_analytics.update_totals()
            
            # Create sample templates for first user
            if user == users[0]:
                templates = [
                    {
                        'name': 'Product Launch',
                        'template_type': 'campaign',
                        'content': '🚀 Exciting news! We\'re launching {product_name}! {description} #NewProduct #Launch',
                        'industry': 'technology',
                        'tone': 'professional',
                        'hashtags': '#NewProduct #Launch #Innovation #Tech'
                    },
                    {
                        'name': 'Behind the Scenes',
                        'template_type': 'caption',
                        'content': '👀 Behind the scenes at {company_name}! Here\'s how we {activity}. #BehindTheScenes',
                        'industry': 'business',
                        'tone': 'casual',
                        'hashtags': '#BehindTheScenes #TeamWork #Process'
                    },
                    {
                        'name': 'Motivational Monday',
                        'template_type': 'post_series',
                        'content': '💪 Monday Motivation: {quote} Remember, {advice}. #MotivationalMonday',
                        'industry': 'lifestyle',
                        'tone': 'inspirational',
                        'hashtags': '#MotivationalMonday #Inspiration #Goals'
                    }
                ]
                
                for template_data in templates:
                    ContentTemplate.objects.get_or_create(
                        user=user,
                        name=template_data['name'],
                        defaults=template_data
                    )
                
                # Create sample hashtag sets
                hashtag_sets = [
                    {
                        'name': 'Tech Startup',
                        'hashtags': '#startup, #technology, #innovation, #entrepreneur, #business',
                        'industry': 'technology'
                    },
                    {
                        'name': 'Lifestyle Content',
                        'hashtags': '#lifestyle, #wellness, #mindfulness, #selfcare, #motivation',
                        'industry': 'lifestyle'
                    }
                ]
                
                for hashtag_data in hashtag_sets:
                    SavedHashtagSet.objects.get_or_create(
                        user=user,
                        name=hashtag_data['name'],
                        defaults=hashtag_data
                    )
            
            # Create some scheduled posts
            draft_posts = Post.objects.filter(user=user, posted=False)[:3]
            for post in draft_posts:
                # Schedule for future dates
                future_date = timezone.now() + timedelta(
                    days=random.randint(1, 7),
                    hours=random.randint(9, 17)
                )
                
                ScheduledPost.objects.get_or_create(
                    post=post,
                    defaults={
                        'user': user,
                        'scheduled_for': future_date,
                        'target_platforms': ['instagram']
                    }
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Sample data created successfully!\n'
                f'- Users: {len(users)}\n'
                f'- Posts per user: {posts_per_user}\n'
                f'- Total posts: {len(users) * posts_per_user}\n'
                f'- Templates: 3\n'
                f'- Hashtag sets: 2'
            )
        )
