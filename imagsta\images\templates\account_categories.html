{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-link-45deg"></i> Account Categories
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'news-dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{% url 'connect-accounts' %}" class="btn btn-outline-primary">
                        <i class="bi bi-plus"></i> Connect Account
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Alert -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                <strong>Account Categories:</strong> 
                Assign categories to your social media accounts to automatically filter relevant news. 
                When AI highlights articles in these categories, they'll be recommended for posting to the corresponding accounts.
            </div>
        </div>
    </div>

    <!-- Account Categories Management -->
    <div class="row">
        <div class="col-12">
            {% if account_data %}
                {% for data in account_data %}
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="{{ data.account.get_platform_icon }} {{ data.account.get_platform_color }}"></i>
                                {{ data.account.platform|title }} - @{{ data.account.username }}
                            </h5>
                            <div>
                                <span class="badge bg-success">{{ data.account.follower_count }} followers</span>
                                <span class="badge bg-info">{{ data.mapped_categories|length }} categories</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form class="account-categories-form" data-account-id="{{ data.account.id }}">
                            {% csrf_token %}
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="mb-3">Select Categories for this Account:</h6>
                                    <div class="row">
                                        {% for category in categories %}
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="checkbox" 
                                                       name="category_ids" 
                                                       value="{{ category.id }}" 
                                                       id="category_{{ data.account.id }}_{{ category.id }}"
                                                       {% if category.id in data.current_mappings %}checked{% endif %}>
                                                <label class="form-check-label" for="category_{{ data.account.id }}_{{ category.id }}">
                                                    <i class="{{ category.icon }}"></i> {{ category.display_name }}
                                                </label>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <h6 class="mb-3">Current Settings:</h6>
                                    
                                    {% if data.mapped_categories %}
                                        <div class="mb-3">
                                            <strong>Mapped Categories:</strong><br>
                                            {% for category in data.mapped_categories %}
                                                <span class="badge bg-secondary me-1 mb-1">
                                                    <i class="{{ category.icon }}"></i> {{ category.display_name }}
                                                </span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="text-muted mb-3">
                                            <i class="bi bi-exclamation-triangle"></i> No categories assigned
                                        </div>
                                    {% endif %}
                                    
                                    <!-- Auto-posting settings -->
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="autoPost_{{ data.account.id }}" 
                                                   name="auto_post">
                                            <label class="form-check-label" for="autoPost_{{ data.account.id }}">
                                                Enable Auto-posting
                                            </label>
                                        </div>
                                        <small class="text-muted">
                                            Automatically post highlighted articles to this account
                                        </small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="minScore_{{ data.account.id }}" class="form-label">
                                            Minimum Highlight Score
                                        </label>
                                        <select class="form-select form-select-sm" 
                                                id="minScore_{{ data.account.id }}" 
                                                name="min_highlight_score">
                                            <option value="60">60 - Good</option>
                                            <option value="70" selected>70 - Very Good</option>
                                            <option value="80">80 - Excellent</option>
                                            <option value="90">90 - Outstanding</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="text-muted">
                                    <small>
                                        <i class="bi bi-info-circle"></i>
                                        Changes are saved automatically when you update categories
                                    </small>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check"></i> Update Categories
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-link text-muted" style="font-size: 4rem;"></i>
                        <h3 class="text-muted mt-3">No Connected Accounts</h3>
                        <p class="text-muted">
                            Connect your social media accounts to start assigning categories and automating your posting workflow.
                        </p>
                        <a href="{% url 'connect-accounts' %}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> Connect Your First Account
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Category Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-tags"></i> Available Categories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for category in categories %}
                        <div class="col-md-3 mb-3">
                            <div class="card border-secondary">
                                <div class="card-body p-3">
                                    <h6 class="card-title">
                                        <i class="{{ category.icon }}" style="color: {{ category.color }};"></i>
                                        {{ category.display_name }}
                                    </h6>
                                    <p class="card-text small text-muted">
                                        {{ category.description|truncatechars:80 }}
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            Priority: {{ category.priority_score }}
                                        </small>
                                        <span class="badge bg-light text-dark">
                                            {{ category.articles.count }} articles
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle form submissions
    document.querySelectorAll('.account-categories-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const accountId = this.dataset.accountId;
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            
            // Show loading state
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Updating...';
            submitBtn.disabled = true;
            
            fetch('{% url "update-account-categories" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    
                    // Update the current categories display
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'An error occurred while updating categories.');
            })
            .finally(() => {
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(
        alertDiv, 
        document.querySelector('.row')
    );
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<style>
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.75em;
}

.form-check-label {
    cursor: pointer;
}

.form-check-label:hover {
    color: #0d6efd;
}
</style>
{% endblock %}
