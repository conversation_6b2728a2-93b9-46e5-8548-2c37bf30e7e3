"""
Image Processing Module for Imagsta
Handles image optimization, resizing, format conversion, and cloud storage.
"""

import logging
import os
from typing import Op<PERSON>, <PERSON><PERSON>, Dict
from decouple import config
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile

logger = logging.getLogger(__name__)

# Try to import image processing libraries
try:
    from PIL import Image, ImageOps
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logger.warning("Pillow not installed. Install with: pip install Pillow")

try:
    import cloudinary
    import cloudinary.uploader
    CLOUDINARY_AVAILABLE = True
except ImportError:
    CLOUDINARY_AVAILABLE = False
    logger.warning("Cloudinary not installed. Install with: pip install cloudinary")


class ImageProcessor:
    """Service for image processing and optimization."""
    
    def __init__(self):
        self.max_size = (1080, 1080)  # Instagram optimal size
        self.quality = 85
        self.format = 'JPEG'
        
        # Configure Cloudinary if available
        if CLOUDINARY_AVAILABLE:
            cloudinary.config(
                cloud_name=config('CLOUDINARY_CLOUD_NAME', default=''),
                api_key=config('CLOUDINARY_API_KEY', default=''),
                api_secret=config('CLOUDINARY_API_SECRET', default='')
            )
            self.cloudinary_configured = bool(
                config('CLOUDINARY_CLOUD_NAME', default='') and
                config('CLOUDINARY_API_KEY', default='') and
                config('CLOUDINARY_API_SECRET', default='')
            )
        else:
            self.cloudinary_configured = False
    
    def optimize_image(self, image_path: str, output_path: Optional[str] = None) -> Optional[str]:
        """Optimize image for social media posting."""
        if not PIL_AVAILABLE:
            logger.warning("PIL not available, skipping image optimization")
            return image_path
        
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'P'):
                    img = img.convert('RGB')
                
                # Auto-orient based on EXIF data
                img = ImageOps.exif_transpose(img)
                
                # Resize if too large
                if img.size[0] > self.max_size[0] or img.size[1] > self.max_size[1]:
                    img.thumbnail(self.max_size, Image.Resampling.LANCZOS)
                
                # Save optimized image
                if not output_path:
                    output_path = image_path
                
                img.save(output_path, format=self.format, quality=self.quality, optimize=True)
                
                logger.info(f"Image optimized: {image_path} -> {output_path}")
                return output_path
                
        except Exception as e:
            logger.error(f"Error optimizing image {image_path}: {e}")
            return None
    
    def resize_image(self, image_path: str, size: Tuple[int, int], output_path: Optional[str] = None) -> Optional[str]:
        """Resize image to specific dimensions."""
        if not PIL_AVAILABLE:
            return image_path
        
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'P'):
                    img = img.convert('RGB')
                
                # Resize image
                resized_img = img.resize(size, Image.Resampling.LANCZOS)
                
                # Save resized image
                if not output_path:
                    base, ext = os.path.splitext(image_path)
                    output_path = f"{base}_resized{ext}"
                
                resized_img.save(output_path, format=self.format, quality=self.quality)
                
                logger.info(f"Image resized: {image_path} -> {output_path} ({size})")
                return output_path
                
        except Exception as e:
            logger.error(f"Error resizing image {image_path}: {e}")
            return None
    
    def create_thumbnail(self, image_path: str, size: Tuple[int, int] = (300, 300)) -> Optional[str]:
        """Create a thumbnail of the image."""
        if not PIL_AVAILABLE:
            return image_path
        
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'P'):
                    img = img.convert('RGB')
                
                # Create thumbnail
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # Save thumbnail
                base, ext = os.path.splitext(image_path)
                thumbnail_path = f"{base}_thumb{ext}"
                
                img.save(thumbnail_path, format=self.format, quality=self.quality)
                
                logger.info(f"Thumbnail created: {image_path} -> {thumbnail_path}")
                return thumbnail_path
                
        except Exception as e:
            logger.error(f"Error creating thumbnail for {image_path}: {e}")
            return None
    
    def upload_to_cloud(self, image_path: str, public_id: Optional[str] = None) -> Optional[str]:
        """Upload image to cloud storage (Cloudinary)."""
        if not self.cloudinary_configured:
            logger.warning("Cloudinary not configured, skipping cloud upload")
            return None
        
        try:
            # Upload to Cloudinary
            result = cloudinary.uploader.upload(
                image_path,
                public_id=public_id,
                quality="auto",
                fetch_format="auto",
                crop="limit",
                width=1080,
                height=1080
            )
            
            url = result.get('secure_url')
            logger.info(f"Image uploaded to cloud: {image_path} -> {url}")
            return url
            
        except Exception as e:
            logger.error(f"Error uploading image to cloud: {e}")
            return None
    
    def get_image_info(self, image_path: str) -> Dict:
        """Get information about an image."""
        info = {
            'size': None,
            'format': None,
            'mode': None,
            'file_size': None
        }
        
        try:
            # Get file size
            info['file_size'] = os.path.getsize(image_path)
            
            if PIL_AVAILABLE:
                with Image.open(image_path) as img:
                    info['size'] = img.size
                    info['format'] = img.format
                    info['mode'] = img.mode
            
        except Exception as e:
            logger.error(f"Error getting image info for {image_path}: {e}")
        
        return info
    
    def validate_image(self, image_path: str) -> Dict:
        """Validate image for social media posting."""
        validation = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            info = self.get_image_info(image_path)
            
            # Check file size (max 10MB)
            if info['file_size'] and info['file_size'] > 10 * 1024 * 1024:
                validation['errors'].append("File size exceeds 10MB limit")
                validation['valid'] = False
            
            # Check dimensions
            if info['size']:
                width, height = info['size']
                if width < 320 or height < 320:
                    validation['warnings'].append("Image resolution is quite low")
                elif width > 2048 or height > 2048:
                    validation['warnings'].append("Image resolution is very high, consider resizing")
            
            # Check format
            if info['format'] and info['format'] not in ['JPEG', 'PNG', 'GIF', 'WEBP']:
                validation['errors'].append(f"Unsupported format: {info['format']}")
                validation['valid'] = False
            
        except Exception as e:
            validation['errors'].append(f"Error validating image: {e}")
            validation['valid'] = False
        
        return validation
    
    def process_for_platform(self, image_path: str, platform: str) -> Optional[str]:
        """Process image for specific social media platform."""
        platform_specs = {
            'instagram': {'size': (1080, 1080), 'format': 'JPEG'},
            'twitter': {'size': (1200, 675), 'format': 'JPEG'},
            'linkedin': {'size': (1200, 627), 'format': 'JPEG'},
            'facebook': {'size': (1200, 630), 'format': 'JPEG'}
        }
        
        spec = platform_specs.get(platform.lower())
        if not spec:
            logger.warning(f"Unknown platform: {platform}")
            return self.optimize_image(image_path)
        
        # Create platform-specific version
        base, ext = os.path.splitext(image_path)
        output_path = f"{base}_{platform}{ext}"
        
        # Resize for platform
        resized_path = self.resize_image(image_path, spec['size'], output_path)
        
        return resized_path


# Singleton instance
image_processor = ImageProcessor()


def optimize_uploaded_image(image_file, user_id: int) -> Optional[str]:
    """Optimize an uploaded image file."""
    try:
        # Save uploaded file temporarily
        temp_path = default_storage.save(
            f'temp/{user_id}_{image_file.name}',
            ContentFile(image_file.read())
        )
        
        full_temp_path = default_storage.path(temp_path)
        
        # Optimize the image
        optimized_path = image_processor.optimize_image(full_temp_path)
        
        if optimized_path:
            # Upload to cloud if configured
            cloud_url = image_processor.upload_to_cloud(optimized_path)
            
            # Clean up temp file
            default_storage.delete(temp_path)
            
            return cloud_url or optimized_path
        
        return None
        
    except Exception as e:
        logger.error(f"Error optimizing uploaded image: {e}")
        return None


def create_image_variants(image_path: str) -> Dict[str, str]:
    """Create variants of an image for different platforms."""
    variants = {}
    
    platforms = ['instagram', 'twitter', 'linkedin', 'facebook']
    
    for platform in platforms:
        variant_path = image_processor.process_for_platform(image_path, platform)
        if variant_path:
            # Upload variant to cloud
            cloud_url = image_processor.upload_to_cloud(variant_path, f"{platform}_{os.path.basename(variant_path)}")
            variants[platform] = cloud_url or variant_path
    
    return variants
