"""
Multi-platform social media integration for Imagsta.
This module provides unified interfaces for posting to different social media platforms.
"""

import logging
import requests
import json
from abc import ABC, abstractmethod
from typing import Dict, Optional, List
from django.conf import settings
from decouple import config

logger = logging.getLogger(__name__)


class SocialPlatform(ABC):
    """Abstract base class for social media platforms."""
    
    @abstractmethod
    def authenticate(self) -> bool:
        """Authenticate with the platform."""
        pass
    
    @abstractmethod
    def post_content(self, content: Dict) -> Dict:
        """Post content to the platform."""
        pass
    
    @abstractmethod
    def get_analytics(self, post_id: str) -> Dict:
        """Get analytics for a specific post."""
        pass


class InstagramPlatform(SocialPlatform):
    """Instagram platform integration."""
    
    def __init__(self):
        self.access_token = config('FACEBOOK_ACCESS_TOKEN', default='')
        self.account_id = config('INSTAGRAM_ACCOUNT_ID', default='')
        self.graph_version = config('FACEBOOK_GRAPH_VERSION', default='v18.0')
        self.base_url = f'https://graph.facebook.com/{self.graph_version}'
    
    def authenticate(self) -> bool:
        """Check if Instagram credentials are valid."""
        if not self.access_token or not self.account_id:
            return False
        
        try:
            url = f"{self.base_url}/{self.account_id}"
            params = {'access_token': self.access_token}
            response = requests.get(url, params=params, timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Instagram authentication failed: {e}")
            return False
    
    def post_content(self, content: Dict) -> Dict:
        """Post content to Instagram."""
        try:
            # Create media object
            media_url = f"{self.base_url}/{self.account_id}/media"
            media_params = {
                'image_url': content['image_url'],
                'caption': content.get('caption', ''),
                'access_token': self.access_token
            }
            
            media_response = requests.post(media_url, data=media_params, timeout=30)
            if media_response.status_code != 200:
                return {'error': f'Failed to create media: {media_response.text}'}
            
            media_id = media_response.json().get('id')
            
            # Publish media
            publish_url = f"{self.base_url}/{self.account_id}/media_publish"
            publish_params = {
                'creation_id': media_id,
                'access_token': self.access_token
            }
            
            publish_response = requests.post(publish_url, data=publish_params, timeout=30)
            if publish_response.status_code != 200:
                return {'error': f'Failed to publish: {publish_response.text}'}
            
            return {
                'success': True,
                'post_id': publish_response.json().get('id'),
                'platform': 'instagram'
            }
            
        except Exception as e:
            logger.error(f"Instagram posting failed: {e}")
            return {'error': str(e)}
    
    def get_analytics(self, post_id: str) -> Dict:
        """Get Instagram post analytics."""
        try:
            url = f"{self.base_url}/{post_id}/insights"
            params = {
                'metric': 'engagement,impressions,reach',
                'access_token': self.access_token
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': 'Failed to fetch analytics'}
                
        except Exception as e:
            logger.error(f"Instagram analytics failed: {e}")
            return {'error': str(e)}


class TwitterPlatform(SocialPlatform):
    """Twitter platform integration."""
    
    def __init__(self):
        self.api_key = config('TWITTER_API_KEY', default='')
        self.api_secret = config('TWITTER_API_SECRET', default='')
        self.access_token = config('TWITTER_ACCESS_TOKEN', default='')
        self.access_token_secret = config('TWITTER_ACCESS_TOKEN_SECRET', default='')
        self.bearer_token = config('TWITTER_BEARER_TOKEN', default='')
    
    def authenticate(self) -> bool:
        """Check if Twitter credentials are valid."""
        if not self.bearer_token:
            return False
        
        try:
            url = "https://api.twitter.com/2/users/me"
            headers = {'Authorization': f'Bearer {self.bearer_token}'}
            response = requests.get(url, headers=headers, timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Twitter authentication failed: {e}")
            return False
    
    def post_content(self, content: Dict) -> Dict:
        """Post content to Twitter."""
        try:
            url = "https://api.twitter.com/2/tweets"
            headers = {
                'Authorization': f'Bearer {self.bearer_token}',
                'Content-Type': 'application/json'
            }
            
            tweet_data = {
                'text': content.get('text', '')[:280]  # Twitter character limit
            }
            
            # Add media if provided
            if content.get('media_ids'):
                tweet_data['media'] = {'media_ids': content['media_ids']}
            
            response = requests.post(url, headers=headers, json=tweet_data, timeout=30)
            
            if response.status_code == 201:
                return {
                    'success': True,
                    'post_id': response.json()['data']['id'],
                    'platform': 'twitter'
                }
            else:
                return {'error': f'Failed to post tweet: {response.text}'}
                
        except Exception as e:
            logger.error(f"Twitter posting failed: {e}")
            return {'error': str(e)}
    
    def get_analytics(self, post_id: str) -> Dict:
        """Get Twitter post analytics."""
        try:
            url = f"https://api.twitter.com/2/tweets/{post_id}"
            headers = {'Authorization': f'Bearer {self.bearer_token}'}
            params = {
                'tweet.fields': 'public_metrics,created_at',
                'expansions': 'author_id'
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': 'Failed to fetch analytics'}
                
        except Exception as e:
            logger.error(f"Twitter analytics failed: {e}")
            return {'error': str(e)}


class LinkedInPlatform(SocialPlatform):
    """LinkedIn platform integration."""
    
    def __init__(self):
        self.access_token = config('LINKEDIN_ACCESS_TOKEN', default='')
        self.person_id = config('LINKEDIN_PERSON_ID', default='')
    
    def authenticate(self) -> bool:
        """Check if LinkedIn credentials are valid."""
        if not self.access_token:
            return False
        
        try:
            url = "https://api.linkedin.com/v2/people/~"
            headers = {'Authorization': f'Bearer {self.access_token}'}
            response = requests.get(url, headers=headers, timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"LinkedIn authentication failed: {e}")
            return False
    
    def post_content(self, content: Dict) -> Dict:
        """Post content to LinkedIn."""
        try:
            url = "https://api.linkedin.com/v2/ugcPosts"
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            post_data = {
                "author": f"urn:li:person:{self.person_id}",
                "lifecycleState": "PUBLISHED",
                "specificContent": {
                    "com.linkedin.ugc.ShareContent": {
                        "shareCommentary": {
                            "text": content.get('text', '')
                        },
                        "shareMediaCategory": "NONE"
                    }
                },
                "visibility": {
                    "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
                }
            }
            
            response = requests.post(url, headers=headers, json=post_data, timeout=30)
            
            if response.status_code == 201:
                return {
                    'success': True,
                    'post_id': response.json().get('id'),
                    'platform': 'linkedin'
                }
            else:
                return {'error': f'Failed to post to LinkedIn: {response.text}'}
                
        except Exception as e:
            logger.error(f"LinkedIn posting failed: {e}")
            return {'error': str(e)}
    
    def get_analytics(self, post_id: str) -> Dict:
        """Get LinkedIn post analytics."""
        # LinkedIn analytics require additional permissions
        return {'error': 'Analytics not available for LinkedIn'}


class SocialMediaManager:
    """Unified manager for all social media platforms."""
    
    def __init__(self):
        self.platforms = {
            'instagram': InstagramPlatform(),
            'twitter': TwitterPlatform(),
            'linkedin': LinkedInPlatform()
        }
    
    def get_platform(self, platform_name: str) -> Optional[SocialPlatform]:
        """Get a specific platform instance."""
        return self.platforms.get(platform_name.lower())
    
    def get_available_platforms(self) -> List[str]:
        """Get list of platforms with valid authentication."""
        available = []
        for name, platform in self.platforms.items():
            if platform.authenticate():
                available.append(name)
        return available
    
    def post_to_multiple_platforms(self, content: Dict, platforms: List[str]) -> Dict:
        """Post content to multiple platforms simultaneously."""
        results = {}
        
        for platform_name in platforms:
            platform = self.get_platform(platform_name)
            if platform and platform.authenticate():
                result = platform.post_content(content)
                results[platform_name] = result
            else:
                results[platform_name] = {'error': 'Platform not authenticated'}
        
        return results
    
    def get_unified_analytics(self, post_data: Dict) -> Dict:
        """Get analytics from all platforms for a post."""
        analytics = {}
        
        for platform_name, post_id in post_data.items():
            if post_id:
                platform = self.get_platform(platform_name)
                if platform:
                    analytics[platform_name] = platform.get_analytics(post_id)
        
        return analytics


# Singleton instance
social_manager = SocialMediaManager()
