{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-rss"></i> News Sources
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'news-dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                        <i class="bi bi-plus"></i> Add Source
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Sources List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list"></i> Configured Sources
                    </h5>
                </div>
                <div class="card-body">
                    {% if sources %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Categories</th>
                                        <th>Articles</th>
                                        <th>Last Fetch</th>
                                        <th>Health</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for source in sources %}
                                    <tr>
                                        <td>
                                            <strong>{{ source.name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ source.url|truncatechars:50 }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ source.get_source_type_display }}</span>
                                        </td>
                                        <td>
                                            {% if source.is_active %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% for category in source.categories.all %}
                                                <span class="badge bg-secondary me-1">{{ category.display_name }}</span>
                                            {% empty %}
                                                <span class="text-muted">None</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <strong>{{ source.articles_count }}</strong>
                                                <br>
                                                <small class="text-muted">
                                                    {{ source.recent_articles_count }} this week
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if source.last_fetch %}
                                                <div>
                                                    {{ source.last_fetch|timesince }} ago
                                                </div>
                                                {% if source.last_success %}
                                                    <small class="text-success">
                                                        Last success: {{ source.last_success|timesince }} ago
                                                    </small>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">Never</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% with health=source.get_health_status %}
                                                {% if health == 'healthy' %}
                                                    <span class="badge bg-success">Healthy</span>
                                                {% elif health == 'stale' %}
                                                    <span class="badge bg-warning">Stale</span>
                                                {% elif health == 'unhealthy' %}
                                                    <span class="badge bg-danger">Unhealthy</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Never Fetched</span>
                                                {% endif %}
                                            {% endwith %}
                                            
                                            {% if source.error_count > 0 %}
                                                <br>
                                                <small class="text-danger">
                                                    {{ source.error_count }} errors
                                                </small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group-vertical btn-group-sm" role="group">
                                                <button class="btn btn-outline-primary toggle-source-btn" 
                                                        data-source-id="{{ source.id }}"
                                                        data-current-status="{{ source.is_active|yesno:'true,false' }}">
                                                    {% if source.is_active %}
                                                        <i class="bi bi-pause"></i> Disable
                                                    {% else %}
                                                        <i class="bi bi-play"></i> Enable
                                                    {% endif %}
                                                </button>
                                                <button class="btn btn-outline-info" 
                                                        onclick="fetchFromSource({{ source.id }})">
                                                    <i class="bi bi-arrow-clockwise"></i> Fetch Now
                                                </button>
                                                <a href="/admin/images/newssource/{{ source.id }}/change/" 
                                                   class="btn btn-outline-secondary" target="_blank">
                                                    <i class="bi bi-gear"></i> Edit
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-rss text-muted" style="font-size: 4rem;"></i>
                            <h3 class="text-muted mt-3">No news sources configured</h3>
                            <p class="text-muted">Add your first news source to start aggregating content.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                                <i class="bi bi-plus"></i> Add Your First Source
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ sources|length }}</h4>
                    <p class="mb-0">Total Sources</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ sources|dictsort:"is_active"|slice:":1"|length }}</h4>
                    <p class="mb-0">Active Sources</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4>{{ categories|length }}</h4>
                    <p class="mb-0">Categories</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h4 id="totalArticles">-</h4>
                    <p class="mb-0">Total Articles</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Source Modal -->
<div class="modal fade" id="addSourceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add News Source</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Note:</strong> For now, please use the Django admin panel to add and configure news sources. 
                    This provides more detailed configuration options.
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Supported Source Types:</h6>
                        <ul>
                            <li><strong>RSS Feeds:</strong> Most news websites provide RSS feeds</li>
                            <li><strong>Reddit:</strong> Subreddit URLs (e.g., /r/technology)</li>
                            <li><strong>News API:</strong> NewsAPI.org integration</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Popular RSS Feeds:</h6>
                        <ul>
                            <li>TechCrunch: https://techcrunch.com/feed/</li>
                            <li>BBC Tech: http://feeds.bbci.co.uk/news/technology/rss.xml</li>
                            <li>Reuters: http://feeds.reuters.com/reuters/technologyNews</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="/admin/images/newssource/add/" class="btn btn-primary" target="_blank">
                    <i class="bi bi-plus"></i> Add in Admin Panel
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate total articles
    let totalArticles = 0;
    {% for source in sources %}
        totalArticles += {{ source.articles_count }};
    {% endfor %}
    document.getElementById('totalArticles').textContent = totalArticles;
    
    // Handle toggle source buttons
    document.querySelectorAll('.toggle-source-btn').forEach(button => {
        button.addEventListener('click', function() {
            const sourceId = this.dataset.sourceId;
            const currentStatus = this.dataset.currentStatus === 'true';
            
            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
            this.disabled = true;
            
            fetch('{% url "toggle-news-source" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: `source_id=${sourceId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update button text and status
                    if (data.is_active) {
                        this.innerHTML = '<i class="bi bi-pause"></i> Disable';
                        this.dataset.currentStatus = 'true';
                    } else {
                        this.innerHTML = '<i class="bi bi-play"></i> Enable';
                        this.dataset.currentStatus = 'false';
                    }
                    
                    // Show success message
                    showAlert('success', data.message);
                    
                    // Reload page after a short delay to update status badges
                    setTimeout(() => location.reload(), 1500);
                } else {
                    this.innerHTML = originalText;
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.innerHTML = originalText;
                showAlert('danger', 'An error occurred while updating the source.');
            })
            .finally(() => {
                this.disabled = false;
            });
        });
    });
});

function fetchFromSource(sourceId) {
    showAlert('info', 'Fetching articles from source... This may take a moment.');
    
    // In a real implementation, you would call a fetch endpoint
    // For now, we'll just show a message
    setTimeout(() => {
        showAlert('success', 'Fetch request initiated. Check the source status in a few minutes.');
    }, 2000);
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(
        alertDiv, 
        document.querySelector('.row')
    );
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
}

.btn-group-vertical .btn {
    margin-bottom: 2px;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
