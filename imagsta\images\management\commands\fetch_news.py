"""
Management command to fetch news from all configured sources.
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from images.models import NewsSource, Category
from images.news_services import NewsAggregatorService
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Fetch news articles from all configured sources'

    def add_arguments(self, parser):
        parser.add_argument(
            '--source',
            type=str,
            help='Fetch from specific source by name',
        )
        parser.add_argument(
            '--create-sample-sources',
            action='store_true',
            help='Create sample news sources for testing',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force fetch even if not due based on interval',
        )

    def handle(self, *args, **options):
        if options['create_sample_sources']:
            self.create_sample_sources()
            return

        aggregator = NewsAggregatorService()
        
        if options['source']:
            # Fetch from specific source
            try:
                source = NewsSource.objects.get(name=options['source'])
                if options['force'] or aggregator._should_fetch_from_source(source):
                    count = aggregator.fetch_from_source(source)
                    self.stdout.write(
                        self.style.SUCCESS(f'Fetched {count} articles from {source.name}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Source {source.name} not due for fetching')
                    )
            except NewsSource.DoesNotExist:
                raise CommandError(f'Source "{options["source"]}" does not exist')
        else:
            # Fetch from all sources
            if options['force']:
                # Override the should_fetch check for all sources
                sources = NewsSource.objects.filter(is_active=True)
                results = {}
                for source in sources:
                    count = aggregator.fetch_from_source(source)
                    results[source.name] = count
            else:
                results = aggregator.fetch_from_all_sources()
            
            total_articles = sum(results.values())
            
            if results:
                self.stdout.write(self.style.SUCCESS(f'Fetched {total_articles} total articles:'))
                for source_name, count in results.items():
                    self.stdout.write(f'  - {source_name}: {count} articles')
            else:
                self.stdout.write(self.style.WARNING('No sources were due for fetching'))

    def create_sample_sources(self):
        """Create sample news sources for testing."""
        self.stdout.write('Creating sample news sources...')
        
        # Create categories first
        categories_data = [
            ('technology', 'Technology', 'Latest tech news and innovations'),
            ('business', 'Business', 'Business and finance news'),
            ('entertainment', 'Entertainment', 'Entertainment and celebrity news'),
            ('sports', 'Sports', 'Sports news and updates'),
            ('science', 'Science', 'Science and research news'),
        ]
        
        created_categories = {}
        for name, display_name, description in categories_data:
            category, created = Category.objects.get_or_create(
                name=name,
                defaults={
                    'display_name': display_name,
                    'description': description,
                }
            )
            created_categories[name] = category
            if created:
                self.stdout.write(f'Created category: {display_name}')
        
        # Create sample news sources
        sources_data = [
            {
                'name': 'TechCrunch RSS',
                'source_type': 'rss',
                'url': 'https://techcrunch.com/feed/',
                'categories': ['technology'],
                'fetch_interval': 60,
            },
            {
                'name': 'BBC Technology RSS',
                'source_type': 'rss',
                'url': 'http://feeds.bbci.co.uk/news/technology/rss.xml',
                'categories': ['technology'],
                'fetch_interval': 120,
            },
            {
                'name': 'Reddit Technology',
                'source_type': 'reddit',
                'url': 'https://www.reddit.com/r/technology',
                'categories': ['technology'],
                'fetch_interval': 180,
                'max_articles_per_fetch': 25,
            },
            {
                'name': 'Reddit Programming',
                'source_type': 'reddit',
                'url': 'https://www.reddit.com/r/programming',
                'categories': ['technology'],
                'fetch_interval': 180,
                'max_articles_per_fetch': 20,
            },
            {
                'name': 'ESPN RSS',
                'source_type': 'rss',
                'url': 'https://www.espn.com/espn/rss/news',
                'categories': ['sports'],
                'fetch_interval': 90,
            },
            {
                'name': 'Science Daily RSS',
                'source_type': 'rss',
                'url': 'https://www.sciencedaily.com/rss/all.xml',
                'categories': ['science'],
                'fetch_interval': 240,
            },
        ]
        
        for source_data in sources_data:
            categories = source_data.pop('categories', [])
            
            source, created = NewsSource.objects.get_or_create(
                name=source_data['name'],
                defaults=source_data
            )
            
            if created:
                # Add categories
                for cat_name in categories:
                    if cat_name in created_categories:
                        source.categories.add(created_categories[cat_name])
                
                self.stdout.write(f'Created source: {source.name}')
            else:
                self.stdout.write(f'Source already exists: {source.name}')
        
        self.stdout.write(self.style.SUCCESS('Sample sources created successfully!'))
        self.stdout.write('You can now run: python manage.py fetch_news')
