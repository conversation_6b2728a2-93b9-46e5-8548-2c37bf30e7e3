"""
Management command for automated posting of highlighted news articles.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from images.auto_posting_service import auto_posting_service
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Automatically post highlighted news articles to configured social media accounts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--max-posts',
            type=int,
            default=10,
            help='Maximum number of posts to create in this run',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be posted without actually creating posts',
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show auto-posting statistics',
        )

    def handle(self, *args, **options):
        if options['stats']:
            self.show_stats()
            return

        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No posts will be created')
            )

        self.stdout.write('Starting auto-posting process...')
        
        try:
            if options['dry_run']:
                # In dry run mode, just show what would be posted
                self.dry_run_auto_posting(options['max_posts'])
            else:
                # Actually create the posts
                stats = auto_posting_service.process_auto_posting(
                    max_posts_per_run=options['max_posts']
                )
                
                self.display_results(stats)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error in auto-posting: {e}')
            )
            logger.error(f"Auto-posting command error: {e}")

    def dry_run_auto_posting(self, max_posts):
        """Show what would be posted in dry run mode."""
        from images.models import AIHighlight, AccountCategoryMapping
        from datetime import timedelta
        
        # Get eligible highlights (same logic as in service)
        cutoff_time = timezone.now() - timedelta(hours=24)
        
        eligible_highlights = AIHighlight.objects.filter(
            was_posted=False,
            highlight_score__gte=60,
            article__published_date__gte=cutoff_time,
            created__gte=cutoff_time
        ).select_related('article').order_by('-highlight_score')[:max_posts]
        
        if not eligible_highlights:
            self.stdout.write(
                self.style.WARNING('No eligible highlights found for auto-posting')
            )
            return
        
        self.stdout.write(f'\nFound {len(eligible_highlights)} eligible highlights:\n')
        
        for i, highlight in enumerate(eligible_highlights, 1):
            article = highlight.article
            
            # Get eligible accounts
            article_categories = article.categories.all()
            eligible_mappings = AccountCategoryMapping.objects.filter(
                category__in=article_categories,
                auto_post=True,
                min_highlight_score__lte=highlight.highlight_score,
                account__status='connected'
            ).select_related('account', 'category')
            
            self.stdout.write(f'{i}. {article.title[:60]}...')
            self.stdout.write(f'   Score: {highlight.highlight_score:.1f}')
            self.stdout.write(f'   Categories: {", ".join([cat.display_name for cat in article_categories])}')
            self.stdout.write(f'   Source: {article.source.name}')
            
            if eligible_mappings:
                self.stdout.write(f'   Would post to:')
                for mapping in eligible_mappings[:3]:
                    self.stdout.write(f'     - {mapping.account.platform} @{mapping.account.username}')
            else:
                self.stdout.write(f'   No eligible accounts found')
            
            self.stdout.write('')

    def display_results(self, stats):
        """Display the results of auto-posting."""
        self.stdout.write('\n' + '='*50)
        self.stdout.write('AUTO-POSTING RESULTS')
        self.stdout.write('='*50)
        
        self.stdout.write(f'📊 Processed highlights: {stats["processed_highlights"]}')
        self.stdout.write(f'✅ Created posts: {stats["created_posts"]}')
        self.stdout.write(f'⏭️  Skipped posts: {stats["skipped_posts"]}')
        self.stdout.write(f'❌ Errors: {stats["errors"]}')
        
        if stats['created_posts'] > 0:
            self.stdout.write(
                self.style.SUCCESS(f'\n🎉 Successfully created {stats["created_posts"]} auto-posts!')
            )
        elif stats['processed_highlights'] == 0:
            self.stdout.write(
                self.style.WARNING('\n⏳ No eligible highlights found for auto-posting')
            )
        else:
            self.stdout.write(
                self.style.WARNING('\n⚠️  No posts were created (check account configurations)')
            )

    def show_stats(self):
        """Show auto-posting statistics."""
        self.stdout.write('Fetching auto-posting statistics...\n')
        
        try:
            stats = auto_posting_service.get_auto_posting_stats(days=7)
            
            self.stdout.write('📈 AUTO-POSTING STATISTICS (Last 7 days)')
            self.stdout.write('='*50)
            
            self.stdout.write(f'Total auto-posts: {stats["total_auto_posts"]}')
            self.stdout.write(f'Auto-enabled accounts: {stats["auto_enabled_accounts"]}')
            self.stdout.write(f'Average posts per day: {stats["avg_posts_per_day"]:.1f}')
            
            self.stdout.write('\n🏆 Top Performing Categories:')
            for cat in stats['top_performing_categories']:
                self.stdout.write(f'  • {cat["category"]}: {cat["posts"]} posts (avg engagement: {cat["avg_engagement"]:.1f}%)')
            
            self.stdout.write('\n📱 Account Performance:')
            for acc in stats['account_performance'][:5]:
                self.stdout.write(f'  • {acc["account"]}: {acc["posts_count"]} posts (avg engagement: {acc["avg_engagement"]:.1f}%)')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error fetching statistics: {e}')
            )
