{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-newspaper"></i> News Dashboard
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'news-highlights' %}" class="btn btn-outline-primary">
                        <i class="bi bi-star"></i> View All Highlights
                    </a>
                    <a href="{% url 'news-sources' %}" class="btn btn-outline-info">
                        <i class="bi bi-rss"></i> Manage Sources
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.total_articles }}</h4>
                            <p class="card-text">Articles Today</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-file-text fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.highlighted_articles }}</h4>
                            <p class="card-text">AI Highlights</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-star fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.active_sources }}</h4>
                            <p class="card-text">Active Sources</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-rss fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.categories_count }}</h4>
                            <p class="card-text">Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-tags fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Highlights Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-star-fill text-warning"></i> AI Highlighted Articles
                    </h5>
                    <a href="{% url 'news-highlights' %}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    {% if highlights %}
                        <div class="row">
                            {% for highlight in highlights %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100 border-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="badge bg-warning text-dark">
                                                Score: {{ highlight.highlight_score|floatformat:1 }}
                                            </span>
                                            <small class="text-muted">
                                                {{ highlight.article.published_date|timesince }} ago
                                            </small>
                                        </div>
                                        
                                        <h6 class="card-title">
                                            <a href="{{ highlight.article.url }}" target="_blank" class="text-decoration-none">
                                                {{ highlight.article.title|truncatechars:80 }}
                                            </a>
                                        </h6>
                                        
                                        <p class="card-text small text-muted">
                                            {{ highlight.article.content|truncatechars:120 }}
                                        </p>
                                        
                                        <div class="mb-2">
                                            {% for category in highlight.article.categories.all %}
                                                <span class="badge bg-secondary me-1">{{ category.display_name }}</span>
                                            {% endfor %}
                                        </div>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-rss"></i> {{ highlight.article.source.name }}
                                            </small>
                                            <button class="btn btn-sm btn-primary create-post-btn" 
                                                    data-article-id="{{ highlight.article.id }}"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#createPostModal">
                                                <i class="bi bi-plus"></i> Create Post
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-star text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-2">No highlights yet</h5>
                            <p class="text-muted">AI will analyze articles and highlight the best ones for posting.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- News by Category -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-grid"></i> Recent News by Category
                    </h5>
                </div>
                <div class="card-body">
                    {% if category_articles %}
                        <div class="row">
                            {% for category, articles in category_articles.items %}
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">
                                            <i class="{{ category.icon }}"></i> {{ category.display_name }}
                                        </h6>
                                    </div>
                                    <div class="card-body p-2">
                                        {% for article in articles %}
                                        <div class="border-bottom py-2">
                                            <h6 class="mb-1">
                                                <a href="{{ article.url }}" target="_blank" class="text-decoration-none">
                                                    {{ article.title|truncatechars:60 }}
                                                </a>
                                            </h6>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    {{ article.published_date|timesince }} ago
                                                </small>
                                                {% if article.is_highlighted %}
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="bi bi-star"></i>
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-newspaper text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-2">No recent articles</h5>
                            <p class="text-muted">Check your news sources or fetch new articles.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Post Modal -->
<div class="modal fade" id="createPostModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Post from Article</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPostForm">
                    {% csrf_token %}
                    <input type="hidden" id="articleId" name="article_id">
                    
                    <div class="mb-3">
                        <label for="customCaption" class="form-label">Caption</label>
                        <textarea class="form-control" id="customCaption" name="custom_caption" rows="4" 
                                  placeholder="AI-generated caption will appear here..."></textarea>
                    </div>
                    
                    {% if connected_accounts %}
                    <div class="mb-3">
                        <label class="form-label">Post to Accounts (optional)</label>
                        {% for account_data in connected_accounts %}
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" 
                                   name="account_ids" value="{{ account_data.account.id }}" 
                                   id="account{{ account_data.account.id }}">
                            <label class="form-check-label" for="account{{ account_data.account.id }}">
                                <i class="{{ account_data.account.get_platform_icon }}"></i>
                                {{ account_data.account.platform|title }} - @{{ account_data.account.username }}
                                {% if account_data.categories %}
                                    <small class="text-muted">
                                        ({{ account_data.categories|join:", " }})
                                    </small>
                                {% endif %}
                            </label>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="createPostBtn">Create Post</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle create post button clicks
    document.querySelectorAll('.create-post-btn').forEach(button => {
        button.addEventListener('click', function() {
            const articleId = this.dataset.articleId;
            document.getElementById('articleId').value = articleId;
            
            // You could fetch and populate the suggested caption here
            // For now, we'll leave it empty for user input
        });
    });
    
    // Handle create post form submission
    document.getElementById('createPostBtn').addEventListener('click', function() {
        const form = document.getElementById('createPostForm');
        const formData = new FormData(form);
        
        fetch('{% url "create-post-from-news" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal and show success message
                bootstrap.Modal.getInstance(document.getElementById('createPostModal')).hide();
                
                // Show success message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show';
                alertDiv.innerHTML = `
                    <i class="bi bi-check-circle"></i> ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));
                
                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the post.');
        });
    });
});
</script>

<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.border-warning {
    border-color: #ffc107 !important;
    border-width: 2px !important;
}

.create-post-btn {
    transition: all 0.2s;
}

.create-post-btn:hover {
    transform: scale(1.05);
}
</style>
{% endblock %}
