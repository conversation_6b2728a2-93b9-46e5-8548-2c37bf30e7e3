from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import (
    Post, Search, Result, User, PostAnalytics, UserAnalytics,
    ContentTemplate, SavedHashtagSet, ScheduledPost,
    SocialMediaAccount, PostPublication, Category, NewsSource,
    NewsArticle, AIHighlight, AccountCategoryMapping, NewsFeedPreference
)


admin.site.register(User, BaseUserAdmin)


class ResultInline(admin.TabularInline):
    """Inline admin for Results."""
    model = Result
    extra = 0
    readonly_fields = ['created', 'updated']
    fields = ['image', 'dis_image', 'created']


@admin.register(Search)
class SearchAdmin(admin.ModelAdmin):
    """Enhanced Search admin with better display and filtering."""
    list_display = ['query', 'result_count', 'created', 'updated']
    list_filter = ['created', 'updated']
    search_fields = ['query']
    ordering = ['-updated']
    readonly_fields = ['created', 'updated']
    inlines = [ResultInline]

    def result_count(self, obj):
        """Display the number of results for this search."""
        return obj.results.count()
    result_count.short_description = 'Results'


@admin.register(Result)
class ResultAdmin(admin.ModelAdmin):
    """Enhanced Result admin with better display and filtering."""
    list_display = ['id', 'query', 'image_preview', 'created']
    list_filter = ['created', 'query']
    search_fields = ['query__query']
    ordering = ['-created']
    readonly_fields = ['created', 'updated', 'image_preview']

    def image_preview(self, obj):
        """Display a small preview of the image."""
        if obj.dis_image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.dis_image
            )
        return "No preview"
    image_preview.short_description = 'Preview'


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    """Enhanced Post admin with better display and filtering."""
    list_display = ['id', 'user', 'posted', 'image_preview', 'created']
    list_filter = ['posted', 'created', 'user']
    search_fields = ['user__username', 'caption', 'post_text', 'hashtags']
    ordering = ['-created']
    readonly_fields = ['created', 'updated', 'image_preview']

    fieldsets = (
        ('Basic Info', {
            'fields': ('user', 'posted')
        }),
        ('Content', {
            'fields': ('image', 'image_preview', 'caption', 'post_text', 'hashtags')
        }),
        ('Instagram Info', {
            'fields': ('published_image_id',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created', 'updated'),
            'classes': ('collapse',)
        }),
    )

    def image_preview(self, obj):
        """Display a small preview of the post image."""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 150px; max-height: 150px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = 'Preview'

    actions = ['mark_as_posted', 'mark_as_draft']

    def mark_as_posted(self, request, queryset):
        """Mark selected posts as posted."""
        updated = queryset.update(posted=True)
        self.message_user(request, f'{updated} posts marked as posted.')
    mark_as_posted.short_description = 'Mark selected posts as posted'

    def mark_as_draft(self, request, queryset):
        """Mark selected posts as draft."""
        updated = queryset.update(posted=False)
        self.message_user(request, f'{updated} posts marked as draft.')
    mark_as_draft.short_description = 'Mark selected posts as draft'


@admin.register(PostAnalytics)
class PostAnalyticsAdmin(admin.ModelAdmin):
    """Admin for Post Analytics."""
    list_display = ['post', 'engagement_rate', 'total_engagement', 'reach', 'impressions', 'last_updated']
    list_filter = ['last_updated', 'engagement_rate']
    search_fields = ['post__caption', 'post__user__username']
    readonly_fields = ['created', 'last_updated', 'total_engagement']
    ordering = ['-engagement_rate']

    def total_engagement(self, obj):
        return obj.total_engagement
    total_engagement.short_description = "Total Engagement"


@admin.register(UserAnalytics)
class UserAnalyticsAdmin(admin.ModelAdmin):
    """Admin for User Analytics."""
    list_display = ['user', 'total_posts', 'avg_engagement_rate', 'total_followers', 'created']
    list_filter = ['created', 'avg_engagement_rate']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created']
    ordering = ['-avg_engagement_rate']


@admin.register(ContentTemplate)
class ContentTemplateAdmin(admin.ModelAdmin):
    """Admin for Content Templates."""
    list_display = ['name', 'template_type', 'industry', 'tone', 'usage_count', 'is_public', 'created']
    list_filter = ['template_type', 'industry', 'tone', 'is_public']
    search_fields = ['name', 'content', 'user__username']
    readonly_fields = ['created', 'updated', 'usage_count']
    ordering = ['-usage_count', '-created']


@admin.register(SavedHashtagSet)
class SavedHashtagSetAdmin(admin.ModelAdmin):
    """Admin for Saved Hashtag Sets."""
    list_display = ['name', 'user', 'industry', 'usage_count', 'created']
    list_filter = ['industry', 'created']
    search_fields = ['name', 'hashtags', 'user__username']
    readonly_fields = ['created', 'updated']
    ordering = ['-usage_count', '-created']


@admin.register(ScheduledPost)
class ScheduledPostAdmin(admin.ModelAdmin):
    """Admin for Scheduled Posts."""
    list_display = ['post', 'user', 'scheduled_for', 'status', 'retry_count', 'created']
    list_filter = ['status', 'scheduled_for', 'created']
    search_fields = ['post__caption', 'user__username']
    readonly_fields = ['created', 'updated', 'last_attempt']
    ordering = ['scheduled_for']


@admin.register(SocialMediaAccount)
class SocialMediaAccountAdmin(admin.ModelAdmin):
    """Admin for Social Media Accounts."""
    list_display = ['user', 'platform', 'username', 'status', 'follower_count', 'connected_at']
    list_filter = ['platform', 'status', 'connected_at']
    search_fields = ['user__username', 'username', 'display_name']
    readonly_fields = ['connected_at', 'last_sync', 'created', 'updated']
    ordering = ['-connected_at']

    fieldsets = (
        ('Account Info', {
            'fields': ('user', 'platform', 'username', 'display_name', 'profile_picture_url')
        }),
        ('Authentication', {
            'fields': ('access_token', 'refresh_token', 'token_expires_at'),
            'classes': ('collapse',)
        }),
        ('Platform Data', {
            'fields': ('platform_user_id', 'platform_data'),
            'classes': ('collapse',)
        }),
        ('Status & Metrics', {
            'fields': ('status', 'follower_count', 'following_count', 'posts_count')
        }),
        ('Permissions', {
            'fields': ('can_post', 'can_read')
        }),
        ('Timestamps', {
            'fields': ('connected_at', 'last_sync', 'created', 'updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PostPublication)
class PostPublicationAdmin(admin.ModelAdmin):
    """Admin for Post Publications."""
    list_display = ['post', 'account', 'status', 'published_at', 'likes_count', 'comments_count']
    list_filter = ['status', 'account__platform', 'published_at']
    search_fields = ['post__caption', 'account__username', 'platform_post_id']
    readonly_fields = ['published_at', 'last_sync', 'created']
    ordering = ['-published_at', '-created']


# News Aggregation Admin

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """Admin for Categories."""
    list_display = ['display_name', 'name', 'priority_score', 'is_active', 'created']
    list_filter = ['is_active', 'created']
    search_fields = ['name', 'display_name', 'description']
    readonly_fields = ['created', 'updated']
    ordering = ['display_name']

    fieldsets = (
        ('Basic Info', {
            'fields': ('name', 'display_name', 'description')
        }),
        ('Display', {
            'fields': ('color', 'icon')
        }),
        ('AI Configuration', {
            'fields': ('keywords', 'priority_score')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('created', 'updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(NewsSource)
class NewsSourceAdmin(admin.ModelAdmin):
    """Admin for News Sources."""
    list_display = ['name', 'source_type', 'is_active', 'last_fetch', 'fetch_count', 'error_count', 'health_status']
    list_filter = ['source_type', 'is_active', 'last_fetch']
    search_fields = ['name', 'url']
    readonly_fields = ['created', 'updated', 'last_fetch', 'last_success', 'fetch_count', 'error_count', 'health_status']
    ordering = ['name']
    filter_horizontal = ['categories']

    def health_status(self, obj):
        status = obj.get_health_status()
        colors = {
            'healthy': 'green',
            'stale': 'orange',
            'unhealthy': 'red',
            'never_fetched': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(status, 'black'),
            status.replace('_', ' ').title()
        )
    health_status.short_description = 'Health Status'

    fieldsets = (
        ('Basic Info', {
            'fields': ('name', 'source_type', 'url', 'categories')
        }),
        ('Configuration', {
            'fields': ('fetch_interval', 'max_articles_per_fetch', 'api_config')
        }),
        ('Parsing (for scrapers)', {
            'fields': ('title_selector', 'content_selector', 'image_selector'),
            'classes': ('collapse',)
        }),
        ('Status & Statistics', {
            'fields': ('is_active', 'last_fetch', 'last_success', 'fetch_count', 'error_count', 'last_error'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created', 'updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(NewsArticle)
class NewsArticleAdmin(admin.ModelAdmin):
    """Admin for News Articles."""
    list_display = ['title_short', 'source', 'published_date', 'is_highlighted', 'engagement_prediction', 'is_processed']
    list_filter = ['source', 'is_highlighted', 'is_processed', 'published_date', 'categories']
    search_fields = ['title', 'content', 'author']
    readonly_fields = ['fetched_date', 'created', 'updated', 'engagement_score']
    ordering = ['-published_date']
    filter_horizontal = ['categories']

    def title_short(self, obj):
        return obj.title[:50] + '...' if len(obj.title) > 50 else obj.title
    title_short.short_description = 'Title'

    def engagement_score(self, obj):
        return f"{obj.get_engagement_score():.1f}"
    engagement_score.short_description = 'Engagement Score'

    fieldsets = (
        ('Article Content', {
            'fields': ('title', 'content', 'summary', 'url', 'image_url', 'author')
        }),
        ('Metadata', {
            'fields': ('source', 'categories', 'published_date', 'fetched_date')
        }),
        ('AI Analysis', {
            'fields': ('sentiment_score', 'engagement_prediction', 'trending_score', 'keywords', 'entities', 'topics'),
            'classes': ('collapse',)
        }),
        ('Social Metrics', {
            'fields': ('social_shares', 'social_comments', 'social_likes'),
            'classes': ('collapse',)
        }),
        ('Processing', {
            'fields': ('is_processed', 'is_highlighted', 'processing_error'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created', 'updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(AIHighlight)
class AIHighlightAdmin(admin.ModelAdmin):
    """Admin for AI Highlights."""
    list_display = ['article_title', 'highlight_score', 'was_posted', 'prediction_accuracy', 'created']
    list_filter = ['was_posted', 'created', 'recommended_platforms']
    search_fields = ['article__title', 'highlight_reasons']
    readonly_fields = ['created', 'updated']
    ordering = ['-highlight_score', '-created']

    def article_title(self, obj):
        return obj.article.title[:50] + '...' if len(obj.article.title) > 50 else obj.article.title
    article_title.short_description = 'Article'


@admin.register(AccountCategoryMapping)
class AccountCategoryMappingAdmin(admin.ModelAdmin):
    """Admin for Account Category Mappings."""
    list_display = ['account', 'category', 'priority', 'auto_post', 'min_highlight_score', 'articles_posted']
    list_filter = ['auto_post', 'category', 'account__platform']
    search_fields = ['account__username', 'category__display_name']
    readonly_fields = ['articles_posted', 'avg_engagement', 'last_posted', 'created', 'updated']
    ordering = ['account', 'priority']


@admin.register(NewsFeedPreference)
class NewsFeedPreferenceAdmin(admin.ModelAdmin):
    """Admin for News Feed Preferences."""
    list_display = ['user', 'enable_auto_highlighting', 'enable_auto_posting', 'min_highlight_score', 'notification_frequency']
    list_filter = ['enable_auto_highlighting', 'enable_auto_posting', 'notification_frequency']
    search_fields = ['user__username']
    readonly_fields = ['created', 'updated']
    filter_horizontal = ['preferred_categories']


# Customize admin site header and title
admin.site.site_header = 'Imagsta Administration'
admin.site.site_title = 'Imagsta Admin'
admin.site.index_title = 'Welcome to Imagsta Administration'

