"""
Management command to update analytics data.
Run this command periodically to fetch latest analytics from social media platforms.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import logging
import random

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Update analytics data for posts and users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days back to update analytics for',
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='Update analytics for specific user only',
        )

    def handle(self, *args, **options):
        from images.models import Post, PostAnalytics, UserAnalytics
        from django.contrib.auth.models import User
        
        days = options['days']
        user_id = options['user_id']
        
        # Get posts to update
        cutoff_date = timezone.now() - timedelta(days=days)
        posts_query = Post.objects.filter(
            posted=True,
            created__gte=cutoff_date
        )
        
        if user_id:
            posts_query = posts_query.filter(user_id=user_id)
        
        posts = posts_query.select_related('user')
        
        if not posts.exists():
            self.stdout.write(
                self.style.WARNING('No posts found to update analytics for')
            )
            return
        
        self.stdout.write(
            f'Updating analytics for {posts.count()} posts'
        )
        
        updated_count = 0
        
        for post in posts:
            try:
                # Get or create analytics record
                analytics, created = PostAnalytics.objects.get_or_create(
                    post=post,
                    defaults={
                        'likes_count': 0,
                        'comments_count': 0,
                        'shares_count': 0,
                        'reach': 0,
                        'impressions': 0
                    }
                )
                
                # Simulate fetching analytics from social media APIs
                # In a real implementation, this would call Instagram, Twitter, etc. APIs
                new_analytics = self.simulate_analytics_fetch(post, analytics)
                
                # Update analytics
                analytics.likes_count = new_analytics['likes']
                analytics.comments_count = new_analytics['comments']
                analytics.shares_count = new_analytics['shares']
                analytics.saves_count = new_analytics['saves']
                analytics.reach = new_analytics['reach']
                analytics.impressions = new_analytics['impressions']
                
                # Calculate engagement rate
                analytics.update_engagement_rate()
                
                updated_count += 1
                
                if created:
                    self.stdout.write(f'Created analytics for: {post}')
                else:
                    self.stdout.write(f'Updated analytics for: {post}')
                    
            except Exception as e:
                logger.error(f'Error updating analytics for post {post.id}: {e}')
                self.stdout.write(
                    self.style.ERROR(f'Failed to update analytics for: {post}')
                )
        
        # Update user analytics
        users_to_update = User.objects.filter(
            posts__in=posts
        ).distinct()
        
        for user in users_to_update:
            try:
                user_analytics, created = UserAnalytics.objects.get_or_create(
                    user=user
                )
                user_analytics.update_totals()
                
                self.stdout.write(f'Updated user analytics for: {user.username}')
                
            except Exception as e:
                logger.error(f'Error updating user analytics for {user.id}: {e}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Analytics update complete: {updated_count} posts updated'
            )
        )
    
    def simulate_analytics_fetch(self, post, current_analytics):
        """Simulate fetching analytics from social media platforms."""
        # This simulates realistic analytics growth over time
        
        # Calculate days since post creation
        days_old = (timezone.now() - post.created).days
        
        # Base engagement based on post age (newer posts get more engagement)
        base_multiplier = max(1, 10 - days_old)
        
        # Simulate realistic engagement numbers
        base_likes = random.randint(10, 100) * base_multiplier
        base_comments = random.randint(1, 20) * base_multiplier
        base_shares = random.randint(0, 10) * base_multiplier
        base_saves = random.randint(0, 15) * base_multiplier
        
        # Add some growth to existing metrics
        growth_factor = random.uniform(1.0, 1.2)  # 0-20% growth
        
        new_likes = max(current_analytics.likes_count, int(base_likes * growth_factor))
        new_comments = max(current_analytics.comments_count, int(base_comments * growth_factor))
        new_shares = max(current_analytics.shares_count, int(base_shares * growth_factor))
        new_saves = max(current_analytics.saves_count, int(base_saves * growth_factor))
        
        # Calculate reach and impressions based on engagement
        total_engagement = new_likes + new_comments + new_shares + new_saves
        reach = int(total_engagement * random.uniform(5, 15))  # 5-15x engagement
        impressions = int(reach * random.uniform(2, 5))  # 2-5x reach
        
        return {
            'likes': new_likes,
            'comments': new_comments,
            'shares': new_shares,
            'saves': new_saves,
            'reach': reach,
            'impressions': impressions
        }
