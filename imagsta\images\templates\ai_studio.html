{% extends 'base.html' %}
{% load widget_tweaks %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-light">
                    <i class="bi bi-robot"></i> AI Content Studio
                </h1>
                <div class="badge bg-primary fs-6">
                    <i class="bi bi-sparkles"></i> Powered by AI
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Input Panel -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-pencil-square"></i> Content Input
                    </h5>
                </div>
                <div class="card-body">
                    {% if news_article %}
                    <div class="alert alert-info mb-3">
                        <h6><i class="bi bi-newspaper"></i> Creating post from news article:</h6>
                        <strong>{{ news_article.title }}</strong><br>
                        <small class="text-muted">
                            Source: {{ news_article.source.name }} |
                            Published: {{ news_article.published_date|timesince }} ago
                        </small>
                        {% if news_article.highlight %}
                        <br><span class="badge bg-warning text-dark mt-1">
                            <i class="bi bi-star"></i> AI Score: {{ news_article.highlight.highlight_score|floatformat:0 }}
                        </span>
                        {% endif %}
                    </div>
                    {% endif %}

                    <form hx-post="{% url 'generate-ai-content' %}"
                          hx-target="#ai-results"
                          hx-indicator="#loading-indicator">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="image_description" class="form-label">
                                <i class="bi bi-image"></i> Image Description
                            </label>
                            <textarea class="form-control" 
                                      id="image_description" 
                                      name="image_description" 
                                      rows="3" 
                                      placeholder="Describe the image you want to post about..."></textarea>
                            <div class="form-text">Describe what's in your image or what you want to create</div>
                        </div>

                        <div class="mb-3">
                            <label for="news_context" class="form-label">
                                <i class="bi bi-newspaper"></i> News Context
                            </label>
                            <textarea class="form-control"
                                      id="news_context"
                                      name="news_context"
                                      rows="4"
                                      placeholder="Paste news article or context here...">{% if suggested_content %}{{ suggested_content }}{% endif %}</textarea>
                            <div class="form-text">Add relevant news or context for your post</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tone" class="form-label">
                                    <i class="bi bi-chat-text"></i> Tone
                                </label>
                                <select class="form-select" id="tone" name="tone">
                                    {% for value, label in tone_options %}
                                        <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="industry" class="form-label">
                                    <i class="bi bi-building"></i> Industry
                                </label>
                                <select class="form-select" id="industry" name="industry">
                                    {% for value, label in industry_options %}
                                        <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-magic"></i> Generate AI Content
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="analyzeImage()">
                            <i class="bi bi-eye"></i> Analyze Image Only
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-stars"></i> AI Generated Content
                    </h5>
                </div>
                <div class="card-body">
                    <div id="loading-indicator" class="htmx-indicator text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Generating content...</span>
                        </div>
                        <p class="mt-2 text-muted">AI is crafting your content...</p>
                    </div>
                    
                    <div id="ai-results">
                        <div class="text-center py-5 text-muted">
                            <i class="bi bi-lightbulb display-4"></i>
                            <p class="mt-3">Fill out the form and click "Generate AI Content" to get started!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Overview -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> AI Features
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="bi bi-chat-quote display-6 text-primary"></i>
                                <h6 class="mt-2">Smart Captions</h6>
                                <p class="text-muted small">AI generates engaging captions based on your content and tone preferences</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="bi bi-hash display-6 text-success"></i>
                                <h6 class="mt-2">Hashtag Intelligence</h6>
                                <p class="text-muted small">Get relevant hashtags that boost your content's discoverability</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="bi bi-clock display-6 text-warning"></i>
                                <h6 class="mt-2">Optimal Timing</h6>
                                <p class="text-muted small">AI suggests the best times to post for maximum engagement</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="bi bi-graph-up display-6 text-info"></i>
                                <h6 class="mt-2">Performance Prediction</h6>
                                <p class="text-muted small">Predict how well your content will perform before posting</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: block;
}

.card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(45deg, var(--accent-color), #0056b3);
    color: white;
    border: none;
}

.badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.display-6 {
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.display-6:hover {
    opacity: 1;
}

.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}
</style>

<script>
// Enhanced image preview functionality
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            alert('File size must be less than 10MB');
            e.target.value = '';
            return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file');
            e.target.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = `
                <div class="position-relative">
                    <img src="${e.target.result}" class="img-fluid rounded" style="max-height: 300px;">
                    <div class="position-absolute top-0 end-0 p-2">
                        <span class="badge bg-dark">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="bi bi-info-circle"></i>
                        ${file.name} • ${file.type}
                    </small>
                </div>
            `;
            preview.style.display = 'block';

            // Auto-analyze image
            analyzeImageMetadata(file);
        };
        reader.readAsDataURL(file);
    }
});

function analyzeImageMetadata(file) {
    const img = new Image();
    img.onload = function() {
        const metadata = {
            width: this.width,
            height: this.height,
            aspectRatio: (this.width / this.height).toFixed(2),
            size: file.size,
            type: file.type
        };

        displayImageAnalysis(metadata);
    };
    img.src = URL.createObjectURL(file);
}

function displayImageAnalysis(metadata) {
    const analysisDiv = document.createElement('div');
    analysisDiv.className = 'alert alert-info mt-3';
    analysisDiv.innerHTML = `
        <h6><i class="bi bi-graph-up"></i> Image Analysis</h6>
        <div class="row">
            <div class="col-md-6">
                <small><strong>Dimensions:</strong> ${metadata.width} × ${metadata.height}px</small><br>
                <small><strong>Aspect Ratio:</strong> ${metadata.aspectRatio}</small>
            </div>
            <div class="col-md-6">
                <small><strong>File Size:</strong> ${(metadata.size / 1024 / 1024).toFixed(2)} MB</small><br>
                <small><strong>Format:</strong> ${metadata.type}</small>
            </div>
        </div>
        <div class="mt-2">
            ${getImageRecommendations(metadata)}
        </div>
    `;

    // Remove existing analysis
    const existing = document.querySelector('.image-analysis');
    if (existing) existing.remove();

    analysisDiv.classList.add('image-analysis');
    document.getElementById('imagePreview').appendChild(analysisDiv);
}

function getImageRecommendations(metadata) {
    const recommendations = [];

    // Platform-specific recommendations
    if (metadata.aspectRatio == 1.00) {
        recommendations.push('<span class="badge bg-success">Perfect for Instagram posts</span>');
    } else if (metadata.aspectRatio > 1.5) {
        recommendations.push('<span class="badge bg-info">Great for Twitter/LinkedIn</span>');
    } else if (metadata.aspectRatio < 0.8) {
        recommendations.push('<span class="badge bg-warning">Ideal for Instagram Stories</span>');
    }

    // Quality recommendations
    if (metadata.width >= 1080 && metadata.height >= 1080) {
        recommendations.push('<span class="badge bg-success">High quality</span>');
    } else if (metadata.width < 500 || metadata.height < 500) {
        recommendations.push('<span class="badge bg-warning">Low resolution</span>');
    }

    // Size recommendations
    if (metadata.size > 5 * 1024 * 1024) {
        recommendations.push('<span class="badge bg-warning">Large file - will be optimized</span>');
    }

    return recommendations.join(' ');
}

function analyzeImage() {
    const fileInput = document.getElementById('image');
    if (!fileInput.files[0]) {
        alert('Please select an image first');
        return;
    }

    showNotification('Analyzing image...', 'info');

    // For now, just show the metadata analysis
    const file = fileInput.files[0];
    analyzeImageMetadata(file);
    showNotification('Image analysis complete!', 'success');
}

function showNotification(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type === 'error' ? 'danger' : type === 'info' ? 'info' : 'success'} alert-dismissible fade show position-fixed`;
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.innerHTML = `
        <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'info' ? 'info-circle' : 'check-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 3000);
}
</script>

{% endblock %}
