# Deployment Guide for Imagsta

This guide covers deploying Imagsta to various platforms and environments.

## 🚀 Production Deployment

### Prerequisites

- Python 3.8+
- PostgreSQL (recommended for production)
- Redis (optional, for caching)
- Web server (Nginx recommended)
- SSL certificate

### 1. Server Setup

#### Ubuntu/Debian Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv postgresql postgresql-contrib nginx redis-server -y

# Create application user
sudo adduser imagsta
sudo usermod -aG sudo imagsta
```

#### PostgreSQL Setup

```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE imagsta_db;
CREATE USER imagsta_user WITH PASSWORD 'your_secure_password';
ALTER ROLE imagsta_user SET client_encoding TO 'utf8';
ALTER ROLE imagsta_user SET default_transaction_isolation TO 'read committed';
ALTER ROLE imagsta_user SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE imagsta_db TO imagsta_user;
\q
```

### 2. Application Deployment

```bash
# Switch to application user
sudo su - imagsta

# Clone repository
git clone <your-repo-url> imagsta
cd imagsta

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install gunicorn psycopg2-binary

# Create production environment file
cp .env.example .env
# Edit .env with production values
```

#### Production Environment Variables

```env
# Production settings
SECRET_KEY=your-very-secure-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Database
DATABASE_URL=postgresql://imagsta_user:your_secure_password@localhost:5432/imagsta_db

# API Keys (get from respective services)
FACEBOOK_ACCESS_TOKEN=your-facebook-access-token
INSTAGRAM_ACCOUNT_ID=your-instagram-account-id
UNSPLASH_CLIENT_ID=your-unsplash-client-id
IMGUR_CLIENT_ID=your-imgur-client-id

# Security
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Logging
LOG_LEVEL=INFO
```

### 3. Database Migration

```bash
# Run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput
```

### 4. Gunicorn Configuration

Create `/home/<USER>/imagsta/gunicorn.conf.py`:

```python
bind = "127.0.0.1:8000"
workers = 3
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
user = "imagsta"
group = "imagsta"
```

### 5. Systemd Service

Create `/etc/systemd/system/imagsta.service`:

```ini
[Unit]
Description=Imagsta Django Application
After=network.target

[Service]
User=imagsta
Group=imagsta
WorkingDirectory=/home/<USER>/imagsta
Environment=PATH=/home/<USER>/imagsta/venv/bin
ExecStart=/home/<USER>/imagsta/venv/bin/gunicorn --config gunicorn.conf.py imagsta.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable imagsta
sudo systemctl start imagsta
sudo systemctl status imagsta
```

### 6. Nginx Configuration

Create `/etc/nginx/sites-available/imagsta`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    location = /favicon.ico { access_log off; log_not_found off; }
    
    location /static/ {
        root /home/<USER>/imagsta;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /media/ {
        root /home/<USER>/imagsta;
        expires 1y;
        add_header Cache-Control "public";
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/imagsta /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . /app/

# Collect static files
RUN python manage.py collectstatic --noinput

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "imagsta.wsgi:application"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=*************************************/imagsta
    depends_on:
      - db
      - redis
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=imagsta
      - POSTGRES_USER=imagsta
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data/

  redis:
    image: redis:6-alpine

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    depends_on:
      - web

volumes:
  postgres_data:
  static_volume:
  media_volume:
```

## ☁️ Cloud Platform Deployment

### Heroku

1. Install Heroku CLI
2. Create Heroku app
3. Add PostgreSQL addon
4. Set environment variables
5. Deploy

```bash
# Create app
heroku create your-app-name

# Add PostgreSQL
heroku addons:create heroku-postgresql:hobby-dev

# Set environment variables
heroku config:set SECRET_KEY=your-secret-key
heroku config:set DEBUG=False
heroku config:set UNSPLASH_CLIENT_ID=your-key

# Deploy
git push heroku main

# Run migrations
heroku run python manage.py migrate
heroku run python manage.py createsuperuser
```

### AWS EC2

1. Launch EC2 instance
2. Configure security groups
3. Follow server setup steps above
4. Configure RDS for database
5. Use S3 for media files

### DigitalOcean

1. Create droplet
2. Follow server setup steps
3. Configure managed database
4. Set up Spaces for media storage

## 🔧 Maintenance

### Backup Strategy

```bash
# Database backup
pg_dump imagsta_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Media files backup
tar -czf media_backup_$(date +%Y%m%d_%H%M%S).tar.gz media/
```

### Log Rotation

```bash
# Configure logrotate
sudo nano /etc/logrotate.d/imagsta
```

```
/home/<USER>/imagsta/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 imagsta imagsta
}
```

### Monitoring

- Set up monitoring with tools like Prometheus/Grafana
- Configure error tracking with Sentry
- Monitor application performance
- Set up alerts for critical issues

## 🔒 Security Checklist

- [ ] Use HTTPS everywhere
- [ ] Keep dependencies updated
- [ ] Regular security audits
- [ ] Backup strategy in place
- [ ] Monitor logs for suspicious activity
- [ ] Use strong passwords and API keys
- [ ] Configure firewall properly
- [ ] Regular system updates

## 📊 Performance Optimization

- Use CDN for static files
- Enable database connection pooling
- Configure Redis for caching
- Optimize database queries
- Monitor and profile application performance
- Use compression for static files
