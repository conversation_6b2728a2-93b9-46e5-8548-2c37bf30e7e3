{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-newspaper"></i> All News Articles
                </h1>
                <div>
                    <span class="badge bg-primary me-2">{{ total_articles }} Articles</span>
                    <span class="badge bg-warning text-dark">{{ total_highlights }} AI Highlights</span>
                </div>
            </div>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle"></i> Error: {{ error }}
    </div>
    {% endif %}

    <!-- AI Highlights Section -->
    {% if highlights %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-star-fill"></i> AI Highlights (Top Scoring Articles)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for highlight in highlights|slice:":6" %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100 border-warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <span class="badge bg-warning text-dark">
                                            <i class="bi bi-star"></i> {{ highlight.highlight_score|floatformat:0 }}
                                        </span>
                                        <small class="text-muted">
                                            {{ highlight.article.published_date|timesince }} ago
                                        </small>
                                    </div>
                                    
                                    <h6 class="card-title">
                                        <a href="{{ highlight.article.url }}" target="_blank" class="text-decoration-none">
                                            {{ highlight.article.title|truncatechars:80 }}
                                        </a>
                                    </h6>
                                    
                                    <p class="card-text small text-muted">
                                        {{ highlight.article.content|truncatechars:120 }}
                                    </p>
                                    
                                    <div class="mb-2">
                                        {% for category in highlight.article.categories.all %}
                                            <span class="badge bg-secondary me-1">{{ category.display_name }}</span>
                                        {% endfor %}
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-rss"></i> {{ highlight.article.source.name }}
                                        </small>
                                        <a href="{% url 'ai-studio' %}?news_article_id={{ highlight.article.id }}" 
                                           class="btn btn-sm btn-primary">
                                            <i class="bi bi-plus"></i> Create Post
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- All Articles Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list"></i> Recent Articles
                    </h5>
                </div>
                <div class="card-body">
                    {% if articles %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Source</th>
                                        <th>Categories</th>
                                        <th>Published</th>
                                        <th>AI Score</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for article in articles %}
                                    <tr>
                                        <td>
                                            <strong>
                                                <a href="{{ article.url }}" target="_blank" class="text-decoration-none">
                                                    {{ article.title|truncatechars:60 }}
                                                </a>
                                            </strong>
                                            {% if article.is_highlighted %}
                                                <span class="badge bg-warning text-dark ms-2">
                                                    <i class="bi bi-star"></i> Highlighted
                                                </span>
                                            {% endif %}
                                            <br>
                                            <small class="text-muted">{{ article.content|truncatechars:100 }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ article.source.name }}</span>
                                        </td>
                                        <td>
                                            {% for category in article.categories.all %}
                                                <span class="badge bg-secondary me-1">{{ category.display_name }}</span>
                                            {% empty %}
                                                <span class="text-muted">None</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            <small>{{ article.published_date|timesince }} ago</small>
                                        </td>
                                        <td>
                                            {% if article.highlight %}
                                                <span class="badge bg-warning text-dark">
                                                    {{ article.highlight.highlight_score|floatformat:0 }}
                                                </span>
                                            {% elif article.engagement_prediction %}
                                                <span class="badge bg-light text-dark">
                                                    {{ article.engagement_prediction|floatformat:0 }}
                                                </span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'ai-studio' %}?news_article_id={{ article.id }}" 
                                               class="btn btn-sm btn-primary">
                                                <i class="bi bi-plus"></i> Create Post
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-newspaper text-muted" style="font-size: 4rem;"></i>
                            <h3 class="text-muted mt-3">No articles found</h3>
                            <p class="text-muted">No news articles have been fetched yet.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Info -->
    {% if categories %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-tags"></i> Available Categories
                    </h6>
                </div>
                <div class="card-body">
                    {% for category in categories %}
                        <span class="badge bg-secondary me-2 mb-2">
                            <i class="{{ category.icon }}"></i> {{ category.display_name }}
                        </span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.border-warning {
    border-color: #ffc107 !important;
    border-width: 2px !important;
}

.table th {
    border-top: none;
    font-weight: 600;
}
</style>
{% endblock %}
