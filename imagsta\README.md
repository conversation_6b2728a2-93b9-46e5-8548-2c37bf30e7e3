# Imagsta - Instagram Content Creation & Management Platform

![Python](https://img.shields.io/badge/python-3.8+-blue.svg)
![Django](https://img.shields.io/badge/django-4.2+-green.svg)
![License](https://img.shields.io/badge/license-MIT-blue.svg)

Imagsta is a comprehensive Django-based web application that combines news aggregation, image search, and Instagram content creation into a powerful social media management tool. Create engaging Instagram posts by combining trending news with beautiful images from Unsplash.

## ✨ Features

### 🔍 **Content Discovery**
- **Real-time News Aggregation**: Fetch latest headlines from Google News (Business, World, Technology)
- **Image Search Integration**: Search and browse high-quality images from Unsplash API
- **Smart Content Combination**: Automatically overlay news text on images with custom styling

### 📱 **Instagram Integration**
- **Direct Publishing**: Post content directly to Instagram using Facebook Graph API
- **Post Management**: Track, edit, and manage your Instagram posts
- **Scheduling Support**: Schedule posts for optimal engagement times
- **Hashtag Generation**: Automated hashtag suggestions for better reach

### 👤 **User Management**
- **Secure Authentication**: Custom user registration and login system
- **User Dashboard**: Personal post history and analytics
- **Role-based Access**: Admin panel for content moderation

### 🎨 **Content Creation**
- **Visual Editor**: Overlay text on images with customizable fonts and positioning
- **Template System**: Pre-designed templates for consistent branding
- **Image Processing**: Automatic image optimization and formatting for Instagram

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- pip (Python package manager)
- Virtual environment (recommended)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd imagsta
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv venv

   # On Windows
   venv\Scripts\activate

   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment Configuration**
   ```bash
   # Copy the example environment file
   cp .env.example .env

   # Edit .env with your configuration
   # See Configuration section below
   ```

5. **Database Setup**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   python manage.py createsuperuser
   ```

6. **Run the development server**
   ```bash
   python manage.py runserver
   ```

7. **Access the application**
   - Open your browser and go to `http://127.0.0.1:8000`
   - Admin panel: `http://127.0.0.1:8000/admin`

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (for production)
DATABASE_URL=sqlite:///db.sqlite3

# Instagram/Facebook API
FACEBOOK_ACCESS_TOKEN=your-facebook-access-token
INSTAGRAM_ACCOUNT_ID=your-instagram-account-id
FACEBOOK_GRAPH_VERSION=v18.0

# Unsplash API
UNSPLASH_CLIENT_ID=your-unsplash-client-id

# Imgur API (for image hosting)
IMGUR_CLIENT_ID=your-imgur-client-id

# Logging
LOG_LEVEL=INFO
```

### API Setup

#### 1. Unsplash API
1. Go to [Unsplash Developers](https://unsplash.com/developers)
2. Create a new application
3. Copy the Access Key to `UNSPLASH_CLIENT_ID`

#### 2. Facebook/Instagram API
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Instagram Basic Display product
4. Get your access token and account ID
5. Add them to your `.env` file

#### 3. Imgur API (Optional)
1. Go to [Imgur API](https://apidocs.imgur.com/)
2. Register your application
3. Copy the Client ID to `IMGUR_CLIENT_ID`

## 📁 Project Structure

```
imagsta/
├── imagsta/                 # Main Django project
│   ├── settings.py         # Project settings
│   ├── urls.py             # URL configuration
│   └── wsgi.py             # WSGI configuration
├── images/                 # Main Django app
│   ├── models.py           # Database models
│   ├── views.py            # View logic
│   ├── urls.py             # App URL patterns
│   ├── utils.py            # Utility functions
│   ├── forms.py            # Django forms
│   ├── admin.py            # Admin configuration
│   ├── tests.py            # Test cases
│   └── templates/          # HTML templates
├── static/                 # Static files (CSS, JS, images)
├── media/                  # User uploaded files
├── logs/                   # Application logs
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables example
├── pytest.ini            # Test configuration
└── README.md              # This file
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
python manage.py test

# Run with pytest (recommended)
pytest

# Run with coverage
pytest --cov=images --cov-report=html

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
```

### Test Categories

- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test API integrations and workflows
- **Model Tests**: Test database models and relationships
- **View Tests**: Test HTTP endpoints and responses

## 📊 Database Models

### User
Extended Django user model with additional fields for social media management.

### Search
Stores search queries and tracks search history with usage statistics.

### Result
Stores image search results from external APIs (Unsplash, etc.).

### Post
Manages user-created posts with status tracking and Instagram integration.

## 🔧 Development

### Code Quality

The project includes several tools for maintaining code quality:

- **Logging**: Comprehensive logging throughout the application
- **Error Handling**: Graceful error handling with user-friendly messages
- **Security**: CSRF protection, secure headers, and input validation
- **Performance**: Database indexing and query optimization

### Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements.txt

# Run development server with debug
python manage.py runserver --settings=imagsta.settings

# Run tests in watch mode
pytest --watch

# Check code style
flake8 images/
black images/
```

## 🚀 Deployment

### Production Checklist

- [ ] Set `DEBUG=False` in production
- [ ] Configure proper database (PostgreSQL recommended)
- [ ] Set up static file serving (WhiteNoise included)
- [ ] Configure logging to files
- [ ] Set up monitoring and error tracking
- [ ] Use environment variables for all secrets
- [ ] Enable HTTPS and security headers

### Docker Deployment

```dockerfile
# Dockerfile example
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN python manage.py collectstatic --noinput

EXPOSE 8000
CMD ["gunicorn", "imagsta.wsgi:application", "--bind", "0.0.0.0:8000"]
```

### Environment-specific Settings

Create separate settings files for different environments:

- `settings/development.py`
- `settings/production.py`
- `settings/testing.py`

## 📈 Performance Optimization

### Database Optimization
- Database indexes on frequently queried fields
- Query optimization with `select_related` and `prefetch_related`
- Connection pooling for production

### Caching
- Template caching for static content
- API response caching
- Static file compression with WhiteNoise

### Monitoring
- Application logging with structured logs
- Performance monitoring with Django Debug Toolbar (development)
- Error tracking integration ready

## 🔒 Security Features

- **CSRF Protection**: Built-in Django CSRF protection
- **XSS Prevention**: Template auto-escaping and CSP headers
- **SQL Injection Prevention**: Django ORM protection
- **Secure Headers**: Security middleware configuration
- **Input Validation**: Form validation and sanitization
- **Authentication**: Secure user authentication system

## 🐛 Troubleshooting

### Common Issues

1. **API Rate Limiting**
   - Unsplash API has rate limits (50 requests/hour for demo)
   - Implement caching to reduce API calls
   - Consider upgrading to paid plan for production

2. **Instagram API Issues**
   - Ensure your Facebook app is approved for Instagram API
   - Check access token validity
   - Verify Instagram account permissions

3. **Image Upload Issues**
   - Check file permissions for media directory
   - Verify Imgur API configuration
   - Ensure proper image formats (JPEG, PNG)

### Debug Mode

Enable debug mode for development:

```python
# In settings.py
DEBUG = True
LOGGING['loggers']['images']['level'] = 'DEBUG'
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs and feature requests on GitHub Issues
- **Community**: Join our discussions for help and collaboration

## 🙏 Acknowledgments

- [Django](https://djangoproject.com/) - The web framework
- [Unsplash](https://unsplash.com/) - Beautiful free photos
- [Bootstrap](https://getbootstrap.com/) - UI components
- [HTMX](https://htmx.org/) - Dynamic interactions
- [Font Awesome](https://fontawesome.com/) - Icons

---

**Made with ❤️ for content creators and social media managers**
