{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-calendar3"></i> Content Calendar
                </h1>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary">
                        <i class="bi bi-chevron-left"></i> Previous
                    </button>
                    <button type="button" class="btn btn-primary">{{ current_month }}</button>
                    <button type="button" class="btn btn-outline-primary">
                        Next <i class="bi bi-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Grid -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-0">
                    <div class="calendar-grid">
                        <!-- Calendar Header -->
                        <div class="calendar-header">
                            <div class="calendar-day-header">Sunday</div>
                            <div class="calendar-day-header">Monday</div>
                            <div class="calendar-day-header">Tuesday</div>
                            <div class="calendar-day-header">Wednesday</div>
                            <div class="calendar-day-header">Thursday</div>
                            <div class="calendar-day-header">Friday</div>
                            <div class="calendar-day-header">Saturday</div>
                        </div>

                        <!-- Calendar Body -->
                        <div class="calendar-body">
                            <!-- Week 1 -->
                            <div class="calendar-week">
                                {% for day in "1,2,3,4,5,6,7"|make_list %}
                                <div class="calendar-day">
                                    <div class="day-number">{{ day }}</div>
                                    <div class="day-content">
                                        <button class="btn btn-sm btn-outline-primary add-post-btn" data-date="2025-07-0{{ day }}">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <!-- Week 2 -->
                            <div class="calendar-week">
                                {% for day in "8,9,10,11,12,13,14"|make_list %}
                                <div class="calendar-day {% if day == '12' %}today{% endif %}">
                                    <div class="day-number">{{ day }}</div>
                                    <div class="day-content">
                                        {% if day == '12' %}
                                        {% for post in posts|slice:":2" %}
                                        <div class="post-item status-{{ post.posted|yesno:'posted,draft' }}" title="{{ post.caption|truncatechars:50 }}">
                                            <div class="post-indicator"></div>
                                            <small>{{ post.caption|truncatechars:20 }}</small>
                                        </div>
                                        {% endfor %}
                                        {% endif %}
                                        <button class="btn btn-sm btn-outline-primary add-post-btn" data-date="2025-07-{{ day }}">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <!-- Week 3 -->
                            <div class="calendar-week">
                                {% for day in "15,16,17,18,19,20,21"|make_list %}
                                <div class="calendar-day">
                                    <div class="day-number">{{ day }}</div>
                                    <div class="day-content">
                                        {% if day == '18' %}
                                        {% for post in posts|slice:":1" %}
                                        <div class="post-item status-{{ post.posted|yesno:'posted,draft' }}" title="{{ post.caption|truncatechars:50 }}">
                                            <div class="post-indicator"></div>
                                            <small>{{ post.caption|truncatechars:20 }}</small>
                                        </div>
                                        {% endfor %}
                                        {% endif %}
                                        <button class="btn btn-sm btn-outline-primary add-post-btn" data-date="2025-07-{{ day }}">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <!-- Week 4 -->
                            <div class="calendar-week">
                                {% for day in "22,23,24,25,26,27,28"|make_list %}
                                <div class="calendar-day">
                                    <div class="day-number">{{ day }}</div>
                                    <div class="day-content">
                                        {% if day == '25' %}
                                        {% for post in posts|slice:":1" %}
                                        <div class="post-item status-{{ post.posted|yesno:'posted,draft' }}" title="{{ post.caption|truncatechars:50 }}">
                                            <div class="post-indicator"></div>
                                            <small>{{ post.caption|truncatechars:20 }}</small>
                                        </div>
                                        {% endfor %}
                                        {% endif %}
                                        <button class="btn btn-sm btn-outline-primary add-post-btn" data-date="2025-07-{{ day }}">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <!-- Week 5 -->
                            <div class="calendar-week">
                                {% for day in "29,30,31"|make_list %}
                                <div class="calendar-day">
                                    <div class="day-number">{{ day }}</div>
                                    <div class="day-content">
                                        <button class="btn btn-sm btn-outline-primary add-post-btn" data-date="2025-07-{{ day }}">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                                <!-- Empty cells for the rest of the week -->
                                {% for i in "1,2,3,4"|make_list %}
                                <div class="calendar-day other-month">
                                    <div class="day-number">{{ i }}</div>
                                    <div class="day-content"></div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mt-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ posts|length }}</h3>
                    <p class="mb-0">Posts This Month</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ posts|length|floatformat:0 }}</h3>
                    <p class="mb-0">Published</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>5</h3>
                    <p class="mb-0">Scheduled</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>3</h3>
                    <p class="mb-0">Drafts</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Posts -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock"></i> Upcoming Posts
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Preview</th>
                                    <th>Caption</th>
                                    <th>Scheduled For</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for post in posts|slice:":10" %}
                                <tr>
                                    <td>
                                        {% if post.image %}
                                        <img src="{{ post.image.url }}" alt="Post" class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-secondary rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="bi bi-image text-white"></i>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ post.caption|truncatechars:50|default:"Untitled Post" }}</strong>
                                            {% if post.hashtags %}
                                            <br><small class="text-muted">{{ post.hashtags|truncatechars:30 }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if post.scheduled_for %}
                                            {{ post.scheduled_for|date:"M d, Y g:i A" }}
                                        {% else %}
                                            <span class="text-muted">Not scheduled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ post.posted|yesno:'success,warning' }}">
                                            {{ post.posted|yesno:'Posted,Draft' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-success" title="Schedule">
                                                <i class="bi bi-clock"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <i class="bi bi-calendar-x display-4 text-muted d-block mb-2"></i>
                                        <p class="text-muted">No posts scheduled for this month</p>
                                        <a href="{% url 'ai-studio' %}" class="btn btn-primary">
                                            <i class="bi bi-plus"></i> Create Your First Post
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Post Modal -->
<div class="modal fade" id="addPostModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Schedule New Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="postDate" class="form-label">Date</label>
                        <input type="date" class="form-control" id="postDate">
                    </div>
                    <div class="mb-3">
                        <label for="postTime" class="form-label">Time</label>
                        <input type="time" class="form-control" id="postTime">
                    </div>
                    <div class="mb-3">
                        <label for="postCaption" class="form-label">Caption</label>
                        <textarea class="form-control" id="postCaption" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Schedule Post</button>
            </div>
        </div>
    </div>
</div>

<style>
.calendar-grid {
    display: flex;
    flex-direction: column;
    height: 600px;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background-color: var(--bs-primary);
    color: white;
}

.calendar-day-header {
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.calendar-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.calendar-week {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    flex: 1;
}

.calendar-day {
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    position: relative;
    background-color: white;
    min-height: 120px;
}

.calendar-day.today {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: var(--bs-primary);
}

.calendar-day.other-month {
    background-color: #f8f9fa;
    color: #6c757d;
}

.day-number {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.day-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.post-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    background-color: rgba(13, 110, 253, 0.1);
    border-left: 3px solid var(--bs-primary);
}

.post-item.status-posted {
    background-color: rgba(25, 135, 84, 0.1);
    border-left-color: var(--bs-success);
}

.post-item.status-draft {
    background-color: rgba(255, 193, 7, 0.1);
    border-left-color: var(--bs-warning);
}

.post-item.status-failed {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: var(--bs-danger);
}

.post-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--bs-primary);
    flex-shrink: 0;
}

.add-post-btn {
    margin-top: auto;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.table th {
    border-top: none;
    font-weight: 600;
}

@media (max-width: 768px) {
    .calendar-day-header {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
    
    .calendar-day {
        min-height: 80px;
        padding: 0.25rem;
    }
    
    .post-item {
        font-size: 0.625rem;
    }
}
</style>

<script>
// Add post button functionality
document.addEventListener('DOMContentLoaded', function() {
    const addPostBtns = document.querySelectorAll('.add-post-btn');
    const modal = new bootstrap.Modal(document.getElementById('addPostModal'));
    
    addPostBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const date = this.getAttribute('data-date');
            document.getElementById('postDate').value = date;
            modal.show();
        });
    });
});
</script>
{% endblock %}
