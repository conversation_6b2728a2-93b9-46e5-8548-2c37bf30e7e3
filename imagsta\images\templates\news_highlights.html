{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-star-fill text-warning"></i> AI Highlights
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'news-dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{% url 'news-sources' %}" class="btn btn-outline-info">
                        <i class="bi bi-rss"></i> Manage Sources
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                {% for category in categories %}
                                <option value="{{ category.name }}" 
                                        {% if category.name == current_category %}selected{% endif %}>
                                    {{ category.display_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="min_score" class="form-label">Minimum Score</label>
                            <select class="form-select" id="min_score" name="min_score">
                                <option value="50" {% if current_min_score == 50 %}selected{% endif %}>50+</option>
                                <option value="60" {% if current_min_score == 60 %}selected{% endif %}>60+</option>
                                <option value="70" {% if current_min_score == 70 %}selected{% endif %}>70+</option>
                                <option value="80" {% if current_min_score == 80 %}selected{% endif %}>80+</option>
                                <option value="90" {% if current_min_score == 90 %}selected{% endif %}>90+</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-funnel"></i> Filter
                            </button>
                            <a href="{% url 'news-highlights' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Highlights List -->
    <div class="row">
        <div class="col-12">
            {% if highlights %}
                {% for highlight in highlights %}
                <div class="card mb-3 border-warning">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <span class="badge bg-warning text-dark me-2">
                                            <i class="bi bi-star"></i> {{ highlight.highlight_score|floatformat:1 }}
                                        </span>
                                        {% for category in highlight.article.categories.all %}
                                            <span class="badge bg-secondary me-1">{{ category.display_name }}</span>
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted">
                                        {{ highlight.article.published_date|timesince }} ago
                                    </small>
                                </div>
                                
                                <h5 class="card-title">
                                    <a href="{{ highlight.article.url }}" target="_blank" class="text-decoration-none">
                                        {{ highlight.article.title }}
                                    </a>
                                </h5>
                                
                                <p class="card-text">
                                    {{ highlight.article.content|truncatechars:200 }}
                                </p>
                                
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="bi bi-rss"></i> {{ highlight.article.source.name }} |
                                        <i class="bi bi-person"></i> {{ highlight.article.author|default:"Unknown" }} |
                                        <i class="bi bi-calendar"></i> {{ highlight.article.published_date|date:"M d, Y H:i" }}
                                    </small>
                                </div>
                                
                                <!-- Highlight Reasons -->
                                {% if highlight.highlight_reasons %}
                                <div class="mb-3">
                                    <h6 class="text-primary">Why this was highlighted:</h6>
                                    <ul class="list-unstyled">
                                        {% for reason in highlight.highlight_reasons %}
                                        <li><i class="bi bi-check-circle text-success"></i> {{ reason }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                                
                                <!-- Recommended Platforms -->
                                {% if highlight.recommended_platforms %}
                                <div class="mb-3">
                                    <h6 class="text-info">Recommended platforms:</h6>
                                    {% for platform in highlight.recommended_platforms %}
                                        <span class="badge bg-info me-1">
                                            <i class="bi bi-{{ platform }}"></i> {{ platform|title }}
                                        </span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4">
                                <!-- Article Image -->
                                {% if highlight.article.image_url %}
                                <img src="{{ highlight.article.image_url }}" 
                                     class="img-fluid rounded mb-3" 
                                     alt="Article image"
                                     style="max-height: 200px; object-fit: cover;">
                                {% endif %}
                                
                                <!-- Metrics -->
                                <div class="card bg-light">
                                    <div class="card-body p-3">
                                        <h6 class="card-title">AI Analysis</h6>
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="text-primary">
                                                    <strong>{{ highlight.article.engagement_prediction|floatformat:0 }}</strong>
                                                </div>
                                                <small class="text-muted">Engagement</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="text-success">
                                                    <strong>{{ highlight.article.trending_score|floatformat:0 }}</strong>
                                                </div>
                                                <small class="text-muted">Trending</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="text-info">
                                                    <strong>{{ highlight.article.sentiment_score|floatformat:1 }}</strong>
                                                </div>
                                                <small class="text-muted">Sentiment</small>
                                            </div>
                                        </div>
                                        
                                        {% if highlight.article.social_shares or highlight.article.social_likes %}
                                        <hr>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="text-warning">
                                                    <strong>{{ highlight.article.social_likes }}</strong>
                                                </div>
                                                <small class="text-muted">Likes</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-primary">
                                                    <strong>{{ highlight.article.social_shares }}</strong>
                                                </div>
                                                <small class="text-muted">Shares</small>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Actions -->
                                <div class="mt-3 d-grid gap-2">
                                    {% if not highlight.was_posted %}
                                    <button class="btn btn-primary create-post-btn" 
                                            data-article-id="{{ highlight.article.id }}"
                                            data-suggested-caption="{{ highlight.suggested_caption }}"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#createPostModal">
                                        <i class="bi bi-plus"></i> Create Post
                                    </button>
                                    {% else %}
                                    <button class="btn btn-success" disabled>
                                        <i class="bi bi-check"></i> Posted
                                    </button>
                                    {% endif %}
                                    
                                    <a href="{{ highlight.article.url }}" target="_blank" class="btn btn-outline-secondary">
                                        <i class="bi bi-box-arrow-up-right"></i> Read Full Article
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-star text-muted" style="font-size: 4rem;"></i>
                        <h3 class="text-muted mt-3">No highlights found</h3>
                        <p class="text-muted">
                            {% if current_category or current_min_score > 60 %}
                                Try adjusting your filters or check back later.
                            {% else %}
                                AI will analyze articles and highlight the best ones for posting.
                            {% endif %}
                        </p>
                        <a href="{% url 'news-dashboard' %}" class="btn btn-primary">
                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Create Post Modal -->
<div class="modal fade" id="createPostModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Post from Highlighted Article</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPostForm">
                    {% csrf_token %}
                    <input type="hidden" id="articleId" name="article_id">
                    
                    <div class="mb-3">
                        <label for="customCaption" class="form-label">Caption</label>
                        <textarea class="form-control" id="customCaption" name="custom_caption" rows="5" 
                                  placeholder="AI-suggested caption will appear here..."></textarea>
                        <div class="form-text">You can edit the AI-suggested caption or write your own.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Suggested Hashtags</label>
                        <div id="suggestedHashtags" class="border rounded p-2 bg-light">
                            <!-- Hashtags will be populated here -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="createPostBtn">
                    <i class="bi bi-plus"></i> Create Post
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle create post button clicks
    document.querySelectorAll('.create-post-btn').forEach(button => {
        button.addEventListener('click', function() {
            const articleId = this.dataset.articleId;
            const suggestedCaption = this.dataset.suggestedCaption;
            
            document.getElementById('articleId').value = articleId;
            document.getElementById('customCaption').value = suggestedCaption || '';
        });
    });
    
    // Handle create post form submission
    document.getElementById('createPostBtn').addEventListener('click', function() {
        const form = document.getElementById('createPostForm');
        const formData = new FormData(form);
        
        // Show loading state
        this.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating...';
        this.disabled = true;
        
        fetch('{% url "create-post-from-news" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                bootstrap.Modal.getInstance(document.getElementById('createPostModal')).hide();
                
                // Show success message and reload page
                alert('Post created successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the post.');
        })
        .finally(() => {
            // Reset button
            this.innerHTML = '<i class="bi bi-plus"></i> Create Post';
            this.disabled = false;
        });
    });
});
</script>

<style>
.border-warning {
    border-color: #ffc107 !important;
    border-width: 2px !important;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.create-post-btn {
    transition: all 0.2s;
}

.create-post-btn:hover {
    transform: scale(1.02);
}
</style>
{% endblock %}
