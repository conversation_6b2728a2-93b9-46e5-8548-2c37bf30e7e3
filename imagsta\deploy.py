#!/usr/bin/env python
"""
Deployment script for Imagsta News Aggregation System
"""

import os
import sys
import subprocess
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'imagsta.settings')
django.setup()

from django.core.management import call_command
from django.contrib.auth import get_user_model
from images.models import Category, NewsSource

User = get_user_model()


def run_command(command, description):
    """Run a shell command with error handling."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False


def setup_database():
    """Set up the database with migrations."""
    print("\n📊 Setting up database...")
    
    try:
        call_command('makemigrations', verbosity=1)
        call_command('migrate', verbosity=1)
        print("✅ Database setup completed")
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False


def create_superuser():
    """Create a superuser if none exists."""
    print("\n👤 Setting up admin user...")
    
    if User.objects.filter(is_superuser=True).exists():
        print("✅ Superuser already exists")
        return True
    
    try:
        username = input("Enter admin username (default: admin): ").strip() or "admin"
        email = input("Enter admin email: ").strip()
        password = input("Enter admin password: ").strip()
        
        if not email or not password:
            print("❌ Email and password are required")
            return False
        
        User.objects.create_superuser(username=username, email=email, password=password)
        print(f"✅ Superuser '{username}' created successfully")
        return True
    except Exception as e:
        print(f"❌ Superuser creation failed: {e}")
        return False


def setup_news_sources():
    """Set up sample news sources."""
    print("\n📰 Setting up news sources...")
    
    try:
        call_command('fetch_news', '--create-sample-sources', verbosity=1)
        print("✅ Sample news sources created")
        return True
    except Exception as e:
        print(f"❌ News sources setup failed: {e}")
        return False


def run_initial_news_fetch():
    """Run initial news fetch and analysis."""
    print("\n🔄 Running initial news fetch and analysis...")
    
    try:
        # Fetch news
        call_command('fetch_news', '--force', verbosity=1)
        
        # Analyze articles
        call_command('analyze_news', '--limit', '20', '--min-score', '60', verbosity=1)
        
        print("✅ Initial news processing completed")
        return True
    except Exception as e:
        print(f"❌ Initial news processing failed: {e}")
        return False


def check_requirements():
    """Check if all requirements are installed."""
    print("\n📋 Checking requirements...")
    
    try:
        import requests
        import beautifulsoup4
        import django
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("Please run: pip install -r requirements.txt")
        return False


def create_env_file():
    """Create .env file if it doesn't exist."""
    print("\n⚙️ Setting up environment configuration...")
    
    env_file = project_dir / '.env'
    env_example = project_dir / '.env.example'
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        # Copy example file
        with open(env_example, 'r') as f:
            content = f.read()
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("✅ .env file created from example")
        print("⚠️  Please edit .env file with your API keys and configuration")
        return True
    else:
        # Create basic .env file
        basic_env = """# Imagsta Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
DATABASE_URL=sqlite:///db.sqlite3

# News API (optional)
NEWSAPI_KEY=your-newsapi-key

# OpenAI API (optional, for enhanced AI analysis)
OPENAI_API_KEY=your-openai-key

# Social Media APIs (optional)
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret
"""
        
        with open(env_file, 'w') as f:
            f.write(basic_env)
        
        print("✅ Basic .env file created")
        print("⚠️  Please edit .env file with your actual configuration")
        return True


def display_final_instructions():
    """Display final setup instructions."""
    print("\n" + "="*60)
    print("🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!")
    print("="*60)
    
    print("\n📋 NEXT STEPS:")
    print("1. Edit .env file with your API keys")
    print("2. Start the development server:")
    print("   python manage.py runserver")
    print("\n3. Visit these URLs:")
    print("   • Main dashboard: http://localhost:8000/")
    print("   • News dashboard: http://localhost:8000/news-dashboard/")
    print("   • Admin panel: http://localhost:8000/admin/")
    print("   • Account categories: http://localhost:8000/account-categories/")
    
    print("\n🤖 AUTOMATION SETUP:")
    print("For production, set up cron jobs:")
    print("# Run news pipeline every hour")
    print("0 * * * * cd /path/to/imagsta && python manage.py news_pipeline")
    
    print("\n📚 DOCUMENTATION:")
    print("• News Aggregation Guide: NEWS_AGGREGATION_GUIDE.md")
    print("• News System README: README_NEWS_SYSTEM.md")
    print("• Management commands: python manage.py help")
    
    print("\n🧪 TESTING:")
    print("Run tests with: python manage.py test images.tests.test_news_system")
    
    print("\n🚀 READY TO GO!")
    print("Your AI-powered news aggregation system is ready!")


def main():
    """Main deployment function."""
    print("🚀 IMAGSTA NEWS AGGREGATION SYSTEM DEPLOYMENT")
    print("=" * 50)
    
    steps = [
        (check_requirements, "Check Requirements"),
        (create_env_file, "Environment Setup"),
        (setup_database, "Database Setup"),
        (create_superuser, "Admin User Setup"),
        (setup_news_sources, "News Sources Setup"),
        (run_initial_news_fetch, "Initial News Processing"),
    ]
    
    failed_steps = []
    
    for step_func, step_name in steps:
        if not step_func():
            failed_steps.append(step_name)
    
    if failed_steps:
        print(f"\n❌ Deployment completed with errors in: {', '.join(failed_steps)}")
        print("Please check the errors above and fix them manually.")
        return False
    else:
        display_final_instructions()
        return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
