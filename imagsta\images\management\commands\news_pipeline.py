"""
Comprehensive news pipeline management command.
Fetches news, analyzes with AI, and optionally auto-posts.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.management import call_command
from images.news_services import NewsAggregatorService
from images.auto_posting_service import auto_posting_service
from images.models import NewsSource, NewsArticle, AIHighlight
import logging
import io
import sys

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Run the complete news pipeline: fetch, analyze, and optionally auto-post'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fetch-only',
            action='store_true',
            help='Only fetch news, skip analysis and posting',
        )
        parser.add_argument(
            '--analyze-only',
            action='store_true',
            help='Only analyze existing articles, skip fetching and posting',
        )
        parser.add_argument(
            '--no-auto-post',
            action='store_true',
            help='Skip auto-posting step',
        )
        parser.add_argument(
            '--max-articles',
            type=int,
            default=100,
            help='Maximum articles to analyze',
        )
        parser.add_argument(
            '--max-posts',
            type=int,
            default=5,
            help='Maximum posts to create via auto-posting',
        )
        parser.add_argument(
            '--min-score',
            type=float,
            default=60.0,
            help='Minimum highlight score for analysis',
        )
        parser.add_argument(
            '--force-fetch',
            action='store_true',
            help='Force fetch from all sources regardless of schedule',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Starting News Pipeline')
        )
        self.stdout.write('='*60)
        
        pipeline_stats = {
            'start_time': timezone.now(),
            'fetched_articles': 0,
            'analyzed_articles': 0,
            'highlighted_articles': 0,
            'auto_posts_created': 0,
            'errors': []
        }
        
        try:
            # Step 1: Fetch News
            if not options['analyze_only']:
                self.stdout.write('\n📰 STEP 1: Fetching News Articles')
                self.stdout.write('-' * 40)
                fetch_stats = self.fetch_news(options['force_fetch'])
                pipeline_stats['fetched_articles'] = fetch_stats['total_articles']
                
                if fetch_stats['total_articles'] == 0:
                    self.stdout.write(
                        self.style.WARNING('No new articles fetched. Pipeline will continue with existing articles.')
                    )
            
            # Step 2: AI Analysis
            if not options['fetch_only']:
                self.stdout.write('\n🤖 STEP 2: AI Analysis & Highlighting')
                self.stdout.write('-' * 40)
                analysis_stats = self.analyze_articles(
                    options['max_articles'], 
                    options['min_score']
                )
                pipeline_stats['analyzed_articles'] = analysis_stats['processed']
                pipeline_stats['highlighted_articles'] = analysis_stats['highlighted']
            
            # Step 3: Auto-posting
            if not options['fetch_only'] and not options['analyze_only'] and not options['no_auto_post']:
                self.stdout.write('\n📱 STEP 3: Auto-posting')
                self.stdout.write('-' * 40)
                posting_stats = self.auto_post_articles(options['max_posts'])
                pipeline_stats['auto_posts_created'] = posting_stats['created_posts']
            
            # Final Summary
            self.display_pipeline_summary(pipeline_stats)
            
        except Exception as e:
            pipeline_stats['errors'].append(str(e))
            self.stdout.write(
                self.style.ERROR(f'Pipeline error: {e}')
            )
            logger.error(f"News pipeline error: {e}")

    def fetch_news(self, force_fetch=False):
        """Fetch news from all sources."""
        self.stdout.write('Fetching from news sources...')
        
        aggregator = NewsAggregatorService()
        
        if force_fetch:
            # Force fetch from all active sources
            sources = NewsSource.objects.filter(is_active=True)
            results = {}
            for source in sources:
                count = aggregator.fetch_from_source(source)
                results[source.name] = count
        else:
            results = aggregator.fetch_from_all_sources()
        
        total_articles = sum(results.values())
        
        if results:
            self.stdout.write(f'✅ Fetched {total_articles} new articles:')
            for source_name, count in results.items():
                if count > 0:
                    self.stdout.write(f'  • {source_name}: {count} articles')
        else:
            self.stdout.write('ℹ️  No sources were due for fetching')
        
        return {'total_articles': total_articles, 'source_results': results}

    def analyze_articles(self, max_articles, min_score):
        """Analyze articles with AI."""
        self.stdout.write(f'Analyzing up to {max_articles} articles...')
        
        # Capture the output of the analyze_news command
        old_stdout = sys.stdout
        sys.stdout = captured_output = io.StringIO()
        
        try:
            call_command(
                'analyze_news',
                limit=max_articles,
                unprocessed_only=True,
                min_score=min_score,
                verbosity=0
            )
        finally:
            sys.stdout = old_stdout
        
        # Parse the output to get stats
        output = captured_output.getvalue()
        
        # Extract numbers from the output (basic parsing)
        processed = 0
        highlighted = 0
        
        for line in output.split('\n'):
            if 'Processed:' in line:
                try:
                    processed = int(line.split('Processed:')[1].split('articles')[0].strip())
                except:
                    pass
            elif 'Highlighted:' in line:
                try:
                    highlighted = int(line.split('Highlighted:')[1].split('articles')[0].strip())
                except:
                    pass
        
        self.stdout.write(f'✅ Analyzed {processed} articles')
        self.stdout.write(f'⭐ Highlighted {highlighted} articles')
        
        return {'processed': processed, 'highlighted': highlighted}

    def auto_post_articles(self, max_posts):
        """Auto-post highlighted articles."""
        self.stdout.write(f'Creating up to {max_posts} auto-posts...')
        
        stats = auto_posting_service.process_auto_posting(max_posts_per_run=max_posts)
        
        if stats['created_posts'] > 0:
            self.stdout.write(f'✅ Created {stats["created_posts"]} auto-posts')
        else:
            self.stdout.write('ℹ️  No auto-posts created')
        
        if stats['skipped_posts'] > 0:
            self.stdout.write(f'⏭️  Skipped {stats["skipped_posts"]} posts')
        
        if stats['errors'] > 0:
            self.stdout.write(f'❌ {stats["errors"]} errors occurred')
        
        return stats

    def display_pipeline_summary(self, stats):
        """Display final pipeline summary."""
        duration = timezone.now() - stats['start_time']
        
        self.stdout.write('\n' + '='*60)
        self.stdout.write('📊 PIPELINE SUMMARY')
        self.stdout.write('='*60)
        
        self.stdout.write(f'⏱️  Duration: {duration.total_seconds():.1f} seconds')
        self.stdout.write(f'📰 Articles fetched: {stats["fetched_articles"]}')
        self.stdout.write(f'🤖 Articles analyzed: {stats["analyzed_articles"]}')
        self.stdout.write(f'⭐ Articles highlighted: {stats["highlighted_articles"]}')
        self.stdout.write(f'📱 Auto-posts created: {stats["auto_posts_created"]}')
        
        if stats['errors']:
            self.stdout.write(f'❌ Errors: {len(stats["errors"])}')
            for error in stats['errors']:
                self.stdout.write(f'   • {error}')
        
        # Current totals
        total_articles = NewsArticle.objects.count()
        total_highlights = AIHighlight.objects.count()
        active_sources = NewsSource.objects.filter(is_active=True).count()
        
        self.stdout.write('\n📈 CURRENT TOTALS:')
        self.stdout.write(f'   • Total articles in database: {total_articles}')
        self.stdout.write(f'   • Total highlights: {total_highlights}')
        self.stdout.write(f'   • Active news sources: {active_sources}')
        
        # Success message
        if stats['highlighted_articles'] > 0 or stats['auto_posts_created'] > 0:
            self.stdout.write(
                self.style.SUCCESS('\n🎉 Pipeline completed successfully!')
            )
        else:
            self.stdout.write(
                self.style.WARNING('\n⚠️  Pipeline completed but no new highlights or posts were created.')
            )
        
        self.stdout.write('\n💡 Next steps:')
        self.stdout.write('   • Check the news dashboard: /news-dashboard/')
        self.stdout.write('   • Review highlights: /news-highlights/')
        self.stdout.write('   • Configure account categories: /account-categories/')
        self.stdout.write('   • Schedule this command to run regularly (e.g., every hour)')
