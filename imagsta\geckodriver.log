1639750318678	geckodriver	INFO	Listening on 127.0.0.1:48037
1639750319180	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "35733" "-no-remote" "-profile" "/tmp/rust_mozprofiled4NSPc"
*** You are running in headless mode.
1639750320122	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiled4NSPc/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:35733/devtools/browser/6241694e-5cca-4abd-9980-5c96d29648c7
1639750322209	Marionette	INFO	Listening on port 44481
1639750322363	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639750340298	Marionette	INFO	Stopped listening on port 44481
1639750397770	geckodriver	INFO	Listening on 127.0.0.1:43539
1639750398283	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "52947" "-no-remote" "-profile" "/tmp/rust_mozprofileH8qj9W"
*** You are running in headless mode.
1639750399422	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileH8qj9W/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:52947/devtools/browser/bf082898-16ee-4082-9b06-e412ad1dfa2d
1639750401703	Marionette	INFO	Listening on port 44821
1639750401816	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639750415489	Marionette	INFO	Stopped listening on port 44821

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639750479829	geckodriver	INFO	Listening on 127.0.0.1:43393
1639750480343	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "60941" "-no-remote" "-profile" "/tmp/rust_mozprofile7EjL10"
*** You are running in headless mode.
1639750481485	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile7EjL10/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:60941/devtools/browser/6e5eb1c0-4ef7-4caa-ba6e-f8e0a0963405
1639750483615	Marionette	INFO	Listening on port 40765
1639750483709	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639750493647	Marionette	INFO	Stopped listening on port 40765
1639750528370	geckodriver	INFO	Listening on 127.0.0.1:60687
1639750528883	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "37055" "-no-remote" "-profile" "/tmp/rust_mozprofilewigjnU"
*** You are running in headless mode.
1639750530071	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilewigjnU/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:37055/devtools/browser/ef5014ab-30e6-4e6a-b7ed-282615f773d5
1639750532310	Marionette	INFO	Listening on port 42295
1639750532454	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639750558027	Marionette	INFO	Stopped listening on port 42295
1639750579399	geckodriver	INFO	Listening on 127.0.0.1:42993
1639750579913	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "57729" "-no-remote" "-profile" "/tmp/rust_mozprofileyX1zcq"
*** You are running in headless mode.
1639750580762	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileyX1zcq/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:57729/devtools/browser/b3f651a4-86ad-4096-900b-56e3b4e1cffe
1639750582444	Marionette	INFO	Listening on port 46711
1639750582550	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639750594462	Marionette	INFO	Stopped listening on port 46711

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639753003806	geckodriver	INFO	Listening on 127.0.0.1:49697
1639753004327	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "50699" "-no-remote" "-profile" "/tmp/rust_mozprofile6tRsu4"
*** You are running in headless mode.
1639753005740	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile6tRsu4/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:50699/devtools/browser/cb34d04d-1114-4368-964c-44ea9ee06450
1639753008126	Marionette	INFO	Listening on port 35175
1639753008166	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639753076090	geckodriver	INFO	Listening on 127.0.0.1:51647
1639753076605	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "55461" "-no-remote" "-profile" "/tmp/rust_mozprofileyAEgci"
*** You are running in headless mode.
1639753077427	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileyAEgci/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:55461/devtools/browser/77d2debd-d1e9-4378-acf7-9702c6591605
1639753079750	Marionette	INFO	Listening on port 34545
1639753079840	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
Exiting due to channel error.
Exiting due to channel error.
Exiting due to channel error.
Exiting due to channel error.
Exiting due to channel error.
Exiting due to channel error.
1639753112422	geckodriver	INFO	Listening on 127.0.0.1:34151
1639753112928	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "51057" "-no-remote" "-profile" "/tmp/rust_mozprofilezL5RdT"
*** You are running in headless mode.
1639753114082	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilezL5RdT/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:51057/devtools/browser/9fc71078-0e6f-4023-886d-d6574c4c71b5
1639753115910	Marionette	INFO	Listening on port 38287
1639753115961	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639753128558	Marionette	INFO	Stopped listening on port 38287

###!!! [Child][MessageChannel] Error: (msgtype=0x390119,name=PContent::Msg_RecordDiscardedData) Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639753153892	geckodriver	INFO	Listening on 127.0.0.1:37201
1639753154409	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "40975" "-no-remote" "-profile" "/tmp/rust_mozprofile6WLrMn"
*** You are running in headless mode.
1639753155674	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile6WLrMn/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:40975/devtools/browser/ff2d992e-ff9e-4132-9ebe-f546e0da7b5a
1639753157991	Marionette	INFO	Listening on port 38595
1639753158065	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639753168893	Marionette	INFO	Stopped listening on port 38595
1639754748308	geckodriver	INFO	Listening on 127.0.0.1:40595
1639754748810	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "50939" "-no-remote" "-profile" "/tmp/rust_mozprofilekfkuKU"
*** You are running in headless mode.
1639754749565	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilekfkuKU/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:50939/devtools/browser/ed6c9b06-bfab-4c41-be84-ae28078c585f
1639754751804	Marionette	INFO	Listening on port 45915
1639754751853	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639754760900	Marionette	INFO	Stopped listening on port 45915

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639754774005	geckodriver	INFO	Listening on 127.0.0.1:42331
1639754774523	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "43601" "-no-remote" "-profile" "/tmp/rust_mozprofileBuIfX8"
*** You are running in headless mode.
1639754775259	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileBuIfX8/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:43601/devtools/browser/0c363d1b-fa0d-42aa-8f02-ea411821dd97
1639754777025	Marionette	INFO	Listening on port 40837
1639754777076	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639754786726	Marionette	INFO	Stopped listening on port 40837

###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x230030,name=PBrowser::Msg___delete__) Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv

1639754794956	geckodriver	INFO	Listening on 127.0.0.1:40767
1639754795472	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "51115" "-no-remote" "-profile" "/tmp/rust_mozprofile4y8qLt"
*** You are running in headless mode.
1639754796365	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile4y8qLt/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:51115/devtools/browser/2e2246fe-2910-45fe-b2cb-454649f6a91c
1639754798548	Marionette	INFO	Listening on port 44339
1639754798608	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639754811414	Marionette	INFO	Stopped listening on port 44339

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639762122167	geckodriver	INFO	Listening on 127.0.0.1:40895
1639762122668	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58215" "-no-remote" "-profile" "/tmp/rust_mozprofile9BIGno"
*** You are running in headless mode.
1639762123469	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile9BIGno/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58215/devtools/browser/d5c46009-f86f-4c95-9fad-2507c8260756
1639762125478	Marionette	INFO	Listening on port 40779
1639762125617	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639762140502	Marionette	INFO	Stopped listening on port 40779

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639765270489	geckodriver	INFO	Listening on 127.0.0.1:55443
1639765270990	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58639" "-no-remote" "-profile" "/tmp/rust_mozprofile7zdn4f"
*** You are running in headless mode.
1639765272157	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile7zdn4f/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58639/devtools/browser/0991bf20-a56a-4fc4-bd7a-1251b03d2081
1639765274281	Marionette	INFO	Listening on port 39901
1639765274355	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639765289872	Marionette	INFO	Stopped listening on port 39901
1639765552988	geckodriver	INFO	Listening on 127.0.0.1:56767
1639765553498	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "41289" "-no-remote" "-profile" "/tmp/rust_mozprofileoqDDNE"
*** You are running in headless mode.
1639765554413	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileoqDDNE/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:41289/devtools/browser/1cc59636-afd1-424d-9857-7b1c433e9761
1639765556750	Marionette	INFO	Listening on port 34429
1639765556847	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639765567855	Marionette	INFO	Stopped listening on port 34429
1639765595264	geckodriver	INFO	Listening on 127.0.0.1:47227
1639765595277	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "51091" "-no-remote" "-profile" "/tmp/rust_mozprofile9KJcDj"
*** You are running in headless mode.
1639765596244	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile9KJcDj/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:51091/devtools/browser/34f7ca47-8f42-4946-832d-cbb845dc0bc4
1639765598313	Marionette	INFO	Listening on port 45527
1639765598460	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639765611645	Marionette	INFO	Stopped listening on port 45527
1639765765152	geckodriver	INFO	Listening on 127.0.0.1:60945
1639765765663	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "56363" "-no-remote" "-profile" "/tmp/rust_mozprofile98IdTA"
*** You are running in headless mode.
1639765766386	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile98IdTA/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:56363/devtools/browser/d02f7614-9353-4ac8-8272-a01dbb508202
1639765768395	Marionette	INFO	Listening on port 45099
1639765768530	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639765782093	Marionette	INFO	Stopped listening on port 45099

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639765931899	geckodriver	INFO	Listening on 127.0.0.1:42541
1639765932417	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "55977" "-no-remote" "-profile" "/tmp/rust_mozprofilejAAjC0"
*** You are running in headless mode.
1639765933196	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilejAAjC0/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:55977/devtools/browser/c4cbc3ae-78f9-4425-a2c0-91955c01f24d
1639765935379	Marionette	INFO	Listening on port 40159
1639765935476	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639765948694	Marionette	INFO	Stopped listening on port 40159

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639766096006	geckodriver	INFO	Listening on 127.0.0.1:44991
1639766096525	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "57769" "-no-remote" "-profile" "/tmp/rust_mozprofileSlmznr"
*** You are running in headless mode.
1639766097138	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileSlmznr/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:57769/devtools/browser/8fbf5b2b-eaeb-4974-9bf8-958438a4d180
1639766099303	Marionette	INFO	Listening on port 45551
1639766099406	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639766113478	Marionette	INFO	Stopped listening on port 45551

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639766177042	geckodriver	INFO	Listening on 127.0.0.1:35587
1639766177058	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "60029" "-no-remote" "-profile" "/tmp/rust_mozprofilekjxNXb"
*** You are running in headless mode.
1639766177816	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilekjxNXb/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:60029/devtools/browser/04455bb9-f782-44ed-b3a9-262b9c223113
1639766179784	Marionette	INFO	Listening on port 45627
1639766179887	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639766192883	Marionette	INFO	Stopped listening on port 45627
1639766214176	geckodriver	INFO	Listening on 127.0.0.1:56201
1639766214695	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "53845" "-no-remote" "-profile" "/tmp/rust_mozprofilegDXINx"
*** You are running in headless mode.
1639766215490	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilegDXINx/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53845/devtools/browser/6304328d-4ad8-48e3-908a-3c61225fc99f
1639766217402	Marionette	INFO	Listening on port 45833
1639766217432	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639766227782	Marionette	INFO	Stopped listening on port 45833
1639766368680	geckodriver	INFO	Listening on 127.0.0.1:42609
1639766369197	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58219" "-no-remote" "-profile" "/tmp/rust_mozprofileaB512g"
*** You are running in headless mode.
1639766369997	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileaB512g/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58219/devtools/browser/ef0581fa-7333-4dea-928f-63303ef8c92d
1639766371945	Marionette	INFO	Listening on port 34069
1639766372040	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639766381703	Marionette	INFO	Stopped listening on port 34069
1639766442881	geckodriver	INFO	Listening on 127.0.0.1:37623
1639766442896	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "41009" "-no-remote" "-profile" "/tmp/rust_mozprofile9A39s1"
*** You are running in headless mode.
1639766443639	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile9A39s1/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:41009/devtools/browser/f9a13cad-66be-4482-ac4c-d29a7a44ad64
1639766445695	Marionette	INFO	Listening on port 34985
1639766445733	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639766459495	Marionette	INFO	Stopped listening on port 34985

###!!! [Child][MessageChannel] Error: (msgtype=0x6C0025,name=PNecko::Msg_RemoveRequestContext) Channel closing: too late to send/recv, messages will be lost

1639804360759	geckodriver	INFO	Listening on 127.0.0.1:39485
1639804361280	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39339" "-no-remote" "-profile" "/tmp/rust_mozprofileZuIv8a"
*** You are running in headless mode.
1639804362610	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileZuIv8a/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39339/devtools/browser/b55b885b-78ec-4a35-917e-3381aa895839
1639804364821	Marionette	INFO	Listening on port 41989
1639804364918	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639804379633	Marionette	INFO	Stopped listening on port 41989
1639804486536	geckodriver	INFO	Listening on 127.0.0.1:55439
1639804487038	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39735" "-no-remote" "-profile" "/tmp/rust_mozprofiles2v6UP"
*** You are running in headless mode.
1639804488324	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiles2v6UP/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39735/devtools/browser/90bc91fa-e898-4d56-914e-c36a2ea19d64
1639804491325	Marionette	INFO	Listening on port 46337
1639804491393	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639804507629	Marionette	INFO	Stopped listening on port 46337

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x230030,name=PBrowser::Msg___delete__) Channel closing: too late to send/recv, messages will be lost

1639804692472	geckodriver	INFO	Listening on 127.0.0.1:47413
1639804692488	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "32867" "-no-remote" "-profile" "/tmp/rust_mozprofilefay3Lf"
*** You are running in headless mode.
1639804693272	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilefay3Lf/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:32867/devtools/browser/3bc9fce5-34d0-4fcb-a4e9-066177f71ecb
1639804695638	Marionette	INFO	Listening on port 41375
1639804695763	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
[GFX1-]: Unable to load glyph: -1
[2021-12-18T05:18:35Z ERROR webrender::platform::unix::font] Unable to load glyph: -1
1639804717258	Marionette	INFO	Stopped listening on port 41375
1639804728140	geckodriver	INFO	Listening on 127.0.0.1:40193
1639804728654	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "33957" "-no-remote" "-profile" "/tmp/rust_mozprofileNL5T9G"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639804731454	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileNL5T9G/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:33957/devtools/browser/3360d1b8-c3e7-4930-adf6-8460dbdb3e4e
1639804733561	Marionette	INFO	Listening on port 36069
1639804733600	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639804752531	Marionette	INFO	Stopped listening on port 36069
1639804764426	geckodriver	INFO	Listening on 127.0.0.1:52987
1639804764956	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "36565" "-no-remote" "-profile" "/tmp/rust_mozprofilejBOHC4"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639804767909	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilejBOHC4/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:36565/devtools/browser/e17a8435-e0d4-4dec-a1c6-0e6b030affc6
1639804770234	Marionette	INFO	Listening on port 36921
1639804770304	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639804780691	Marionette	INFO	Stopped listening on port 36921
1639807103613	geckodriver	INFO	Listening on 127.0.0.1:49303
1639807104134	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "48753" "-no-remote" "-profile" "/tmp/rust_mozprofileCI1N4f"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639807105520	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileCI1N4f/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:48753/devtools/browser/ad7b99e3-4fa6-471e-be50-8425ff669ee5
1639807108206	Marionette	INFO	Listening on port 35055
1639807108263	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639807122274	Marionette	INFO	Stopped listening on port 35055
1639807201748	geckodriver	INFO	Listening on 127.0.0.1:46021
1639807202259	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "53723" "-no-remote" "-profile" "/tmp/rust_mozprofiletNKCS6"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639807204293	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiletNKCS6/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53723/devtools/browser/5518eec5-e7d6-4e6c-a0b3-fec181ebc9cc
1639807206249	Marionette	INFO	Listening on port 45341
1639807206299	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639807218377	Marionette	INFO	Stopped listening on port 45341

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639807333603	geckodriver	INFO	Listening on 127.0.0.1:39475
1639807334119	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "37301" "-no-remote" "-profile" "/tmp/rust_mozprofiled5Y5YR"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639807336159	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiled5Y5YR/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:37301/devtools/browser/c56366a5-ffa3-486b-ba40-f2d0ef2c2234
1639807338352	Marionette	INFO	Listening on port 45567
1639807338537	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639807350180	Marionette	INFO	Stopped listening on port 45567
1639807425955	geckodriver	INFO	Listening on 127.0.0.1:39017
1639807426473	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "42829" "-no-remote" "-profile" "/tmp/rust_mozprofilevgD971"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639807428582	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilevgD971/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42829/devtools/browser/cb80558a-74a8-4120-be22-90f9b35ecaa2
1639807430601	Marionette	INFO	Listening on port 37729
1639807430713	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639807441431	Marionette	INFO	Stopped listening on port 37729
1639807726118	geckodriver	INFO	Listening on 127.0.0.1:48201
1639807726138	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "34381" "-no-remote" "-profile" "/tmp/rust_mozprofileSN9sRu"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639807727354	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileSN9sRu/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:34381/devtools/browser/34aed710-38ad-4497-80e3-876d3dce9064
1639807729701	Marionette	INFO	Listening on port 33033
1639807729783	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639807741805	Marionette	INFO	Stopped listening on port 33033

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639807927740	geckodriver	INFO	Listening on 127.0.0.1:34723
1639807928256	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "50425" "-no-remote" "-profile" "/tmp/rust_mozprofileKP6O3Q"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639807929697	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileKP6O3Q/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:50425/devtools/browser/f59fa829-0358-4fc4-bc1b-c4ed94b0bc56
1639807931474	Marionette	INFO	Listening on port 46603
1639807931591	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639807943329	Marionette	INFO	Stopped listening on port 46603
1639807952127	geckodriver	INFO	Listening on 127.0.0.1:35097
1639807952643	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "53553" "-no-remote" "-profile" "/tmp/rust_mozprofile97j91v"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639807954159	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile97j91v/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53553/devtools/browser/a6a323a2-58b4-498e-b863-9a6aa0b913ff
1639807956076	Marionette	INFO	Listening on port 37325
1639807956168	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639807956830	Marionette	INFO	Stopped listening on port 37325
console.warn: TopSitesFeed: Failed to fetch data from Contile server: NetworkError when attempting to fetch resource.
JavaScript error: chrome://remote/content/marionette/cert.js, line 55: NS_ERROR_NOT_AVAILABLE: Component returned failure code: 0x80040111 (NS_ERROR_NOT_AVAILABLE) [nsICertOverrideService.setDisableAllSecurityChecksAndLetAttackersInterceptMyData]

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639808941726	geckodriver	INFO	Listening on 127.0.0.1:41635
1639808942241	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "37821" "-no-remote" "-profile" "/tmp/rust_mozprofilevgbolh"
*** You are running in headless mode.
1639808943025	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilevgbolh/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:37821/devtools/browser/849a870d-30b4-4598-befc-f4ca84eafc2a
1639808945021	Marionette	INFO	Listening on port 40249
1639808945935	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639808953180	Marionette	INFO	Stopped listening on port 40249
1639810247442	geckodriver	INFO	Listening on 127.0.0.1:55523
1639810247465	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "48773" "-no-remote" "-profile" "/tmp/rust_mozprofiledfaYoL"
*** You are running in headless mode.
1639810248417	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiledfaYoL/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:48773/devtools/browser/fc990e3a-19d4-421b-95e3-1ae766b06973
1639810250460	Marionette	INFO	Listening on port 38975
1639810250500	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639810256446	Marionette	INFO	Stopped listening on port 38975

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639810293682	geckodriver	INFO	Listening on 127.0.0.1:57087
1639810294199	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "45973" "-no-remote" "-profile" "/tmp/rust_mozprofilenpC00D"
*** You are running in headless mode.
1639810295042	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilenpC00D/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:45973/devtools/browser/fad1992b-d828-4e51-aa18-a291d70ce3d2
1639810297045	Marionette	INFO	Listening on port 38757
1639810297169	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639810309628	Marionette	INFO	Stopped listening on port 38757
1639810357312	geckodriver	INFO	Listening on 127.0.0.1:52487
1639810357832	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "47379" "-no-remote" "-profile" "/tmp/rust_mozprofile4uPRBm"
*** You are running in headless mode.
1639810358764	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile4uPRBm/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:47379/devtools/browser/0aa20eb1-07cc-442b-a4e4-1f2ee4e7566e
1639810360724	Marionette	INFO	Listening on port 41825
1639810360769	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639810373343	Marionette	INFO	Stopped listening on port 41825

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv

1639811986757	geckodriver	INFO	Listening on 127.0.0.1:39349
1639811987273	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "47819" "-no-remote" "-profile" "/tmp/rust_mozprofilea27xry"
*** You are running in headless mode.
1639811988193	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilea27xry/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:47819/devtools/browser/bc6adbc4-6aa3-45b2-903b-bdb5a1403485
1639811990108	Marionette	INFO	Listening on port 37571
1639811990322	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639812004397	Marionette	INFO	Stopped listening on port 37571

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639812030970	geckodriver	INFO	Listening on 127.0.0.1:47609
1639812031491	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "40149" "-no-remote" "-profile" "/tmp/rust_mozprofiletGY9Sx"
*** You are running in headless mode.
1639812032550	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiletGY9Sx/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:40149/devtools/browser/5805fe73-c857-4a4d-95e1-bd98ff860eac
1639812034691	Marionette	INFO	Listening on port 43559
1639812034723	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639812052272	Marionette	INFO	Stopped listening on port 43559
1639812110320	geckodriver	INFO	Listening on 127.0.0.1:44415
1639812110838	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "55101" "-no-remote" "-profile" "/tmp/rust_mozprofile3PlPhD"
*** You are running in headless mode.
1639812111628	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile3PlPhD/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:55101/devtools/browser/647c6005-4380-4bb6-a698-8ab664685a97
1639812113648	Marionette	INFO	Listening on port 35163
1639812113770	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639812130158	Marionette	INFO	Stopped listening on port 35163

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639827636682	geckodriver	INFO	Listening on 127.0.0.1:37141
1639827637179	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "59223" "-no-remote" "-profile" "/tmp/rust_mozprofileT33BhL"
*** You are running in headless mode.
1639827638080	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileT33BhL/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:59223/devtools/browser/e9e5d656-71d7-49eb-819d-bbb451083a1d
1639827640521	Marionette	INFO	Listening on port 36679
1639827640621	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639827653365	Marionette	INFO	Stopped listening on port 36679
1639827678132	geckodriver	INFO	Listening on 127.0.0.1:47821
1639827678645	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "36845" "-no-remote" "-profile" "/tmp/rust_mozprofilelJilt9"
*** You are running in headless mode.
1639827679909	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilelJilt9/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:36845/devtools/browser/64f49b81-152b-4a08-9f2f-8a66a22be7fd
1639827682466	Marionette	INFO	Listening on port 44381
1639827682638	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639827697981	Marionette	INFO	Stopped listening on port 44381

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639828021988	geckodriver	INFO	Listening on 127.0.0.1:54789
1639828021992	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "48335" "-no-remote" "-profile" "/tmp/rust_mozprofileZ4NHQ0"
*** You are running in headless mode.
1639828022707	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileZ4NHQ0/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:48335/devtools/browser/7b485d99-ca2f-4b94-8087-b47a9d5bd099
1639828025066	Marionette	INFO	Listening on port 36939
1639828025175	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639828027037	Marionette	INFO	Stopped listening on port 36939
JavaScript error: resource://gre/modules/UrlClassifierListManager.jsm, line 691: TypeError: this.tablesData[table] is undefined
1639828144942	geckodriver	INFO	Listening on 127.0.0.1:57331
1639828145461	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "36609" "-no-remote" "-profile" "/tmp/rust_mozprofiles3wSvu"
*** You are running in headless mode.
1639828146309	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiles3wSvu/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:36609/devtools/browser/0c749569-5517-456a-9671-e581663d1427
1639828148417	Marionette	INFO	Listening on port 44341
1639828148504	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639828163248	Marionette	INFO	Stopped listening on port 44341
1639828860161	geckodriver	INFO	Listening on 127.0.0.1:37193
1639828860665	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "37265" "-no-remote" "-profile" "/tmp/rust_mozprofilee9U1nd"
*** You are running in headless mode.
1639828862062	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilee9U1nd/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:37265/devtools/browser/826f5904-33b7-4693-abd3-bcfda75f5fd8
1639828864799	Marionette	INFO	Listening on port 38797
1639828864956	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639828877703	Marionette	INFO	Stopped listening on port 38797
1639836931654	geckodriver	INFO	Listening on 127.0.0.1:44841
1639836932153	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42331" "-no-remote" "-profile" "/tmp/rust_mozprofileTixgLE"
*** You are running in headless mode.
1639836932943	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileTixgLE/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42331/devtools/browser/a63101a1-c490-4d4b-8b83-b090294e7ea0
1639836934805	Marionette	INFO	Listening on port 32985
1639836934884	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639836953227	Marionette	INFO	Stopped listening on port 32985

###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639837428865	geckodriver	INFO	Listening on 127.0.0.1:48041
1639837429479	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "48191" "-no-remote" "-profile" "/tmp/rust_mozprofile8qzJm5"
*** You are running in headless mode.
1639837430281	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile8qzJm5/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:48191/devtools/browser/f964cb1d-ad04-46af-a7f6-5c66f5796e3a
1639837432137	Marionette	INFO	Listening on port 44999
1639837432212	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639837449060	Marionette	INFO	Stopped listening on port 44999

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639838283417	geckodriver	INFO	Listening on 127.0.0.1:48919
1639838283932	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "34741" "-no-remote" "-profile" "/tmp/rust_mozprofileAvHpZY"
*** You are running in headless mode.
1639838284540	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileAvHpZY/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:34741/devtools/browser/001c492f-4640-4a84-963b-f91720a721c9
1639838286960	Marionette	INFO	Listening on port 43827
1639838287072	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639838294326	geckodriver	INFO	Listening on 127.0.0.1:60091
1639838294331	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "34771" "-no-remote" "-profile" "/tmp/rust_mozprofilelP2jOu"
*** You are running in headless mode.
1639838295360	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilelP2jOu/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:34771/devtools/browser/6942c8ca-c196-4bbb-a935-f64586997384
1639838298359	Marionette	INFO	Listening on port 34773
1639838298493	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639838305259	Marionette	INFO	Stopped listening on port 43827
1639838314715	Marionette	INFO	Stopped listening on port 34773

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639838337258	geckodriver	INFO	Listening on 127.0.0.1:40519
1639838337797	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "35693" "-no-remote" "-profile" "/tmp/rust_mozprofilehTqtUJ"
*** You are running in headless mode.
1639838338880	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilehTqtUJ/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:35693/devtools/browser/90f53450-b9c6-4cac-9614-11021e8f335f
1639838340752	Marionette	INFO	Listening on port 36361
1639838340827	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639838354851	Marionette	INFO	Stopped listening on port 36361

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639839252340	geckodriver	INFO	Listening on 127.0.0.1:59737
1639839252858	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42505" "-no-remote" "-profile" "/tmp/rust_mozprofileaFO40j"
*** You are running in headless mode.
1639839253666	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileaFO40j/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42505/devtools/browser/4eac9d5e-1776-4655-8f5a-1ccc62b7fe5e
1639839255671	Marionette	INFO	Listening on port 39635
1639839255790	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839276459	Marionette	INFO	Stopped listening on port 39635

###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639839374229	geckodriver	INFO	Listening on 127.0.0.1:43531
1639839374747	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42005" "-no-remote" "-profile" "/tmp/rust_mozprofilemj8cdT"
*** You are running in headless mode.
1639839375621	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilemj8cdT/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42005/devtools/browser/d21d4f91-24c1-4662-9b2e-9bf968161702
1639839377987	Marionette	INFO	Listening on port 33405
1639839378103	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839392159	Marionette	INFO	Stopped listening on port 33405
1639839425926	geckodriver	INFO	Listening on 127.0.0.1:54713
1639839425932	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "52257" "-no-remote" "-profile" "/tmp/rust_mozprofileiz82rm"
*** You are running in headless mode.
1639839426614	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileiz82rm/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:52257/devtools/browser/66225a27-ed34-46c6-8fee-43f05eb51b02
1639839428668	Marionette	INFO	Listening on port 33971
1639839428816	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839445517	Marionette	INFO	Stopped listening on port 33971

###!!! [Child][MessageChannel] Error: (msgtype=0x6C0025,name=PNecko::Msg_RemoveRequestContext) Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv

1639839486193	geckodriver	INFO	Listening on 127.0.0.1:38419
1639839486699	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42295" "-no-remote" "-profile" "/tmp/rust_mozprofilebXzs0w"
*** You are running in headless mode.
1639839487373	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilebXzs0w/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42295/devtools/browser/d303ae69-e064-40c2-a7ac-5d880cbd793c
1639839489559	Marionette	INFO	Listening on port 39535
1639839489649	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839509103	Marionette	INFO	Stopped listening on port 39535

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639839523733	geckodriver	INFO	Listening on 127.0.0.1:40789
1639839523750	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "53153" "-no-remote" "-profile" "/tmp/rust_mozprofilemJown4"
*** You are running in headless mode.
1639839524490	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilemJown4/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53153/devtools/browser/8ab80977-3f0a-4cec-a064-5a26e030dd71
1639839526558	Marionette	INFO	Listening on port 46729
1639839526596	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839539622	Marionette	INFO	Stopped listening on port 46729

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639839574585	geckodriver	INFO	Listening on 127.0.0.1:58771
1639839575103	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "57201" "-no-remote" "-profile" "/tmp/rust_mozprofileQVkC5T"
*** You are running in headless mode.
1639839575770	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileQVkC5T/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:57201/devtools/browser/8c5b70c2-6ec1-4de1-91b6-455c30dee0c3
1639839577960	Marionette	INFO	Listening on port 36927
1639839578072	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839592750	Marionette	INFO	Stopped listening on port 36927

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639839611001	geckodriver	INFO	Listening on 127.0.0.1:39359
1639839611018	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "33321" "-no-remote" "-profile" "/tmp/rust_mozprofileNybJRM"
*** You are running in headless mode.
1639839611735	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileNybJRM/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:33321/devtools/browser/90b2d46a-7259-460c-86bb-94bb00c306a8
1639839613488	Marionette	INFO	Listening on port 45521
1639839613549	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839625494	Marionette	INFO	Stopped listening on port 45521
1639839644253	geckodriver	INFO	Listening on 127.0.0.1:54311
1639839644267	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "51163" "-no-remote" "-profile" "/tmp/rust_mozprofileBjXhg8"
*** You are running in headless mode.
1639839645009	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileBjXhg8/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:51163/devtools/browser/e74718a9-5534-4f54-9d4c-abd7bbe478e7
1639839646982	Marionette	INFO	Listening on port 44073
1639839647114	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839651856	geckodriver	INFO	Listening on 127.0.0.1:56733
1639839651886	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "32869" "-no-remote" "-profile" "/tmp/rust_mozprofileBG4pqt"
*** You are running in headless mode.
1639839652865	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileBG4pqt/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:32869/devtools/browser/ec477e75-f4dc-4c1a-ab37-5184bd811366
1639839656037	Marionette	INFO	Listening on port 41037
1639839656151	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
[GFX1-]: Unable to load glyph: -1
[2021-12-18T15:01:03Z ERROR webrender::platform::unix::font] Unable to load glyph: -1
1639839673173	Marionette	INFO	Stopped listening on port 44073

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x230030,name=PBrowser::Msg___delete__) Channel closing: too late to send/recv, messages will be lost

1639839679896	geckodriver	INFO	Listening on 127.0.0.1:44195
1639839679915	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "43499" "-no-remote" "-profile" "/tmp/rust_mozprofileiEJOx4"
*** You are running in headless mode.
1639839680851	Marionette	INFO	Stopped listening on port 41037
1639839680971	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileiEJOx4/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:43499/devtools/browser/59593db1-eee9-4528-99c8-53e62ce27450
1639839683112	Marionette	INFO	Listening on port 40099
1639839683169	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839683735	geckodriver	INFO	Listening on 127.0.0.1:57879
1639839684243	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "56337" "-no-remote" "-profile" "/tmp/rust_mozprofileUzmxpN"
*** You are running in headless mode.
1639839685486	Marionette	INFO	Marionette enabled
1639839686263	geckodriver	INFO	Listening on 127.0.0.1:44485
1639839686776	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "33243" "-no-remote" "-profile" "/tmp/rust_mozprofileCFkt8h"
*** You are running in headless mode.
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileUzmxpN/search.json.mozlz4", (void 0)))
1639839688658	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
DevTools listening on ws://localhost:56337/devtools/browser/74939b39-dc82-47f9-82fc-68f004cc7ff8
1639839691664	Marionette	INFO	Listening on port 45785
1639839691796	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileCFkt8h/search.json.mozlz4", (void 0)))
1639839692880	geckodriver	INFO	Listening on 127.0.0.1:37015
1639839693374	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "57019" "-no-remote" "-profile" "/tmp/rust_mozprofilewxxNTA"
*** You are running in headless mode.
DevTools listening on ws://localhost:33243/devtools/browser/625c998b-ce08-453c-8b36-c904d820d4f2
1639839695141	Marionette	INFO	Listening on port 40679
1639839695229	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839695376	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilewxxNTA/search.json.mozlz4", (void 0)))
1639839698914	Marionette	WARN	TimedPromise timed out after 500 ms: stacktrace:
TimedPromise/<@chrome://remote/content/marionette/sync.js:238:19
TimedPromise@chrome://remote/content/marionette/sync.js:223:10
interaction.flushEventLoop@chrome://remote/content/marionette/interaction.js:431:10
webdriverClickElement@chrome://remote/content/marionette/interaction.js:179:31
DevTools listening on ws://localhost:57019/devtools/browser/4209eaf1-73ec-4870-9b34-23952d0d14dd
1639839701815	Marionette	INFO	Listening on port 36167
1639839702001	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639839719914	Marionette	INFO	Stopped listening on port 40099

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639839731684	Marionette	INFO	Stopped listening on port 40679

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639839735471	Marionette	INFO	Stopped listening on port 45785

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639839740661	Marionette	INFO	Stopped listening on port 36167

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639995614916	geckodriver	INFO	Listening on 127.0.0.1:47141
1639995615417	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "56567" "-no-remote" "-profile" "/tmp/rust_mozprofilerxBn1j"
*** You are running in headless mode.
1639995617513	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilerxBn1j/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:56567/devtools/browser/057fdeea-7497-4e3f-8980-9bbd85fb75b1
1639995620116	Marionette	INFO	Listening on port 34053
1639995620157	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639995637071	Marionette	INFO	Stopped listening on port 34053
1639995765578	geckodriver	INFO	Listening on 127.0.0.1:52379
1639995766095	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "53639" "-no-remote" "-profile" "/tmp/rust_mozprofileIiHrjM"
*** You are running in headless mode.
1639995767947	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileIiHrjM/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53639/devtools/browser/a27d2909-3e61-479b-9a1f-708586525d07
1639995769986	Marionette	INFO	Listening on port 43655
1639995770177	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639995777206	geckodriver	INFO	Listening on 127.0.0.1:59965
1639995777728	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39669" "-no-remote" "-profile" "/tmp/rust_mozprofileRPsC35"
*** You are running in headless mode.
1639995779847	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileRPsC35/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39669/devtools/browser/f39fc880-ee4f-4028-ac54-0a9a493581aa
1639995783445	Marionette	INFO	Listening on port 43631
1639995783527	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639995786219	Marionette	INFO	Stopped listening on port 43655
1639995796919	Marionette	INFO	Stopped listening on port 43631

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1639995839439	geckodriver	INFO	Listening on 127.0.0.1:45915
1639995839958	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "46457" "-no-remote" "-profile" "/tmp/rust_mozprofilesWPvcD"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1639995843040	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilesWPvcD/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:46457/devtools/browser/b7ca2a68-1168-4b03-a4c7-6c65439eba68
1639995845177	Marionette	INFO	Listening on port 46177
1639995845209	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1639995860134	Marionette	INFO	Stopped listening on port 46177

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1640020486806	geckodriver	INFO	Listening on 127.0.0.1:60853
1640020487306	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "37485" "-no-remote" "-profile" "/tmp/rust_mozprofiletCLEKa"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1640020490419	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiletCLEKa/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:37485/devtools/browser/6032b9cb-441a-4372-9c35-698d239f2389
1640020492593	Marionette	INFO	Listening on port 41285
1640020492647	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1640020509843	Marionette	INFO	Stopped listening on port 41285
1640022309402	geckodriver	INFO	Listening on 127.0.0.1:56337
1640022309905	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "44441" "-no-remote" "-profile" "/tmp/rust_mozprofilenXosUH"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1640022312584	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilenXosUH/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:44441/devtools/browser/389f212c-16eb-4d3d-a78f-6d96f332dc28
1640022314640	Marionette	INFO	Listening on port 33745
1640022315244	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1640022332073	Marionette	INFO	Stopped listening on port 33745
1640088870742	geckodriver	INFO	Listening on 127.0.0.1:47215
1640088871246	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "35393" "-no-remote" "-profile" "/tmp/rust_mozprofile4ZyjNx"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1640088873879	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile4ZyjNx/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:35393/devtools/browser/15fc5981-c4f1-406a-97d7-d2122612738e
1640088877048	Marionette	INFO	Listening on port 33545
1640088877093	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1640088879046	Marionette	INFO	Stopped listening on port 33545
console.warn: TopSitesFeed: Failed to fetch data from Contile server: NetworkError when attempting to fetch resource.
JavaScript error: chrome://remote/content/marionette/cert.js, line 55: NS_ERROR_NOT_AVAILABLE: Component returned failure code: 0x80040111 (NS_ERROR_NOT_AVAILABLE) [nsICertOverrideService.setDisableAllSecurityChecksAndLetAttackersInterceptMyData]

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1640174379797	geckodriver	INFO	Listening on 127.0.0.1:57237
1640174380302	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "38657" "-no-remote" "-profile" "/tmp/rust_mozprofilefyWUI6"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1640174383947	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilefyWUI6/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:38657/devtools/browser/d2c2b5ee-caaf-4bbf-a2b5-304630cd113c
1640174386858	Marionette	INFO	Listening on port 42183
1640174386949	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1640174399084	Marionette	INFO	Stopped listening on port 42183
1640175987481	geckodriver	INFO	Listening on 127.0.0.1:46943
1640175987982	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "57503" "-no-remote" "-profile" "/tmp/rust_mozprofile7j9KtM"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1640175989343	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile7j9KtM/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:57503/devtools/browser/2572d8e5-c00e-4e81-9694-2a990ad97a99
1640175991268	Marionette	INFO	Listening on port 36621
1640175992250	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1640176010812	Marionette	INFO	Stopped listening on port 36621

###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1640810816883	geckodriver	INFO	Listening on 127.0.0.1:50051
1640810817391	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "45723" "-no-remote" "-profile" "/tmp/rust_mozprofileeGcFAw"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1640810819018	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileeGcFAw/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:45723/devtools/browser/e07bab89-e22e-4242-842a-c70eba3e3e56
1640810821831	Marionette	INFO	Listening on port 45619
1640810821932	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1640810835988	Marionette	INFO	Stopped listening on port 45619
1641076167867	geckodriver	INFO	Listening on 127.0.0.1:39831
1641076168370	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "43077" "-no-remote" "-profile" "/tmp/rust_mozprofileMT8kGZ"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1641076170173	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileMT8kGZ/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:43077/devtools/browser/ff406f71-5f7a-47b3-93e6-e59068f25247
1641076172672	Marionette	INFO	Listening on port 34857
1641076172709	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641076187941	Marionette	INFO	Stopped listening on port 34857
1641111956703	geckodriver	INFO	Listening on 127.0.0.1:42099
1641111957207	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "51523" "-no-remote" "-profile" "/tmp/rust_mozprofileizYwPb"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1641111958948	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileizYwPb/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:51523/devtools/browser/177ec911-c4d2-4eb7-808d-619c3c5db87f
1641111961498	Marionette	INFO	Listening on port 37817
1641111961544	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641111973775	Marionette	INFO	Stopped listening on port 37817
1641626803992	geckodriver	INFO	Listening on 127.0.0.1:56487
1641626804496	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "46623" "-no-remote" "-profile" "/tmp/rust_mozprofilehhIj8f"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1641626807526	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilehhIj8f/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:46623/devtools/browser/4117588b-d6f6-41ac-b5fd-e87399e6f7be
1641626810396	Marionette	INFO	Listening on port 36147
1641626810441	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641626812928	Marionette	INFO	Stopped listening on port 36147
JavaScript error: chrome://remote/content/marionette/cert.js, line 55: NS_ERROR_NOT_AVAILABLE: Component returned failure code: 0x80040111 (NS_ERROR_NOT_AVAILABLE) [nsICertOverrideService.setDisableAllSecurityChecksAndLetAttackersInterceptMyData]
1641628426492	geckodriver	INFO	Listening on 127.0.0.1:46937
1641628426510	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "--remote-debugging-port" "42505" "-no-remote" "-profile" "/tmp/rust_mozprofile4GK230"
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
ATTENTION: default value of option mesa_glthread overridden by environment.
1641628427727	Marionette	INFO	Marionette enabled
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile4GK230/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42505/devtools/browser/1d2842c0-cdf7-4856-bfcb-a48c753f0f5b
1641628429670	Marionette	INFO	Listening on port 43375
1641628429739	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641628441907	Marionette	INFO	Stopped listening on port 43375

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1641649827893	geckodriver	INFO	Listening on 127.0.0.1:39931
1641649828401	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "52913" "-no-remote" "-profile" "/tmp/rust_mozprofileLKm9ua"
*** You are running in headless mode.
1641649829575	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileLKm9ua/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:52913/devtools/browser/3fa8d4cd-824a-433d-ba04-572ba3f62a1f
1641649831979	Marionette	INFO	Listening on port 39267
1641649832075	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641649850901	Marionette	INFO	Stopped listening on port 39267

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv

1641650525080	geckodriver	INFO	Listening on 127.0.0.1:50067
1641650525580	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "55749" "-no-remote" "-profile" "/tmp/rust_mozprofile2xpyJW"
*** You are running in headless mode.
1641650526542	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile2xpyJW/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:55749/devtools/browser/908c7f0e-04d7-4baa-94b6-508816341203
1641650528859	Marionette	INFO	Listening on port 45911
1641650528920	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641650540455	Marionette	INFO	Stopped listening on port 45911

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1641657057623	geckodriver	INFO	Listening on 127.0.0.1:57145
1641657058130	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "53101" "-no-remote" "-profile" "/tmp/rust_mozprofilevHDgie"
*** You are running in headless mode.
1641657059792	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilevHDgie/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53101/devtools/browser/452a80eb-fd4e-4a9b-b507-666091d4d085
1641657062170	Marionette	INFO	Listening on port 46235
1641657062264	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641657080984	Marionette	INFO	Stopped listening on port 46235

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1641669154297	geckodriver	INFO	Listening on 127.0.0.1:41605
1641669154790	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "52161" "-no-remote" "-profile" "/tmp/rust_mozprofile3bNg0l"
*** You are running in headless mode.
1641669155608	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile3bNg0l/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:52161/devtools/browser/3a19781e-b8ad-49f9-8f82-01a93cd57903
1641669158173	Marionette	INFO	Listening on port 34695
1641669158235	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641669172004	Marionette	INFO	Stopped listening on port 34695
1641927677070	geckodriver	INFO	Listening on 127.0.0.1:40971
1641927677573	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "32953" "-no-remote" "-profile" "/tmp/rust_mozprofileygfnaO"
*** You are running in headless mode.
1641927678548	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileygfnaO/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:32953/devtools/browser/ba61fa61-0885-42aa-afef-23e6a692a1bd
1641927680947	Marionette	INFO	Listening on port 42345
1641927681005	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1641927694632	Marionette	INFO	Stopped listening on port 42345
1642060941320	geckodriver	INFO	Listening on 127.0.0.1:35889
1642060941850	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42607" "-no-remote" "-profile" "/tmp/rust_mozprofileQul1jS"
*** You are running in headless mode.
1642060942998	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileQul1jS/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42607/devtools/browser/f80df150-abbb-41a8-965f-4f0455e7e99a
1642060945084	Marionette	INFO	Listening on port 43197
1642060945290	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642060959836	Marionette	INFO	Stopped listening on port 43197

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642062080248	geckodriver	INFO	Listening on 127.0.0.1:50471
1642062080763	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "45113" "-no-remote" "-profile" "/tmp/rust_mozprofiletzRk1u"
*** You are running in headless mode.
1642062081718	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiletzRk1u/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:45113/devtools/browser/db6c43ea-042d-4ede-8f96-f3fdc4fb73d5
1642062083873	Marionette	INFO	Listening on port 33129
1642062084001	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642062102049	Marionette	INFO	Stopped listening on port 33129

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642062185061	geckodriver	INFO	Listening on 127.0.0.1:41451
1642062185070	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "36045" "-no-remote" "-profile" "/tmp/rust_mozprofileYTsRP8"
*** You are running in headless mode.
1642062185896	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileYTsRP8/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:36045/devtools/browser/213b3eb9-398c-4223-810f-5010acbf082e
1642062188239	Marionette	INFO	Listening on port 34859
1642062188330	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642062203750	Marionette	INFO	Stopped listening on port 34859
1642064476478	geckodriver	INFO	Listening on 127.0.0.1:40591
1642064476984	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "49013" "-no-remote" "-profile" "/tmp/rust_mozprofileHsBM7N"
*** You are running in headless mode.
1642064477916	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileHsBM7N/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:49013/devtools/browser/76db3cbb-2491-4dd5-a0ac-db648b4ed281
1642064480103	Marionette	INFO	Listening on port 42671
1642064480245	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642064496716	Marionette	INFO	Stopped listening on port 42671

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642064530812	geckodriver	INFO	Listening on 127.0.0.1:44501
1642064530833	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42481" "-no-remote" "-profile" "/tmp/rust_mozprofileqHmac5"
*** You are running in headless mode.
1642064531650	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileqHmac5/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42481/devtools/browser/787e8134-45d9-4893-9a1c-8ec0423ab4d5
1642064533844	Marionette	INFO	Listening on port 36855
1642064533967	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642064548429	Marionette	INFO	Stopped listening on port 36855
1642068817875	geckodriver	INFO	Listening on 127.0.0.1:47555
1642068818376	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "51513" "-no-remote" "-profile" "/tmp/rust_mozprofileChdKGA"
*** You are running in headless mode.
1642068819478	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileChdKGA/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:51513/devtools/browser/5766859d-1383-496b-9ccd-12f327223317
1642068821560	Marionette	INFO	Listening on port 43949
1642068821611	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642068833466	Marionette	INFO	Stopped listening on port 43949
1642068889738	geckodriver	INFO	Listening on 127.0.0.1:44357
1642068889765	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "51677" "-no-remote" "-profile" "/tmp/rust_mozprofilebSJuVw"
*** You are running in headless mode.
1642068890695	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilebSJuVw/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:51677/devtools/browser/e8d9a035-6112-42b9-8f07-32b44810bee7
1642068892790	Marionette	INFO	Listening on port 45253
1642068892937	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642068901911	Marionette	INFO	Stopped listening on port 45253

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642069096609	geckodriver	INFO	Listening on 127.0.0.1:41727
1642069096629	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "52137" "-no-remote" "-profile" "/tmp/rust_mozprofilej7U1Ae"
*** You are running in headless mode.
1642069097444	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilej7U1Ae/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:52137/devtools/browser/192f42c1-f8e7-4539-b806-43bd3bde6014
1642069099482	Marionette	INFO	Listening on port 37333
1642069099561	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642069118278	Marionette	INFO	Stopped listening on port 37333

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642069140090	geckodriver	INFO	Listening on 127.0.0.1:46327
1642069140613	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "47979" "-no-remote" "-profile" "/tmp/rust_mozprofileBYOXKX"
*** You are running in headless mode.
1642069141712	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileBYOXKX/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:47979/devtools/browser/969f6183-322c-46c8-a72a-101d9a4d07bb
1642069144266	Marionette	INFO	Listening on port 41279
1642069144416	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642069156469	Marionette	INFO	Stopped listening on port 41279

###!!! [Parent][MessageChannel] Error: (msgtype=0x390078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642200197305	geckodriver	INFO	Listening on 127.0.0.1:49211
1642200197818	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "50585" "-no-remote" "-profile" "/tmp/rust_mozprofileOusvt7"
*** You are running in headless mode.
1642200199381	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileOusvt7/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:50585/devtools/browser/2dd2b4da-09d6-4557-9d1a-72b3daafb1cc
1642200202473	Marionette	INFO	Listening on port 43605
1642200202643	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642200215852	Marionette	INFO	Stopped listening on port 43605
1642200649973	geckodriver	INFO	Listening on 127.0.0.1:43667
1642200650477	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "48101" "-no-remote" "-profile" "/tmp/rust_mozprofileKK4xdO"
*** You are running in headless mode.
1642200651375	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileKK4xdO/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:48101/devtools/browser/f8bbe860-9ef2-49c0-94d2-33937ae6c721
1642200653966	Marionette	INFO	Listening on port 46775
1642200654063	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642200666319	Marionette	INFO	Stopped listening on port 46775
1642200972075	geckodriver	INFO	Listening on 127.0.0.1:53491
1642200972592	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "54543" "-no-remote" "-profile" "/tmp/rust_mozprofile2QZU0B"
*** You are running in headless mode.
1642200973353	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile2QZU0B/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:54543/devtools/browser/f8a7085a-c0c7-43d0-98c1-59c089cc3fdd
1642200975448	Marionette	INFO	Listening on port 44395
1642200975529	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642200993988	Marionette	INFO	Stopped listening on port 44395
1642201034915	geckodriver	INFO	Listening on 127.0.0.1:50819
1642201034931	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "40539" "-no-remote" "-profile" "/tmp/rust_mozprofile69vT8d"
*** You are running in headless mode.
1642201035660	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile69vT8d/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:40539/devtools/browser/d5e93674-893f-49b2-9a47-07eaf1be0fa1
1642201037547	Marionette	INFO	Listening on port 42013
1642201037749	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642201053812	Marionette	INFO	Stopped listening on port 42013
1642202165743	geckodriver	INFO	Listening on 127.0.0.1:59447
1642202165755	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58473" "-no-remote" "-profile" "/tmp/rust_mozprofileyjczfa"
*** You are running in headless mode.
1642202166389	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileyjczfa/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58473/devtools/browser/e753a889-5988-430a-97c1-748876164caa
1642202168098	Marionette	INFO	Listening on port 45507
1642202168515	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642202184988	Marionette	INFO	Stopped listening on port 45507
1642227773071	geckodriver	INFO	Listening on 127.0.0.1:54727
1642227773576	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "47583" "-no-remote" "-profile" "/tmp/rust_mozprofileiD1N6k"
*** You are running in headless mode.
1642227775981	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileiD1N6k/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:47583/devtools/browser/0b34ae69-dec1-48b1-80f3-1ff7e71556ce
1642227779337	Marionette	INFO	Listening on port 38983
1642227779481	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
[GFX1-]: Unable to load glyph: -1
[2022-01-15T06:23:10Z ERROR webrender::platform::unix::font] Unable to load glyph: -1
1642227798806	Marionette	INFO	Stopped listening on port 38983
1642228108386	geckodriver	INFO	Listening on 127.0.0.1:48177
1642228108902	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "41535" "-no-remote" "-profile" "/tmp/rust_mozprofileRz9dT8"
*** You are running in headless mode.
1642228109808	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileRz9dT8/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:41535/devtools/browser/7992a157-b98c-4b21-b588-b67ba3240fcf
1642228112222	Marionette	INFO	Listening on port 44875
1642228112347	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642228133734	Marionette	INFO	Stopped listening on port 44875
1642231636767	geckodriver	INFO	Listening on 127.0.0.1:54945
1642231637274	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58547" "-no-remote" "-profile" "/tmp/rust_mozprofileAh0CsQ"
*** You are running in headless mode.
1642231638468	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileAh0CsQ/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58547/devtools/browser/6e485386-b8c9-4ae5-9a7d-accbc16f42f5
1642231640490	Marionette	INFO	Listening on port 37631
1642231640612	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
Exiting due to channel error.
Exiting due to channel error.
Exiting due to channel error.
1642231716048	geckodriver	INFO	Listening on 127.0.0.1:45991
1642231716573	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "34791" "-no-remote" "-profile" "/tmp/rust_mozprofilepD0V6w"
*** You are running in headless mode.
1642231717367	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilepD0V6w/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:34791/devtools/browser/a0dd8bb9-dc8e-4513-a94d-2b9cff3a68f6
1642231719470	Marionette	INFO	Listening on port 34815
1642231719533	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642231737537	Marionette	INFO	Stopped listening on port 34815

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642347736143	geckodriver	INFO	Listening on 127.0.0.1:46149
1642347736652	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "43273" "-no-remote" "-profile" "/tmp/rust_mozprofileSO2kxR"
*** You are running in headless mode.
1642347737981	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileSO2kxR/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:43273/devtools/browser/88a5cde4-471f-4724-9dd1-3cc9ad1746ee
1642347740182	Marionette	INFO	Listening on port 46391
1642347740302	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642347752621	Marionette	INFO	Stopped listening on port 46391

###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642348740416	geckodriver	INFO	Listening on 127.0.0.1:42249
1642348740433	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42307" "-no-remote" "-profile" "/tmp/rust_mozprofileD9rIZ0"
*** You are running in headless mode.
1642348741224	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileD9rIZ0/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42307/devtools/browser/98c108f4-a74c-4345-aad7-a985c5a70bbc
1642348743268	Marionette	INFO	Listening on port 42041
1642348743368	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642348759035	Marionette	INFO	Stopped listening on port 42041

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642349208927	geckodriver	INFO	Listening on 127.0.0.1:43173
1642349209444	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "35351" "-no-remote" "-profile" "/tmp/rust_mozprofilefeGA7o"
*** You are running in headless mode.
1642349210284	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilefeGA7o/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:35351/devtools/browser/7c5cb58d-2439-43f9-89a2-f312e018d711
1642349212226	Marionette	INFO	Listening on port 36745
1642349212275	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642349224196	Marionette	INFO	Stopped listening on port 36745
1642350593720	geckodriver	INFO	Listening on 127.0.0.1:49623
1642350594239	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39205" "-no-remote" "-profile" "/tmp/rust_mozprofileBhz6c3"
*** You are running in headless mode.
1642350594882	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileBhz6c3/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39205/devtools/browser/c93d4e7b-45de-4575-ada5-9a3dd7174221
1642350596794	Marionette	INFO	Listening on port 36197
1642350596868	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642350615261	Marionette	INFO	Stopped listening on port 36197

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642350856828	geckodriver	INFO	Listening on 127.0.0.1:44915
1642350857347	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "36769" "-no-remote" "-profile" "/tmp/rust_mozprofileCtZZWK"
*** You are running in headless mode.
1642350858409	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileCtZZWK/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:36769/devtools/browser/233aec1a-7a8b-46d9-bda3-606e73ac3712
1642350860303	Marionette	INFO	Listening on port 33201
1642350860380	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642350870457	Marionette	INFO	Stopped listening on port 33201

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642351095898	geckodriver	INFO	Listening on 127.0.0.1:38819
1642351095910	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39287" "-no-remote" "-profile" "/tmp/rust_mozprofile4Xjzlp"
*** You are running in headless mode.
1642351096599	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile4Xjzlp/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39287/devtools/browser/26b03b9a-d597-4ac4-938f-50e7bf1b01af
1642351098444	Marionette	INFO	Listening on port 33557
1642351098588	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642351115112	Marionette	INFO	Stopped listening on port 33557
1642351574132	geckodriver	INFO	Listening on 127.0.0.1:33205
1642351574652	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58877" "-no-remote" "-profile" "/tmp/rust_mozprofilejbPm9w"
*** You are running in headless mode.
1642351575367	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilejbPm9w/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58877/devtools/browser/5e442386-2e30-4963-9419-da1c02971dfe
1642351577182	Marionette	INFO	Listening on port 42787
1642351577293	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642351589427	Marionette	INFO	Stopped listening on port 42787
1642352289096	geckodriver	INFO	Listening on 127.0.0.1:44743
1642352289601	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "36997" "-no-remote" "-profile" "/tmp/rust_mozprofilePqTMnG"
*** You are running in headless mode.
1642352290293	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilePqTMnG/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:36997/devtools/browser/49122a5f-6c8c-4672-840b-0e27cea90130
1642352292238	Marionette	INFO	Listening on port 42659
1642352292338	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642352311078	Marionette	INFO	Stopped listening on port 42659

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642352643610	geckodriver	INFO	Listening on 127.0.0.1:43855
1642352644126	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "41933" "-no-remote" "-profile" "/tmp/rust_mozprofileoCMtyB"
*** You are running in headless mode.
1642352645271	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileoCMtyB/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:41933/devtools/browser/08d1d712-49b9-438f-9931-ed7b38f1f5e9
1642352647244	Marionette	INFO	Listening on port 34127
1642352647359	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642352661666	Marionette	INFO	Stopped listening on port 34127

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642352664928	geckodriver	INFO	Listening on 127.0.0.1:44805
1642352664947	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "38089" "-no-remote" "-profile" "/tmp/rust_mozprofilecuBlBu"
*** You are running in headless mode.
1642352665663	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilecuBlBu/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:38089/devtools/browser/158f29d5-ef12-4a73-84af-783eb465b2be
1642352667517	Marionette	INFO	Listening on port 40879
1642352667575	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642352678219	Marionette	INFO	Stopped listening on port 40879

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv

1642354598751	geckodriver	INFO	Listening on 127.0.0.1:55719
1642354598769	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "59743" "-no-remote" "-profile" "/tmp/rust_mozprofilenQeIbz"
*** You are running in headless mode.
1642354599436	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilenQeIbz/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:59743/devtools/browser/c2508ab8-12ee-4333-9237-449b809dff99
1642354601533	Marionette	INFO	Listening on port 35731
1642354601597	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642354619731	Marionette	INFO	Stopped listening on port 35731

###!!! [Child][MessageChannel] Error: (msgtype=0x6D0025,name=PNecko::Msg_RemoveRequestContext) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642354697889	geckodriver	INFO	Listening on 127.0.0.1:52139
1642354697908	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39635" "-no-remote" "-profile" "/tmp/rust_mozprofileED0Mem"
*** You are running in headless mode.
1642354698642	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileED0Mem/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39635/devtools/browser/14478c69-d94b-484b-8456-195a5c86f27a
1642354700753	Marionette	INFO	Listening on port 37981
1642354700887	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642354713191	Marionette	INFO	Stopped listening on port 37981
1642354899259	geckodriver	INFO	Listening on 127.0.0.1:54607
1642354899283	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "52127" "-no-remote" "-profile" "/tmp/rust_mozprofileYRdesg"
*** You are running in headless mode.
1642354900112	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileYRdesg/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:52127/devtools/browser/f5c0caf7-0a75-4d0f-bbd2-2bb4795f6f57
1642354901864	Marionette	INFO	Listening on port 42025
1642354902192	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642354916656	Marionette	INFO	Stopped listening on port 42025

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642355301640	geckodriver	INFO	Listening on 127.0.0.1:40533
1642355301659	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "56393" "-no-remote" "-profile" "/tmp/rust_mozprofileL1wyb9"
*** You are running in headless mode.
1642355302684	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileL1wyb9/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:56393/devtools/browser/9463c7d7-eb3b-4953-8d9c-5933ac1246da
1642355304977	Marionette	INFO	Listening on port 35653
1642355305103	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642355320008	Marionette	INFO	Stopped listening on port 35653

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642356632702	geckodriver	INFO	Listening on 127.0.0.1:60667
1642356633211	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "34471" "-no-remote" "-profile" "/tmp/rust_mozprofile4jMf1x"
*** You are running in headless mode.
1642356633994	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile4jMf1x/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:34471/devtools/browser/aab8b86c-b369-4fb6-b54b-086569455460
1642356636044	Marionette	INFO	Listening on port 36741
1642356636143	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642356653690	Marionette	INFO	Stopped listening on port 36741

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv

1642419173548	geckodriver	INFO	Listening on 127.0.0.1:44235
1642419174052	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "60421" "-no-remote" "-profile" "/tmp/rust_mozprofiledpxg06"
*** You are running in headless mode.
1642419175061	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiledpxg06/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:60421/devtools/browser/9756c8e1-b868-4fe5-a7be-c2ba81b6cd35
1642419178009	Marionette	INFO	Listening on port 45211
1642419178138	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642419197287	Marionette	INFO	Stopped listening on port 45211
1642419449922	geckodriver	INFO	Listening on 127.0.0.1:51155
1642419450439	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39925" "-no-remote" "-profile" "/tmp/rust_mozprofileDgpj54"
*** You are running in headless mode.
1642419451423	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileDgpj54/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39925/devtools/browser/9cf458ab-400e-43cb-9437-06097172804d
1642419453806	Marionette	INFO	Listening on port 37359
1642419453874	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642419470270	Marionette	INFO	Stopped listening on port 37359
1642419578182	geckodriver	INFO	Listening on 127.0.0.1:39751
1642419578202	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "38755" "-no-remote" "-profile" "/tmp/rust_mozprofileqdKqL1"
*** You are running in headless mode.
1642419579062	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileqdKqL1/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:38755/devtools/browser/bb24224f-b770-4bd7-ac1f-e1cc5ef19c69
1642419581270	Marionette	INFO	Listening on port 42633
1642419581390	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642419600477	Marionette	INFO	Stopped listening on port 42633
1642498356999	geckodriver	INFO	Listening on 127.0.0.1:56727
1642498357502	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "50733" "-no-remote" "-profile" "/tmp/rust_mozprofiley85Cdo"
*** You are running in headless mode.
1642498358404	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiley85Cdo/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:50733/devtools/browser/895ea282-0ce0-4563-8f5d-7f5725f0963e
1642498360364	Marionette	INFO	Listening on port 42081
1642498360617	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642498375429	Marionette	INFO	Stopped listening on port 42081

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv

1642498523988	geckodriver	INFO	Listening on 127.0.0.1:36337
1642498524505	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "47825" "-no-remote" "-profile" "/tmp/rust_mozprofileqtV17u"
*** You are running in headless mode.
1642498525287	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileqtV17u/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:47825/devtools/browser/1d57054f-d483-49d6-a79f-9b6c235bdf95
1642498527170	Marionette	INFO	Listening on port 32861
1642498527266	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642498538144	Marionette	INFO	Stopped listening on port 32861

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642500208799	geckodriver	INFO	Listening on 127.0.0.1:47397
1642500209311	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "38471" "-no-remote" "-profile" "/tmp/rust_mozprofileWt7knF"
*** You are running in headless mode.
1642500210195	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileWt7knF/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:38471/devtools/browser/4a311e28-7b28-41fd-adc7-a1a9d72f4e4e
1642500212803	Marionette	INFO	Listening on port 34577
1642500212857	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642500223323	Marionette	INFO	Stopped listening on port 34577
1642502103036	geckodriver	INFO	Listening on 127.0.0.1:45043
1642502103546	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "40263" "-no-remote" "-profile" "/tmp/rust_mozprofileM7YaNc"
*** You are running in headless mode.
1642502104585	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileM7YaNc/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:40263/devtools/browser/02f7a9b3-574d-4dc7-aa4b-92f11d15982b
1642502107091	Marionette	INFO	Listening on port 41697
1642502107192	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642502124560	Marionette	INFO	Stopped listening on port 41697
1642574826126	geckodriver	INFO	Listening on 127.0.0.1:55697
1642574826664	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "34303" "-no-remote" "-profile" "/tmp/rust_mozprofileRWUpkY"
*** You are running in headless mode.
1642574828120	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileRWUpkY/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:34303/devtools/browser/89b88427-bc51-4546-8962-d4b7b20a1824
1642574830499	Marionette	INFO	Listening on port 43601
1642574830602	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642574850136	Marionette	INFO	Stopped listening on port 43601
1642576645877	geckodriver	INFO	Listening on 127.0.0.1:40753
1642576646383	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "55851" "-no-remote" "-profile" "/tmp/rust_mozprofileQw76YI"
*** You are running in headless mode.
1642576647814	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileQw76YI/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:55851/devtools/browser/bbcadec5-d54f-48bc-b591-e0c4fa34af3d
1642576650045	Marionette	INFO	Listening on port 44183
1642576650122	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642576664045	Marionette	INFO	Stopped listening on port 44183
1642577411500	geckodriver	INFO	Listening on 127.0.0.1:37717
1642577412021	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "50727" "-no-remote" "-profile" "/tmp/rust_mozprofile6rTwKx"
*** You are running in headless mode.
1642577412876	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile6rTwKx/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:50727/devtools/browser/e9546473-e860-4808-9f46-7bc4dfafea9c
1642577414744	Marionette	INFO	Listening on port 44015
1642577414869	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642577429763	Marionette	INFO	Stopped listening on port 44015
1642577693837	geckodriver	INFO	Listening on 127.0.0.1:48391
1642577693846	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "51857" "-no-remote" "-profile" "/tmp/rust_mozprofileTJCmp6"
*** You are running in headless mode.
1642577694969	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileTJCmp6/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:51857/devtools/browser/f126b888-0657-46b6-a0d6-dae442c2c07d
1642577697004	Marionette	INFO	Listening on port 46189
1642577697078	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642577715753	Marionette	INFO	Stopped listening on port 46189
1642577783006	geckodriver	INFO	Listening on 127.0.0.1:42683
1642577783524	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58063" "-no-remote" "-profile" "/tmp/rust_mozprofile8e6RxH"
*** You are running in headless mode.
1642577784332	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile8e6RxH/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58063/devtools/browser/72b52549-809e-4ca0-af64-948f58639c02
1642577786383	Marionette	INFO	Listening on port 46349
1642577786453	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642577806131	Marionette	INFO	Stopped listening on port 46349
1642577897823	geckodriver	INFO	Listening on 127.0.0.1:34577
1642577897842	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "45607" "-no-remote" "-profile" "/tmp/rust_mozprofileKcctkb"
*** You are running in headless mode.
1642577898560	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileKcctkb/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:45607/devtools/browser/5a596e2a-68bd-49ea-b58f-a248d77a6471
1642577900554	Marionette	INFO	Listening on port 33551
1642577900669	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642577917779	Marionette	INFO	Stopped listening on port 33551
1642578093333	geckodriver	INFO	Listening on 127.0.0.1:42751
1642578093357	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "38293" "-no-remote" "-profile" "/tmp/rust_mozprofileY06kHZ"
*** You are running in headless mode.
1642578094118	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileY06kHZ/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:38293/devtools/browser/a0640832-f1fe-4df6-8b07-7825df069838
1642578095803	Marionette	INFO	Listening on port 39095
1642578096131	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642578108953	Marionette	INFO	Stopped listening on port 39095
1642578197237	geckodriver	INFO	Listening on 127.0.0.1:50169
1642578197755	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "32957" "-no-remote" "-profile" "/tmp/rust_mozprofileuEOHMb"
*** You are running in headless mode.
1642578198440	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileuEOHMb/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:32957/devtools/browser/4b02f5b6-8a12-4e9b-81e4-319424ffc86e
1642578200219	Marionette	INFO	Listening on port 37851
1642578200287	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642578215995	Marionette	INFO	Stopped listening on port 37851

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv

1642578241854	geckodriver	INFO	Listening on 127.0.0.1:54159
1642578241859	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "57635" "-no-remote" "-profile" "/tmp/rust_mozprofileYIHedK"
*** You are running in headless mode.
1642578242562	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileYIHedK/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:57635/devtools/browser/3aa0a85e-0008-43ca-929c-4e42b3e14d10
1642578244488	Marionette	INFO	Listening on port 32999
1642578244589	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642578259416	Marionette	INFO	Stopped listening on port 32999

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642578370402	geckodriver	INFO	Listening on 127.0.0.1:33237
1642578370431	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "53235" "-no-remote" "-profile" "/tmp/rust_mozprofileFUr5nd"
*** You are running in headless mode.
1642578371367	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileFUr5nd/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53235/devtools/browser/d3a5fe8c-38bf-4816-8322-25b5bc6e0ad9
1642578373130	Marionette	INFO	Listening on port 34751
1642578373400	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642578388607	Marionette	INFO	Stopped listening on port 34751
1642671839236	geckodriver	INFO	Listening on 127.0.0.1:40623
1642671839772	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42393" "-no-remote" "-profile" "/tmp/rust_mozprofile7qHTzx"
*** You are running in headless mode.
1642671841622	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile7qHTzx/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42393/devtools/browser/fbdc8239-114e-4a7d-ad45-b495af15ded5
1642671844502	Marionette	INFO	Listening on port 43569
1642671844704	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642671857678	Marionette	INFO	Stopped listening on port 43569
1642672270491	geckodriver	INFO	Listening on 127.0.0.1:39033
1642672270999	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "40445" "-no-remote" "-profile" "/tmp/rust_mozprofilen4evz3"
*** You are running in headless mode.
1642672272303	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilen4evz3/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:40445/devtools/browser/8baa74b7-74ca-4c6f-a851-955f3474543e
1642672274924	Marionette	INFO	Listening on port 38519
1642672275100	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642672288082	Marionette	INFO	Stopped listening on port 38519

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642672478618	geckodriver	INFO	Listening on 127.0.0.1:36517
1642672479145	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "53841" "-no-remote" "-profile" "/tmp/rust_mozprofileaKvhmy"
*** You are running in headless mode.
1642672480058	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileaKvhmy/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53841/devtools/browser/1be2daa3-6a01-4448-92bf-dd0c6134f889
1642672482578	Marionette	INFO	Listening on port 34205
1642672482687	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642672494600	Marionette	INFO	Stopped listening on port 34205

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642672662303	geckodriver	INFO	Listening on 127.0.0.1:40591
1642672662817	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "54131" "-no-remote" "-profile" "/tmp/rust_mozprofileHZAe4R"
*** You are running in headless mode.
1642672664379	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileHZAe4R/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:54131/devtools/browser/87e5388f-1b54-4e37-80b9-1dcc0f45a25d
1642672666405	Marionette	INFO	Listening on port 39413
1642672666451	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642672675948	Marionette	INFO	Stopped listening on port 39413
1642673736156	geckodriver	INFO	Listening on 127.0.0.1:48367
1642673736663	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "60257" "-no-remote" "-profile" "/tmp/rust_mozprofilevxdMFD"
*** You are running in headless mode.
1642673737852	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilevxdMFD/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:60257/devtools/browser/f143d455-51d3-4d16-a86d-ff1a81fdd93a
1642673740514	Marionette	INFO	Listening on port 36197
1642673740646	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642673754895	Marionette	INFO	Stopped listening on port 36197
1642674071520	geckodriver	INFO	Listening on 127.0.0.1:50043
1642674072038	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "45773" "-no-remote" "-profile" "/tmp/rust_mozprofile7dCSDx"
*** You are running in headless mode.
1642674072848	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile7dCSDx/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:45773/devtools/browser/8bc9fe4a-cf6f-4229-ba14-3f8dbeaafef6
1642674074797	Marionette	INFO	Listening on port 43351
1642674074874	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642674087135	Marionette	INFO	Stopped listening on port 43351

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642674288878	geckodriver	INFO	Listening on 127.0.0.1:57615
1642674288900	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "32961" "-no-remote" "-profile" "/tmp/rust_mozprofileZkBnFM"
*** You are running in headless mode.
1642674289725	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileZkBnFM/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:32961/devtools/browser/b4d29009-9338-4eb9-84a0-5e8b8415ef10
1642674291829	Marionette	INFO	Listening on port 46465
1642674291929	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642674307826	Marionette	INFO	Stopped listening on port 46465

###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642762377542	geckodriver	INFO	Listening on 127.0.0.1:51997
1642762378048	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42855" "-no-remote" "-profile" "/tmp/rust_mozprofileH5rqDF"
*** You are running in headless mode.
1642762379499	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileH5rqDF/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42855/devtools/browser/854fae1e-5876-44f1-a35e-2001752b0792
1642762381755	Marionette	INFO	Listening on port 36007
1642762381791	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642762395374	Marionette	INFO	Stopped listening on port 36007

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642762750178	geckodriver	INFO	Listening on 127.0.0.1:35935
1642762750684	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "41677" "-no-remote" "-profile" "/tmp/rust_mozprofileen9AW0"
*** You are running in headless mode.
1642762751472	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileen9AW0/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:41677/devtools/browser/57b1dff7-d35c-42ef-92c8-b1ef2489aaab
1642762754162	Marionette	INFO	Listening on port 39275
1642762754345	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642762767418	Marionette	INFO	Stopped listening on port 39275

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642769985254	geckodriver	INFO	Listening on 127.0.0.1:55253
1642769985770	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "35569" "-no-remote" "-profile" "/tmp/rust_mozprofileu00vCn"
*** You are running in headless mode.
1642769987527	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileu00vCn/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:35569/devtools/browser/6f86c5cb-2101-41d8-9d36-18a2420a7f85
1642769989879	Marionette	INFO	Listening on port 35485
1642769989915	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642770008678	Marionette	INFO	Stopped listening on port 35485

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642770028178	geckodriver	INFO	Listening on 127.0.0.1:55193
1642770028698	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "37017" "-no-remote" "-profile" "/tmp/rust_mozprofilejdGqk0"
*** You are running in headless mode.
1642770029605	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilejdGqk0/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:37017/devtools/browser/c8222ae4-ca3d-4d78-a509-e0779809c4a5
1642770031576	Marionette	INFO	Listening on port 43291
1642770031644	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642770048420	Marionette	INFO	Stopped listening on port 43291

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642783065006	geckodriver	INFO	Listening on 127.0.0.1:33267
1642783065516	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42391" "-no-remote" "-profile" "/tmp/rust_mozprofilestoePN"
*** You are running in headless mode.
1642783067207	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilestoePN/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42391/devtools/browser/17ad5c76-2855-411d-b616-f4dfc61e1cc6
1642783069276	Marionette	INFO	Listening on port 45063
1642783069365	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 772))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1642783094770	Marionette	INFO	Stopped listening on port 45063
1642792721456	geckodriver	INFO	Listening on 127.0.0.1:44199
1642792721965	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "57257" "-no-remote" "-profile" "/tmp/rust_mozprofileYCjaGV"
*** You are running in headless mode.
1642792723367	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileYCjaGV/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:57257/devtools/browser/5f64f315-27e4-4a8b-b12d-f5b4cb57f985
1642792725485	Marionette	INFO	Listening on port 33257
1642792725643	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642792739620	Marionette	INFO	Stopped listening on port 33257
1642792894930	geckodriver	INFO	Listening on 127.0.0.1:34623
1642792895457	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "42899" "-no-remote" "-profile" "/tmp/rust_mozprofileoYJ1bl"
*** You are running in headless mode.
1642792896242	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileoYJ1bl/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:42899/devtools/browser/e39b0a14-66b9-4bd0-999b-f657022a1860
1642792898197	Marionette	INFO	Listening on port 33539
1642792898286	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642792916221	Marionette	INFO	Stopped listening on port 33539
1642792929022	geckodriver	INFO	Listening on 127.0.0.1:41705
1642792929541	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "53199" "-no-remote" "-profile" "/tmp/rust_mozprofilefkPTfz"
*** You are running in headless mode.
1642792930612	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilefkPTfz/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53199/devtools/browser/ef5beef7-ca9e-411d-bd78-1e39991d924a
1642792932847	Marionette	INFO	Listening on port 44751
1642792932873	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642792946590	Marionette	INFO	Stopped listening on port 44751

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642793291513	geckodriver	INFO	Listening on 127.0.0.1:50017
1642793291530	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "60447" "-no-remote" "-profile" "/tmp/rust_mozprofilelOxxlx"
*** You are running in headless mode.
1642793292387	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilelOxxlx/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:60447/devtools/browser/6ee17d22-b46f-424f-950c-ed003fdca09e
1642793294336	Marionette	INFO	Listening on port 44439
1642793294359	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642793309730	Marionette	INFO	Stopped listening on port 44439
1642816422842	geckodriver	INFO	Listening on 127.0.0.1:39953
1642816423375	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "47861" "-no-remote" "-profile" "/tmp/rust_mozprofileNkzw6g"
*** You are running in headless mode.
1642816425451	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileNkzw6g/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:47861/devtools/browser/047c2949-f1f0-42b6-a1d1-a828dbe7478f
1642816427661	Marionette	INFO	Listening on port 34501
1642816427984	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642816446185	Marionette	INFO	Stopped listening on port 34501
1642840483050	geckodriver	INFO	Listening on 127.0.0.1:43623
1642840483556	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "32921" "-no-remote" "-profile" "/tmp/rust_mozprofilevBuUCT"
*** You are running in headless mode.
1642840484912	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilevBuUCT/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:32921/devtools/browser/aeb8d0cc-b469-4989-b3b7-98048e32a74d
1642840487458	Marionette	INFO	Listening on port 38619
1642840487497	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642840502883	Marionette	INFO	Stopped listening on port 38619

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642840528891	geckodriver	INFO	Listening on 127.0.0.1:55691
1642840529414	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "59809" "-no-remote" "-profile" "/tmp/rust_mozprofileOlba61"
*** You are running in headless mode.
1642840530389	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileOlba61/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:59809/devtools/browser/71b9654a-157e-42df-a94e-05f800638198
1642840532663	Marionette	INFO	Listening on port 43877
1642840532744	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642840545926	Marionette	INFO	Stopped listening on port 43877
1642846445899	geckodriver	INFO	Listening on 127.0.0.1:56089
1642846446403	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "52777" "-no-remote" "-profile" "/tmp/rust_mozprofilekUL34G"
*** You are running in headless mode.
1642846447780	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofilekUL34G/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:52777/devtools/browser/f500bbdc-b022-4ce9-bbbb-a6d1d1dbb6c5
1642846450186	Marionette	INFO	Listening on port 33605
1642846450264	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642846463069	Marionette	INFO	Stopped listening on port 33605

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642870869145	geckodriver	INFO	Listening on 127.0.0.1:49491
1642870869655	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "50165" "-no-remote" "-profile" "/tmp/rust_mozprofileGp67Hz"
*** You are running in headless mode.
1642870870696	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileGp67Hz/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:50165/devtools/browser/663e7441-3ded-4812-a549-51205c38a0e5
1642870872983	Marionette	INFO	Listening on port 32769
1642870873110	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642870890122	Marionette	INFO	Stopped listening on port 32769
1642871135934	geckodriver	INFO	Listening on 127.0.0.1:48503
1642871135957	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "56789" "-no-remote" "-profile" "/tmp/rust_mozprofile1ojCLK"
*** You are running in headless mode.
1642871136750	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile1ojCLK/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:56789/devtools/browser/61ccabc4-5fa2-4b2e-9545-38e5a5786a2b
1642871138866	Marionette	INFO	Listening on port 37047
1642871139155	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642871154766	Marionette	INFO	Stopped listening on port 37047

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642942329758	geckodriver	INFO	Listening on 127.0.0.1:54295
1642942330262	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "47941" "-no-remote" "-profile" "/tmp/rust_mozprofileT2Socr"
*** You are running in headless mode.
1642942331411	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileT2Socr/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:47941/devtools/browser/7603bece-530b-4837-8e21-296cbf1b3ce5
1642942333520	Marionette	INFO	Listening on port 34607
1642942333596	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642942347237	Marionette	INFO	Stopped listening on port 34607

###!!! [Parent][MessageChannel] Error: (msgtype=0x3A0078,name=PContent::Msg_DestroyBrowsingContextGroup) Closed channel: cannot send/recv


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642942725651	geckodriver	INFO	Listening on 127.0.0.1:49239
1642942726172	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "34545" "-no-remote" "-profile" "/tmp/rust_mozprofiletwWpiD"
*** You are running in headless mode.
1642942727158	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofiletwWpiD/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:34545/devtools/browser/9d83c642-4965-4255-9cb0-2e573be8c160
1642942728915	Marionette	INFO	Listening on port 45409
1642942729060	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642942743722	Marionette	INFO	Stopped listening on port 45409
1642943214275	geckodriver	INFO	Listening on 127.0.0.1:38843
1642943214294	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "43317" "-no-remote" "-profile" "/tmp/rust_mozprofileqk4wiU"
*** You are running in headless mode.
1642943215490	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileqk4wiU/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:43317/devtools/browser/0bd56f3e-e135-40be-aae8-712ebfba8f74
1642943217701	Marionette	INFO	Listening on port 44491
1642943217846	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642943231700	Marionette	INFO	Stopped listening on port 44491

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642943812533	geckodriver	INFO	Listening on 127.0.0.1:44101
1642943813051	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39649" "-no-remote" "-profile" "/tmp/rust_mozprofile01TuxQ"
*** You are running in headless mode.
1642943813875	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile01TuxQ/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39649/devtools/browser/53d476f0-9043-4220-985b-bff269a9104d
1642943815832	Marionette	INFO	Listening on port 38345
1642943816048	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642943832303	Marionette	INFO	Stopped listening on port 38345

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1642944134361	geckodriver	INFO	Listening on 127.0.0.1:37409
1642944134872	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "36507" "-no-remote" "-profile" "/tmp/rust_mozprofileRg5kx4"
*** You are running in headless mode.
1642944135745	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileRg5kx4/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:36507/devtools/browser/f4302ac2-0a20-4825-9afa-ce6cc272164a
1642944138479	Marionette	INFO	Listening on port 37031
1642944138552	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1642944155177	Marionette	INFO	Stopped listening on port 37031
1643014825493	geckodriver	INFO	Listening on 127.0.0.1:57033
1643014826001	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58119" "-no-remote" "-profile" "/tmp/rust_mozprofileQ9KGge"
*** You are running in headless mode.
1643014828262	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileQ9KGge/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58119/devtools/browser/848d57c8-ef3e-4044-85c6-8b665efbcd58
1643014830861	Marionette	INFO	Listening on port 38407
1643014830939	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1643014878962	Marionette	INFO	Stopped listening on port 38407
1643018867377	geckodriver	INFO	Listening on 127.0.0.1:35355
1643018867885	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "44635" "-no-remote" "-profile" "/tmp/rust_mozprofileoQKYpb"
*** You are running in headless mode.
1643018868847	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileoQKYpb/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:44635/devtools/browser/c4d072cf-cf7c-41a5-8dab-edbe633e9d98
1643018870685	Marionette	INFO	Listening on port 45285
1643018870729	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 772))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1643019003278	geckodriver	INFO	Listening on 127.0.0.1:56101
1643019003287	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "58015" "-no-remote" "-profile" "/tmp/rust_mozprofileYWalWL"
*** You are running in headless mode.
1643019004093	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileYWalWL/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:58015/devtools/browser/f29c8ff1-8243-46a0-8609-1b54a3acfbdb
1643019005871	Marionette	INFO	Listening on port 41433
1643019005915	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 772))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1643019025224	Marionette	INFO	Stopped listening on port 45285
1643019110983	geckodriver	INFO	Listening on 127.0.0.1:49831
1643019111495	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "39477" "-no-remote" "-profile" "/tmp/rust_mozprofile9k9SDc"
*** You are running in headless mode.
1643019112363	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofile9k9SDc/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:39477/devtools/browser/740c2993-30b4-41c5-b30e-62ae21ef02d0
1643019115171	Marionette	INFO	Listening on port 38247
1643019115231	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
1643019116793	Marionette	INFO	Stopped listening on port 41433

###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0004,name=PHttpChannel::Msg_Cancel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][MessageChannel] Error: (msgtype=0x5C0008,name=PHttpChannel::Msg_DeletingChannel) Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 772))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1643019171208	geckodriver	INFO	Listening on 127.0.0.1:44737
1643019171220	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "53175" "-no-remote" "-profile" "/tmp/rust_mozprofileSrtIDB"
*** You are running in headless mode.
1643019171927	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileSrtIDB/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:53175/devtools/browser/640b672a-6826-4632-a585-9a90a4fe4eeb
1643019173628	Marionette	INFO	Listening on port 37501
1643019173651	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 772))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1643019225114	Marionette	INFO	Stopped listening on port 38247

###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Parent][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1643019242646	geckodriver	INFO	Listening on 127.0.0.1:47107
1643019242667	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "54525" "-no-remote" "-profile" "/tmp/rust_mozprofileVRUJcc"
*** You are running in headless mode.
1643019243495	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileVRUJcc/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:54525/devtools/browser/db9580f0-c226-4524-abde-3b3c1545ed96
1643019245412	Marionette	INFO	Listening on port 38343
1643019245496	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 772))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1643019306795	Marionette	INFO	Stopped listening on port 37501
1643019360779	Marionette	INFO	Stopped listening on port 38343

###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost


###!!! [Child][RunMessage] Error: Channel closing: too late to send/recv, messages will be lost

1643019464542	geckodriver	INFO	Listening on 127.0.0.1:57101
1643019464564	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "46905" "-no-remote" "-profile" "/tmp/rust_mozprofileVcjZPt"
*** You are running in headless mode.
1643019465363	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileVcjZPt/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:46905/devtools/browser/d6c278b1-8c0e-48ac-adb2-44cc11ca0a3c
1643019467255	Marionette	INFO	Listening on port 41555
1643019467298	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 772))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1643019547625	Marionette	INFO	Stopped listening on port 41555
1643019720816	geckodriver	INFO	Listening on 127.0.0.1:55945
1643019721335	mozrunner::runner	INFO	Running command: "/usr/bin/firefox" "--marionette" "-headless" "--remote-debugging-port" "56021" "-no-remote" "-profile" "/tmp/rust_mozprofileTYQucy"
*** You are running in headless mode.
1643019722006	Marionette	INFO	Marionette enabled
[GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
console.warn: SearchSettings: "get: No settings file exists, new profile?" (new NotFoundError("Could not open the file at /tmp/rust_mozprofileTYQucy/search.json.mozlz4", (void 0)))
DevTools listening on ws://localhost:56021/devtools/browser/7b8f96de-6844-45ed-b007-6513a8dd2751
1643019723558	Marionette	INFO	Listening on port 34903
1643019723716	RemoteAgent	WARN	TLS certificate errors will be ignored for this session
console.error: Region.jsm: "Error fetching region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 772))
console.error: Region.jsm: "Failed to fetch region" (new Error("TIMEOUT", "resource://gre/modules/Region.jsm", 419))
1643019833559	Marionette	INFO	Stopped listening on port 34903
