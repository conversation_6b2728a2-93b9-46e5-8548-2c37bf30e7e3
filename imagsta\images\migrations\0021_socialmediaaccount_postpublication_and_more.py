# Generated by Django 4.2.7 on 2025-07-13 09:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('images', '0020_rename_last_updated_useranalytics_last_calculated_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SocialMediaAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('instagram', 'Instagram'), ('twitter', 'Twitter'), ('linkedin', 'LinkedIn'), ('facebook', 'Facebook'), ('tiktok', 'TikTok'), ('youtube', 'YouTube')], max_length=20)),
                ('username', models.CharField(max_length=100)),
                ('display_name', models.CharField(blank=True, max_length=100)),
                ('profile_picture_url', models.URLField(blank=True)),
                ('access_token', models.TextField(blank=True)),
                ('refresh_token', models.TextField(blank=True)),
                ('token_expires_at', models.DateTimeField(blank=True, null=True)),
                ('platform_user_id', models.CharField(blank=True, max_length=100)),
                ('platform_data', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(choices=[('connected', 'Connected'), ('disconnected', 'Disconnected'), ('expired', 'Token Expired'), ('error', 'Error')], default='connected', max_length=20)),
                ('follower_count', models.PositiveIntegerField(default=0)),
                ('following_count', models.PositiveIntegerField(default=0)),
                ('posts_count', models.PositiveIntegerField(default=0)),
                ('can_post', models.BooleanField(default=True)),
                ('can_read', models.BooleanField(default=True)),
                ('connected_at', models.DateTimeField(auto_now_add=True)),
                ('last_sync', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='social_accounts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-connected_at'],
            },
        ),
        migrations.CreateModel(
            name='PostPublication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform_post_id', models.CharField(blank=True, max_length=100)),
                ('platform_url', models.URLField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('published', 'Published'), ('failed', 'Failed'), ('deleted', 'Deleted')], default='pending', max_length=20)),
                ('likes_count', models.PositiveIntegerField(default=0)),
                ('comments_count', models.PositiveIntegerField(default=0)),
                ('shares_count', models.PositiveIntegerField(default=0)),
                ('views_count', models.PositiveIntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('max_retries', models.PositiveIntegerField(default=3)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('last_sync', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='publications', to='images.socialmediaaccount')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='publications', to='images.post')),
            ],
            options={
                'ordering': ['-published_at', '-created'],
            },
        ),
        migrations.AddIndex(
            model_name='socialmediaaccount',
            index=models.Index(fields=['user', 'platform'], name='images_soci_user_id_0b2d32_idx'),
        ),
        migrations.AddIndex(
            model_name='socialmediaaccount',
            index=models.Index(fields=['status', 'platform'], name='images_soci_status_fba5b8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='socialmediaaccount',
            unique_together={('user', 'platform', 'platform_user_id')},
        ),
        migrations.AddIndex(
            model_name='postpublication',
            index=models.Index(fields=['account', 'status'], name='images_post_account_31bb78_idx'),
        ),
        migrations.AddIndex(
            model_name='postpublication',
            index=models.Index(fields=['post', 'status'], name='images_post_post_id_22a0b9_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='postpublication',
            unique_together={('post', 'account')},
        ),
    ]
