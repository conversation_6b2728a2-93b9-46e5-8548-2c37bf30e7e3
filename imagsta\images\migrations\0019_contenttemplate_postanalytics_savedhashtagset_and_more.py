# Generated by Django 4.2.7 on 2025-07-12 19:13

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('images', '0018_result_dis_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('template_type', models.CharField(choices=[('caption', 'Caption Template'), ('hashtag_set', 'Hashtag Set'), ('post_series', 'Post Series')], max_length=20)),
                ('content', models.TextField()),
                ('industry', models.CharField(blank=True, max_length=50)),
                ('tone', models.CharField(blank=True, max_length=20)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('is_public', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-usage_count', '-created'],
            },
        ),
        migrations.CreateModel(
            name='PostAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('likes_count', models.PositiveIntegerField(default=0)),
                ('comments_count', models.PositiveIntegerField(default=0)),
                ('shares_count', models.PositiveIntegerField(default=0)),
                ('reach', models.PositiveIntegerField(default=0)),
                ('impressions', models.PositiveIntegerField(default=0)),
                ('engagement_rate', models.FloatField(default=0.0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Post Analytics',
            },
        ),
        migrations.CreateModel(
            name='SavedHashtagSet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('hashtags', models.TextField(help_text='Comma-separated hashtags')),
                ('industry', models.CharField(blank=True, max_length=50)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-usage_count', '-created'],
            },
        ),
        migrations.CreateModel(
            name='ScheduledPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_for', models.DateTimeField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('posted', 'Posted'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('target_platforms', models.JSONField(default=list, help_text='List of platforms to post to')),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('max_retries', models.PositiveIntegerField(default=3)),
                ('last_attempt', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['scheduled_for'],
            },
        ),
        migrations.CreateModel(
            name='UserAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_posts', models.PositiveIntegerField(default=0)),
                ('total_likes', models.PositiveIntegerField(default=0)),
                ('total_comments', models.PositiveIntegerField(default=0)),
                ('total_followers', models.PositiveIntegerField(default=0)),
                ('total_following', models.PositiveIntegerField(default=0)),
                ('avg_engagement_rate', models.FloatField(default=0.0)),
                ('best_posting_hour', models.PositiveIntegerField(default=12)),
                ('best_posting_day', models.PositiveIntegerField(default=1)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'User Analytics',
            },
        ),
        migrations.AlterModelOptions(
            name='search',
            options={'ordering': ['-updated']},
        ),
        migrations.AddField(
            model_name='search',
            name='search_count',
            field=models.PositiveIntegerField(default=1, help_text='Number of times this query was searched'),
        ),
        migrations.AlterField(
            model_name='search',
            name='query',
            field=models.CharField(help_text='Search query for images', max_length=128, unique=True, validators=[django.core.validators.MinLengthValidator(2)]),
        ),
        migrations.AddIndex(
            model_name='search',
            index=models.Index(fields=['query'], name='images_sear_query_90c054_idx'),
        ),
        migrations.AddIndex(
            model_name='search',
            index=models.Index(fields=['-updated'], name='images_sear_updated_098697_idx'),
        ),
        migrations.AddField(
            model_name='useranalytics',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='scheduledpost',
            name='post',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='schedule', to='images.post'),
        ),
        migrations.AddField(
            model_name='scheduledpost',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scheduled_posts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='savedhashtagset',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hashtag_sets', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='postanalytics',
            name='post',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='images.post'),
        ),
        migrations.AddField(
            model_name='contenttemplate',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='templates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='scheduledpost',
            index=models.Index(fields=['user', 'status', 'scheduled_for'], name='images_sche_user_id_97fcfd_idx'),
        ),
        migrations.AddIndex(
            model_name='scheduledpost',
            index=models.Index(fields=['status', 'scheduled_for'], name='images_sche_status_345ffc_idx'),
        ),
    ]
