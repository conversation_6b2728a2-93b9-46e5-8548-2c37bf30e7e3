{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-file-text"></i> Content Templates
                </h1>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                        <i class="bi bi-plus"></i> Create Template
                    </button>
                    <a href="{% url 'ai-studio' %}" class="btn btn-outline-primary">
                        <i class="bi bi-robot"></i> AI Studio
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="industry" class="form-label">Industry</label>
                            <select class="form-select" id="industry" name="industry">
                                <option value="">All Industries</option>
                                {% for value, label in industries %}
                                <option value="{{ value }}" {% if current_industry == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="type" class="form-label">Template Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                {% for value, label in template_types %}
                                <option value="{{ value }}" {% if current_type == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tone" class="form-label">Tone</label>
                            <select class="form-select" id="tone" name="tone">
                                <option value="">All Tones</option>
                                {% for value, label in tones %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="bi bi-funnel"></i> Filter
                            </button>
                        </div>
                    </form>

                    <!-- Quick Actions -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-outline-success" onclick="exportTemplates()">
                                    <i class="bi bi-download"></i> Export All
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="importTemplates()">
                                    <i class="bi bi-upload"></i> Import
                                </button>
                                <a href="{% url 'templates' %}" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-x"></i> Clear Filters
                                </a>
                            </div>
                        </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Templates Grid -->
    <div class="row">
        {% for template in templates %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100 template-card" data-template-id="{{ template.id }}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ template.name }}</h6>
                    <div class="d-flex gap-2">
                        <span class="badge bg-{{ template.type|yesno:'primary,success,info,warning' }}">
                            {{ template.type|title }}
                        </span>
                        <span class="badge bg-secondary">
                            {{ template.industry|title }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="template-content mb-3">
                        <small class="text-muted">Template:</small>
                        <p class="template-text">{{ template.content|truncatechars:150 }}</p>
                    </div>
                    
                    {% if template.hashtags %}
                    <div class="hashtags mb-3">
                        <small class="text-muted">Hashtags:</small>
                        <div class="hashtag-preview">
                            {% for hashtag in template.hashtags|slice:":5" %}
                            <span class="badge bg-light text-dark me-1">{{ hashtag }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="template-stats mb-3">
                        <small class="text-muted">
                            <i class="bi bi-graph-up"></i> Used {{ template.usage_count }} times
                        </small>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm preview-btn" 
                                data-template-id="{{ template.id }}">
                            <i class="bi bi-eye"></i> Preview
                        </button>
                        <button type="button" class="btn btn-primary btn-sm use-template-btn"
                                data-template-content="{{ template.content }}"
                                data-template-hashtags="{{ template.hashtags }}">
                            <i class="bi bi-arrow-right"></i> Use Template
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-pencil"></i> Edit
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-file-text display-1 text-muted"></i>
                <h4 class="mt-3">No Templates Found</h4>
                <p class="text-muted">Create your first template to get started with consistent content creation.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                    <i class="bi bi-plus"></i> Create Your First Template
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Popular Templates Section -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-light mb-4">
                <i class="bi bi-star"></i> Popular Templates
            </h3>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card bg-gradient-primary text-white">
                        <div class="card-body">
                            <h6>Product Announcement</h6>
                            <p class="small mb-2">Perfect for launching new products with excitement and engagement.</p>
                            <small><i class="bi bi-heart"></i> Used 150+ times</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-gradient-success text-white">
                        <div class="card-body">
                            <h6>Customer Testimonial</h6>
                            <p class="small mb-2">Showcase customer success stories and build trust.</p>
                            <small><i class="bi bi-heart"></i> Used 120+ times</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-gradient-info text-white">
                        <div class="card-body">
                            <h6>Educational Content</h6>
                            <p class="small mb-2">Share knowledge and establish thought leadership.</p>
                            <small><i class="bi bi-heart"></i> Used 95+ times</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Template Modal -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createTemplateForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="templateName" class="form-label">Template Name</label>
                            <input type="text" class="form-control" id="templateName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="templateType" class="form-label">Type</label>
                            <select class="form-select" id="templateType" required>
                                <option value="">Select Type</option>
                                {% for value, label in template_types %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="templateIndustry" class="form-label">Industry</label>
                            <select class="form-select" id="templateIndustry">
                                <option value="">Select Industry</option>
                                {% for value, label in industries %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="templateTone" class="form-label">Tone</label>
                            <select class="form-select" id="templateTone">
                                <option value="">Select Tone</option>
                                <option value="professional">Professional</option>
                                <option value="casual">Casual</option>
                                <option value="humorous">Humorous</option>
                                <option value="inspirational">Inspirational</option>
                                <option value="educational">Educational</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="templateContent" class="form-label">Template Content</label>
                        <textarea class="form-control" id="templateContent" rows="4" 
                                  placeholder="Use {variable_name} for dynamic content. Example: Welcome to {company_name}!" required></textarea>
                        <div class="form-text">
                            Use curly braces {} to create variables that can be filled in later.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="templateHashtags" class="form-label">Default Hashtags</label>
                        <input type="text" class="form-control" id="templateHashtags" 
                               placeholder="#hashtag1 #hashtag2 #hashtag3">
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="makePublic">
                        <label class="form-check-label" for="makePublic">
                            Make this template public (other users can use it)
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveTemplate">
                    <i class="bi bi-save"></i> Save Template
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="usePreviewedTemplate">
                    <i class="bi bi-arrow-right"></i> Use This Template
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preview template functionality
    document.querySelectorAll('.preview-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const templateId = this.dataset.templateId;
            
            fetch(`/templates/preview/${templateId}/`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('Error loading template preview');
                        return;
                    }
                    
                    document.getElementById('previewContent').innerHTML = `
                        <h6>${data.name}</h6>
                        <div class="mb-3">
                            <strong>Template:</strong>
                            <div class="bg-light p-2 rounded mt-1">
                                <code>${data.content}</code>
                            </div>
                        </div>
                        <div class="mb-3">
                            <strong>Preview with sample data:</strong>
                            <div class="border p-3 rounded mt-1">
                                ${data.preview}
                            </div>
                        </div>
                        ${data.variables ? `
                        <div>
                            <strong>Variables:</strong>
                            <div class="mt-1">
                                ${data.variables.map(v => `<span class="badge bg-primary me-1">{${v}}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}
                    `;
                    
                    new bootstrap.Modal(document.getElementById('templatePreviewModal')).show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading template preview');
                });
        });
    });
    
    // Use template functionality
    document.querySelectorAll('.use-template-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const content = this.dataset.templateContent;
            const hashtags = this.dataset.templateHashtags;
            
            // Store in localStorage to use in AI Studio or Multi-Platform
            localStorage.setItem('selectedTemplate', JSON.stringify({
                content: content,
                hashtags: hashtags
            }));
            
            // Redirect to AI Studio
            window.location.href = '/ai-studio/?template=true';
        });
    });
    
    // Save template functionality
    document.getElementById('saveTemplate').addEventListener('click', function() {
        const form = document.getElementById('createTemplateForm');
        const formData = new FormData(form);

        // Show loading state
        this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Saving...';
        this.disabled = true;

        // Submit form
        fetch('{% url "create-template" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Template created successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createTemplateModal')).hide();
                form.reset();
                // Reload page to show new template
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.error || 'Failed to create template', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to create template', 'error');
        })
        .finally(() => {
            this.innerHTML = '<i class="bi bi-check"></i> Save Template';
            this.disabled = false;
        });
    });
});

// Enhanced template management functions
function exportTemplates() {
    showNotification('Exporting templates...', 'info');

    fetch('{% url "export-templates" %}', {
        method: 'GET',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'imagsta_templates.json';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        showNotification('Templates exported successfully!', 'success');
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to export templates', 'error');
    });
}

function importTemplates() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);

        showNotification('Importing templates...', 'info');

        fetch('{% url "import-templates" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Imported ${data.count} templates successfully!`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.error || 'Failed to import templates', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to import templates', 'error');
        });
    };
    input.click();
}

function duplicateTemplate(templateId) {
    fetch(`{% url "duplicate-template" 0 %}`.replace('0', templateId), {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Template duplicated successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(data.error || 'Failed to duplicate template', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to duplicate template', 'error');
    });
}

function deleteTemplate(templateId, templateName) {
    if (!confirm(`Are you sure you want to delete "${templateName}"?`)) {
        return;
    }

    fetch(`{% url "delete-template" 0 %}`.replace('0', templateId), {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Template deleted successfully!', 'success');
            // Remove the template card from DOM
            document.querySelector(`[data-template-id="${templateId}"]`).closest('.col-md-6').remove();
        } else {
            showNotification(data.error || 'Failed to delete template', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to delete template', 'error');
    });
}

function showNotification(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type === 'error' ? 'danger' : type === 'info' ? 'info' : 'success'} alert-dismissible fade show position-fixed`;
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.innerHTML = `
        <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'info' ? 'info-circle' : 'check-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 5000);
}
</script>

<style>
.template-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.template-text {
    font-size: 0.9em;
    line-height: 1.4;
    color: #666;
}

.hashtag-preview .badge {
    font-size: 0.75em;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #117a8b);
}

.card-footer .btn-group .btn {
    border-radius: 0;
}

.card-footer .btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.card-footer .btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>
{% endblock %}
