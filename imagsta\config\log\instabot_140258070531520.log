2022-01-13 10:01:44,787 - instabot version: 0.117.0 (bot) - INFO - Instabot version: 0.117.0 Started
2022-01-13 10:01:44,787 - instabot version: 0.117.0 (bot) - DEBUG - Bot imported from /home/<USER>/programming/imagsta/env/lib/python3.8/site-packages/instabot/bot/bot.py
2022-01-13 10:01:44,790 - instabot version: 0.117.0 (api_login) - DEBUG - Loading uuids
2022-01-13 10:01:44,790 - instabot version: 0.117.0 (api_login) - INFO - Recovery from /home/<USER>/programming/imagsta/config/news_wave4_uuid_and_cookie.json: COOKIE False - UUIDs True - TIMING, DEVICE and ...
- user-agent=Instagram 117.0.0.28.123 Android (28/9.0; 420dpi; 1080x1920; OnePlus; ONEPLUS A3003; OnePlus3; qcom; en_US; *********)
- phone_id=e6390504-cf4c-4060-a868-67337159fc1f
- uuid=b827ef42-acb9-41e1-bc48-cc471e56185b
- client_session_id=4b3980b9-e372-4453-8282-2c96f6e1f5b7
- device_id=android-8e7d6c708ffbfc31
2022-01-13 10:01:44,791 - instabot version: 0.117.0 (api_login) - INFO - Not yet logged in starting: PRE-LOGIN FLOW!
2022-01-13 10:01:45,149 - instabot version: 0.117.0 (api) - DEBUG - POST to endpoint: accounts/get_prefill_candidates/ returned response: <Response [429]>
2022-01-13 10:01:45,149 - instabot version: 0.117.0 (api) - DEBUG - Responsecode indicates error; response content: b'{"message":"Please wait a few minutes before you try again.","status":"fail"}'
2022-01-13 10:01:45,150 - instabot version: 0.117.0 (api) - ERROR - Request returns 429 error!
2022-01-13 10:01:45,150 - instabot version: 0.117.0 (api) - WARNING - That means 'too many requests'. I'll go to sleep for 5 minutes.
