# Generated by Django 4.2.7 on 2025-07-13 09:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('images', '0021_socialmediaaccount_postpublication_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('technology', 'Technology'), ('business', 'Business'), ('entertainment', 'Entertainment'), ('sports', 'Sports'), ('politics', 'Politics'), ('health', 'Health'), ('science', 'Science'), ('lifestyle', 'Lifestyle'), ('travel', 'Travel'), ('food', 'Food & Beverage'), ('fashion', 'Fashion'), ('gaming', 'Gaming'), ('education', 'Education'), ('finance', 'Finance'), ('automotive', 'Automotive'), ('real_estate', 'Real Estate'), ('environment', 'Environment'), ('other', 'Other')], max_length=50, unique=True)),
                ('display_name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('color', models.CharField(default='#007bff', help_text='Hex color code for UI display', max_length=7)),
                ('icon', models.CharField(default='bi-tag', help_text='Bootstrap icon class', max_length=50)),
                ('keywords', models.JSONField(blank=True, default=list, help_text='Keywords for auto-categorization')),
                ('priority_score', models.FloatField(default=1.0, help_text='Priority multiplier for AI highlighting')),
                ('is_active', models.BooleanField(default=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['display_name'],
            },
        ),
        migrations.CreateModel(
            name='NewsSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('source_type', models.CharField(choices=[('rss', 'RSS Feed'), ('api', 'API Integration'), ('scraper', 'Web Scraper'), ('manual', 'Manual Entry')], max_length=20)),
                ('url', models.URLField(help_text='RSS feed URL or API endpoint')),
                ('fetch_interval', models.PositiveIntegerField(default=60, help_text='Fetch interval in minutes')),
                ('max_articles_per_fetch', models.PositiveIntegerField(default=50)),
                ('api_config', models.JSONField(blank=True, default=dict, help_text='API keys, headers, parameters')),
                ('title_selector', models.CharField(blank=True, help_text='CSS selector for title', max_length=200)),
                ('content_selector', models.CharField(blank=True, help_text='CSS selector for content', max_length=200)),
                ('image_selector', models.CharField(blank=True, help_text='CSS selector for image', max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('last_fetch', models.DateTimeField(blank=True, null=True)),
                ('last_success', models.DateTimeField(blank=True, null=True)),
                ('fetch_count', models.PositiveIntegerField(default=0)),
                ('error_count', models.PositiveIntegerField(default=0)),
                ('last_error', models.TextField(blank=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('categories', models.ManyToManyField(blank=True, help_text='Categories this source covers', to='images.category')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='NewsFeedPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('max_articles_per_day', models.PositiveIntegerField(default=100)),
                ('min_highlight_score', models.FloatField(default=60)),
                ('enable_auto_highlighting', models.BooleanField(default=True)),
                ('enable_auto_posting', models.BooleanField(default=False)),
                ('enable_sentiment_filter', models.BooleanField(default=True)),
                ('sentiment_threshold', models.FloatField(default=0.0, help_text='Minimum sentiment score (-1 to 1)')),
                ('notify_highlights', models.BooleanField(default=True)),
                ('notify_auto_posts', models.BooleanField(default=True)),
                ('notification_frequency', models.CharField(choices=[('immediate', 'Immediate'), ('hourly', 'Hourly'), ('daily', 'Daily'), ('weekly', 'Weekly')], default='daily', max_length=20)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('preferred_categories', models.ManyToManyField(blank=True, to='images.category')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='news_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'News Feed Preference',
                'verbose_name_plural': 'News Feed Preferences',
            },
        ),
        migrations.CreateModel(
            name='NewsArticle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=500)),
                ('content', models.TextField()),
                ('summary', models.TextField(blank=True, help_text='AI-generated summary')),
                ('url', models.URLField(unique=True)),
                ('image_url', models.URLField(blank=True)),
                ('author', models.CharField(blank=True, max_length=200)),
                ('published_date', models.DateTimeField()),
                ('fetched_date', models.DateTimeField(auto_now_add=True)),
                ('sentiment_score', models.FloatField(blank=True, help_text='Sentiment analysis score (-1 to 1)', null=True)),
                ('engagement_prediction', models.FloatField(blank=True, help_text='Predicted engagement score (0-100)', null=True)),
                ('trending_score', models.FloatField(blank=True, help_text='Trending potential score (0-100)', null=True)),
                ('keywords', models.JSONField(blank=True, default=list)),
                ('entities', models.JSONField(blank=True, default=list, help_text='Named entities extracted from content')),
                ('topics', models.JSONField(blank=True, default=list, help_text='Topic classification results')),
                ('social_shares', models.PositiveIntegerField(default=0)),
                ('social_comments', models.PositiveIntegerField(default=0)),
                ('social_likes', models.PositiveIntegerField(default=0)),
                ('is_processed', models.BooleanField(default=False)),
                ('is_highlighted', models.BooleanField(default=False)),
                ('processing_error', models.TextField(blank=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('categories', models.ManyToManyField(blank=True, to='images.category')),
                ('source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='articles', to='images.newssource')),
            ],
            options={
                'ordering': ['-published_date'],
            },
        ),
        migrations.CreateModel(
            name='AIHighlight',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight_score', models.FloatField(help_text='Overall highlight score (0-100)')),
                ('highlight_reasons', models.JSONField(default=list, help_text='List of reasons for highlighting')),
                ('recommended_platforms', models.JSONField(default=list, help_text='Recommended social media platforms')),
                ('suggested_posting_time', models.DateTimeField(blank=True, null=True)),
                ('suggested_hashtags', models.JSONField(blank=True, default=list)),
                ('suggested_caption', models.TextField(blank=True)),
                ('was_posted', models.BooleanField(default=False)),
                ('actual_engagement', models.FloatField(blank=True, help_text='Actual engagement if posted', null=True)),
                ('prediction_accuracy', models.FloatField(blank=True, help_text='How accurate was the prediction', null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('article', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='highlight', to='images.newsarticle')),
            ],
            options={
                'ordering': ['-highlight_score', '-created'],
            },
        ),
        migrations.CreateModel(
            name='AccountCategoryMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.PositiveIntegerField(default=1, help_text='Priority level (1=highest)')),
                ('auto_post', models.BooleanField(default=False, help_text='Automatically post highlighted articles')),
                ('min_highlight_score', models.FloatField(default=70, help_text='Minimum score for auto-posting')),
                ('posting_schedule', models.JSONField(blank=True, default=dict, help_text='Preferred posting times')),
                ('custom_hashtags', models.TextField(blank=True, help_text='Additional hashtags for this category')),
                ('caption_template', models.TextField(blank=True, help_text='Custom caption template')),
                ('articles_posted', models.PositiveIntegerField(default=0)),
                ('avg_engagement', models.FloatField(default=0.0)),
                ('last_posted', models.DateTimeField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='category_mappings', to='images.socialmediaaccount')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='account_mappings', to='images.category')),
            ],
            options={
                'ordering': ['account', 'priority'],
            },
        ),
        migrations.AddIndex(
            model_name='newssource',
            index=models.Index(fields=['source_type', 'is_active'], name='images_news_source__424c96_idx'),
        ),
        migrations.AddIndex(
            model_name='newssource',
            index=models.Index(fields=['last_fetch'], name='images_news_last_fe_41469d_idx'),
        ),
        migrations.AddIndex(
            model_name='newsarticle',
            index=models.Index(fields=['source', '-published_date'], name='images_news_source__06fe6e_idx'),
        ),
        migrations.AddIndex(
            model_name='newsarticle',
            index=models.Index(fields=['is_highlighted', '-engagement_prediction'], name='images_news_is_high_018591_idx'),
        ),
        migrations.AddIndex(
            model_name='newsarticle',
            index=models.Index(fields=['is_processed'], name='images_news_is_proc_b6da0e_idx'),
        ),
        migrations.AddIndex(
            model_name='newsarticle',
            index=models.Index(fields=['-trending_score'], name='images_news_trendin_a1bb42_idx'),
        ),
        migrations.AddIndex(
            model_name='aihighlight',
            index=models.Index(fields=['-highlight_score'], name='images_aihi_highlig_e374da_idx'),
        ),
        migrations.AddIndex(
            model_name='aihighlight',
            index=models.Index(fields=['was_posted'], name='images_aihi_was_pos_32ca3e_idx'),
        ),
        migrations.AddIndex(
            model_name='accountcategorymapping',
            index=models.Index(fields=['account', 'auto_post'], name='images_acco_account_fae07e_idx'),
        ),
        migrations.AddIndex(
            model_name='accountcategorymapping',
            index=models.Index(fields=['category', 'priority'], name='images_acco_categor_ff88da_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accountcategorymapping',
            unique_together={('account', 'category')},
        ),
    ]
