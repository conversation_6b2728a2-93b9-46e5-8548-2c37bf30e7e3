# Generated by Django 4.2.7 on 2025-07-12 19:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('images', '0019_contenttemplate_postanalytics_savedhashtagset_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='useranalytics',
            old_name='last_updated',
            new_name='last_calculated',
        ),
        migrations.AddField(
            model_name='contenttemplate',
            name='hashtags',
            field=models.TextField(blank=True, help_text='Default hashtags for this template'),
        ),
        migrations.AddField(
            model_name='contenttemplate',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Featured template'),
        ),
        migrations.AddField(
            model_name='postanalytics',
            name='click_through_rate',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='postanalytics',
            name='platform_data',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='postanalytics',
            name='saves_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='savedhashtagset',
            name='avg_engagement_rate',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='savedhashtagset',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='savedhashtagset',
            name='updated',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='useranalytics',
            name='avg_comments_per_post',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='useranalytics',
            name='avg_likes_per_post',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='useranalytics',
            name='content_type_performance',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='useranalytics',
            name='engagement_growth_rate',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='useranalytics',
            name='follower_growth_rate',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='useranalytics',
            name='top_performing_hashtags',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='useranalytics',
            name='total_shares',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='contenttemplate',
            name='content',
            field=models.TextField(help_text='Template content with placeholders like {product_name}'),
        ),
        migrations.AlterField(
            model_name='contenttemplate',
            name='industry',
            field=models.CharField(blank=True, choices=[('technology', 'Technology'), ('fashion', 'Fashion'), ('food', 'Food & Beverage'), ('travel', 'Travel'), ('fitness', 'Fitness & Health'), ('business', 'Business'), ('lifestyle', 'Lifestyle'), ('education', 'Education'), ('entertainment', 'Entertainment'), ('other', 'Other')], max_length=50),
        ),
        migrations.AlterField(
            model_name='contenttemplate',
            name='is_public',
            field=models.BooleanField(default=False, help_text='Make template available to other users'),
        ),
        migrations.AlterField(
            model_name='contenttemplate',
            name='template_type',
            field=models.CharField(choices=[('caption', 'Caption Template'), ('hashtag_set', 'Hashtag Set'), ('post_series', 'Post Series'), ('campaign', 'Campaign Template')], max_length=20),
        ),
        migrations.AlterField(
            model_name='contenttemplate',
            name='tone',
            field=models.CharField(blank=True, choices=[('professional', 'Professional'), ('casual', 'Casual'), ('humorous', 'Humorous'), ('inspirational', 'Inspirational'), ('educational', 'Educational'), ('promotional', 'Promotional')], max_length=20),
        ),
        migrations.AddIndex(
            model_name='postanalytics',
            index=models.Index(fields=['post', '-last_updated'], name='images_post_post_id_b992a8_idx'),
        ),
        migrations.AddIndex(
            model_name='postanalytics',
            index=models.Index(fields=['engagement_rate'], name='images_post_engagem_001950_idx'),
        ),
        migrations.AddIndex(
            model_name='savedhashtagset',
            index=models.Index(fields=['user', 'industry'], name='images_save_user_id_187524_idx'),
        ),
    ]
