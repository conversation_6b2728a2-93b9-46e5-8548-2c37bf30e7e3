{% extends 'base.html' %}
{% load dict_extras %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-broadcast"></i> Multi-Platform Posting
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'ai-studio' %}" class="btn btn-outline-primary">
                        <i class="bi bi-robot"></i> AI Studio
                    </a>
                    <a href="{% url 'analytics' %}" class="btn btn-outline-info">
                        <i class="bi bi-graph-up"></i> Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Platform Status Cards -->
    <div class="row mb-4">
        {% for platform_key, platform in platform_info.items %}
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card {% if platform_key in available_platforms %}border-success{% else %}border-secondary{% endif %}">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <i class="bi {{ platform.icon }} display-6 text-{{ platform.color }}"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-1">{{ platform.name }}</h5>
                            {% if platform_key in available_platforms %}
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle"></i> Connected
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="bi bi-x-circle"></i> Not Connected
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="features">
                        <small class="text-muted">Features:</small>
                        <ul class="list-unstyled small mt-1">
                            {% for feature in platform.features %}
                            <li><i class="bi bi-check text-success"></i> {{ feature }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Posting Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-pencil-square"></i> Create Multi-Platform Post
                    </h5>
                </div>
                <div class="card-body">
                    {% if available_platforms %}
                    <form method="post" enctype="multipart/form-data" id="multiPlatformForm">
                        {% csrf_token %}
                        
                        <!-- Platform Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Select Platforms</label>
                            <div class="row">
                                {% for platform_key in available_platforms %}
                                {% with platform=platform_info|lookup:platform_key %}
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="platforms" value="{{ platform_key }}" id="platform_{{ platform_key }}">
                                        <label class="form-check-label d-flex align-items-center" for="platform_{{ platform_key }}">
                                            <i class="bi {{ platform.icon }} text-{{ platform.color }} me-2"></i>
                                            {{ platform.name }}
                                        </label>
                                    </div>
                                </div>
                                {% endwith %}
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Content Text -->
                        <div class="mb-4">
                            <label for="content_text" class="form-label fw-bold">Content Text</label>
                            <textarea class="form-control" id="content_text" name="content_text" rows="5" 
                                      placeholder="Write your post content here..."></textarea>
                            <div class="form-text">
                                <div class="d-flex justify-content-between">
                                    <span>Character count: <span id="charCount">0</span></span>
                                    <span class="text-muted">
                                        <i class="bi bi-info-circle"></i> 
                                        Twitter: 280 chars max, Instagram: 2200 chars max
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div class="mb-4">
                            <label for="image" class="form-label fw-bold">Image (Optional)</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <div class="form-text">
                                <i class="bi bi-info-circle"></i> 
                                Supported formats: JPG, PNG, GIF. Max size: 10MB
                            </div>
                            <!-- Image Preview -->
                            <div id="imagePreview" class="mt-3" style="display: none;">
                                <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 300px;">
                            </div>
                        </div>

                        <!-- Hashtag Suggestions -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Hashtag Suggestions</label>
                            <div id="hashtagSuggestions" class="d-flex flex-wrap gap-2">
                                <span class="badge bg-primary hashtag-suggestion" data-hashtag="#socialmedia">#socialmedia</span>
                                <span class="badge bg-primary hashtag-suggestion" data-hashtag="#content">#content</span>
                                <span class="badge bg-primary hashtag-suggestion" data-hashtag="#marketing">#marketing</span>
                                <span class="badge bg-primary hashtag-suggestion" data-hashtag="#business">#business</span>
                                <span class="badge bg-primary hashtag-suggestion" data-hashtag="#technology">#technology</span>
                            </div>
                            <small class="text-muted">Click hashtags to add them to your content</small>
                        </div>

                        <!-- Platform-Specific Previews -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Platform Previews</label>
                            <div class="row">
                                <!-- Instagram Preview -->
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-header py-2">
                                            <small class="text-muted">
                                                <i class="bi bi-instagram text-danger"></i> Instagram Preview
                                            </small>
                                        </div>
                                        <div class="card-body p-2">
                                            <div class="instagram-preview">
                                                <div class="preview-image bg-secondary rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                                                    <i class="bi bi-image text-white"></i>
                                                </div>
                                                <div class="preview-caption">
                                                    <small id="instagramPreview" class="text-muted">Your caption will appear here...</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Twitter Preview -->
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-header py-2">
                                            <small class="text-muted">
                                                <i class="bi bi-twitter text-info"></i> Twitter Preview
                                            </small>
                                        </div>
                                        <div class="card-body p-2">
                                            <div class="twitter-preview">
                                                <div class="d-flex mb-2">
                                                    <div class="bg-secondary rounded-circle me-2" style="width: 30px; height: 30px;"></div>
                                                    <div class="flex-grow-1">
                                                        <small class="fw-bold">{{ user.username }}</small>
                                                        <small class="text-muted">@{{ user.username }}</small>
                                                    </div>
                                                </div>
                                                <div class="preview-text">
                                                    <small id="twitterPreview" class="text-muted">Your tweet will appear here...</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- LinkedIn Preview -->
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-header py-2">
                                            <small class="text-muted">
                                                <i class="bi bi-linkedin text-primary"></i> LinkedIn Preview
                                            </small>
                                        </div>
                                        <div class="card-body p-2">
                                            <div class="linkedin-preview">
                                                <div class="d-flex mb-2">
                                                    <div class="bg-secondary rounded-circle me-2" style="width: 30px; height: 30px;"></div>
                                                    <div class="flex-grow-1">
                                                        <small class="fw-bold">{{ user.first_name }} {{ user.last_name }}</small>
                                                        <small class="text-muted d-block">Professional</small>
                                                    </div>
                                                </div>
                                                <div class="preview-text">
                                                    <small id="linkedinPreview" class="text-muted">Your post will appear here...</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                <i class="bi bi-broadcast"></i> Post to Selected Platforms
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <!-- No platforms connected -->
                    <div class="text-center py-5">
                        <i class="bi bi-exclamation-triangle display-1 text-warning"></i>
                        <h4 class="mt-3">No Platforms Connected</h4>
                        <p class="text-muted">You need to connect at least one social media platform to start posting.</p>
                        <div class="mt-4">
                            <h6>To connect platforms, add these to your .env file:</h6>
                            <div class="text-start mt-3">
                                <code class="d-block mb-2">FACEBOOK_ACCESS_TOKEN=your_token</code>
                                <code class="d-block mb-2">INSTAGRAM_ACCOUNT_ID=your_id</code>
                                <code class="d-block mb-2">TWITTER_BEARER_TOKEN=your_token</code>
                                <code class="d-block mb-2">LINKEDIN_ACCESS_TOKEN=your_token</code>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const contentText = document.getElementById('content_text');
    const charCount = document.getElementById('charCount');
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const hashtagSuggestions = document.querySelectorAll('.hashtag-suggestion');
    
    // Character count
    if (contentText && charCount) {
        contentText.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;
            
            // Update previews
            updatePreviews(this.value);
            
            // Color coding for character limits
            if (count > 280) {
                charCount.className = 'text-warning';
            } else if (count > 2200) {
                charCount.className = 'text-danger';
            } else {
                charCount.className = 'text-success';
            }
        });
    }
    
    // Image preview
    if (imageInput && imagePreview) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    imagePreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                imagePreview.style.display = 'none';
            }
        });
    }
    
    // Hashtag suggestions
    hashtagSuggestions.forEach(function(suggestion) {
        suggestion.addEventListener('click', function() {
            const hashtag = this.dataset.hashtag;
            if (contentText) {
                const currentText = contentText.value;
                const newText = currentText + (currentText ? ' ' : '') + hashtag;
                contentText.value = newText;
                contentText.dispatchEvent(new Event('input'));
            }
        });
    });
    
    // Update platform previews
    function updatePreviews(text) {
        const instagramPreview = document.getElementById('instagramPreview');
        const twitterPreview = document.getElementById('twitterPreview');
        const linkedinPreview = document.getElementById('linkedinPreview');
        
        const previewText = text || 'Your content will appear here...';
        
        if (instagramPreview) {
            instagramPreview.textContent = previewText;
        }
        
        if (twitterPreview) {
            const twitterText = previewText.length > 280 ? previewText.substring(0, 277) + '...' : previewText;
            twitterPreview.textContent = twitterText;
        }
        
        if (linkedinPreview) {
            linkedinPreview.textContent = previewText;
        }
    }
    
    // Form validation
    const form = document.getElementById('multiPlatformForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const selectedPlatforms = document.querySelectorAll('input[name="platforms"]:checked');
            const contentText = document.getElementById('content_text').value.trim();
            const imageFile = document.getElementById('image').files[0];
            
            if (selectedPlatforms.length === 0) {
                e.preventDefault();
                alert('Please select at least one platform to post to.');
                return;
            }
            
            if (!contentText && !imageFile) {
                e.preventDefault();
                alert('Please provide either content text or an image.');
                return;
            }
        });
    }
});
</script>

<style>
.hashtag-suggestion {
    cursor: pointer;
    transition: all 0.2s ease;
}

.hashtag-suggestion:hover {
    transform: scale(1.05);
    opacity: 0.8;
}

.preview-image {
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.form-check-input:checked + .form-check-label {
    font-weight: 600;
}
</style>
{% endblock %}
