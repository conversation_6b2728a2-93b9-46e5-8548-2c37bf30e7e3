@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css");
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@300;400;500;700&display=swap');

/* CSS Custom Properties for consistent theming */
:root {
    --primary-bg: rgba(31, 37, 45, 0.95);
    --secondary-bg: rgba(0, 0, 0, 0.445);
    --accent-color: #007bff;
    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --text-light: #ffffff;
    --text-muted: #6c757d;
    --border-color: #000000;
    --hover-shadow: rgba(0, 0, 0, 0.3);
    --transition-speed: 0.3s;
}

body {
    background: linear-gradient(135deg, var(--primary-bg), rgba(45, 52, 65, 0.9));
    font-family: 'Roboto Mono', monospace;
    color: var(--text-light);
    min-height: 100vh;
}

#navbar {
    height: 100px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--accent-color);
}

/* Enhanced alert styles */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
    border-left: 4px solid var(--error-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.success {
    color: var(--success-color);
    transition: all ease-in var(--transition-speed);
    font-weight: 500;
}

.error {
    color: var(--error-color);
    transition: all ease-in var(--transition-speed);
    font-weight: 500;
}

/* Enhanced card styles */
.card {
    transition: all var(--transition-speed) ease;
    border: 2px solid var(--border-color);
    background: var(--secondary-bg);
    backdrop-filter: blur(5px);
    border-radius: 12px;
    overflow: hidden;
}

.card-body {
    padding: 1rem;
}

.card img {
    height: 250px;
    object-fit: cover;
    transition: transform var(--transition-speed) ease;
}

.card:hover {
    box-shadow: 0 8px 25px var(--hover-shadow);
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

.card:hover img {
    transform: scale(1.05);
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Form enhancements */
.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    border-radius: 8px;
    transition: all var(--transition-speed) ease;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    color: var(--text-light);
}

.form-control::placeholder {
    color: var(--text-muted);
}

/* Button enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all var(--transition-speed) ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--accent-color), #0056b3);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
}

.btn-outline-primary:hover {
    background: var(--accent-color);
    transform: translateY(-1px);
}

button#results {
    border-radius: 0 20px 20px 0;
    
}

.album{
    border-radius: 20px;
}

#search {
    border-radius: 20px 0 0 20px;
}

button.btn:hover {
    box-shadow: 2px 2px 3px black;
}



i {
    padding: .8em;
}


.htmx-indicator{
    opacity:0;
    transition: opacity 500ms ease-in;
}
.htmx-indicat{
    opacity: 0;
    display: none;
}

.htmx-request.htmx-indicat{
    display: block;
    transition: opacity 1s ease-in;
    opacity: 1;
    padding-left: 2em;
}

.htmx-request .htmx-indicator{
    opacity:1
}
.htmx-request.htmx-indicator{
    opacity:1
}

form.input-group {

    width: 80%;
    height: 20%;
    margin: 1em auto;
}

#index-card {
    height: 15em;

}

#news_item:hover {
    background-color: #373232cf !important;
}

#news_item p {
    margin: 0 0 0.3rem 0;
}

h2.h2 {
    margin: 0;
    border-radius: 20px 20px 0 0 ;
    background-image: url('../images/tech.jpg') ;
    background-position: center;
}


.list-group-item:first-child {
    border-radius: 0;
}

li span {
    position: absolute;
    margin: -1em;
    background-color: #dc3545;
    width: 6.7%;
    border-radius: 0 0 17px;
    text-align: center;
    padding: .2em;
}

li#news_item p {
    margin: 1.4em 0 .7em 0;
}

#feed-title {
    /* border: 2px #dc3545 solid; */
    height: 40%;
    padding: 1em;
    border-radius: 10px;
    color: white;
}