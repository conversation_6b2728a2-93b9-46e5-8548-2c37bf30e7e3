{% extends 'base.html' %}
{% load static %}
{% block content %}

{% if user.is_authenticated %}
<!-- Hero Section -->
<div class="hero-section py-5 mb-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="text-center text-lg-start">
                    <img class="mb-3" src="{% static 'images/logoo.png' %}" alt="Imagsta" width="60" height="48">
                    <h1 class="display-4 fw-bold text-primary mb-3">
                        Welcome back to Imagsta
                    </h1>
                    <p class="lead text-muted mb-4">
                        Create stunning social media content with AI-powered tools,
                        professional templates, and seamless platform integration.
                    </p>

                    <!-- Quick Actions -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-start mb-4">
                        <a href="{% url 'ai-studio' %}" class="btn btn-primary btn-lg px-4">
                            <i class="bi bi-magic"></i> AI Studio
                        </a>
                        <a href="{% url 'templates' %}" class="btn btn-outline-secondary btn-lg px-4">
                            <i class="bi bi-file-text"></i> Templates
                        </a>
                    </div>

                    <!-- Quick Stats -->
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-card p-3 bg-light rounded">
                                <h4 class="text-primary mb-1">{{ user_stats.total_posts|default:0 }}</h4>
                                <small class="text-muted">Posts</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-card p-3 bg-light rounded">
                                <h4 class="text-success mb-1">{{ user_stats.total_templates|default:0 }}</h4>
                                <small class="text-muted">Templates</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-card p-3 bg-light rounded">
                                <h4 class="text-info mb-1">{{ user_stats.connected_accounts|default:0 }}</h4>
                                <small class="text-muted">Connected</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <img src="{% static 'images/tech.jpg' %}" alt="Content Creation" class="img-fluid rounded shadow-lg">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI News Highlights Section -->
{% if has_news %}
<div class="container mb-5">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-warning">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-star-fill"></i> AI-Highlighted News
                    </h5>
                    <div>
                        <span class="badge bg-dark me-2">{{ news_stats.total_highlights }} highlights</span>
                        <a href="{% url 'news-dashboard' %}" class="btn btn-dark btn-sm">
                            <i class="bi bi-newspaper"></i> View All News
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if recent_highlights %}
                        <div class="row">
                            {% for highlight in recent_highlights %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100 border-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="badge bg-warning text-dark">
                                                <i class="bi bi-star"></i> {{ highlight.highlight_score|floatformat:0 }}
                                            </span>
                                            <small class="text-muted">
                                                {{ highlight.article.published_date|timesince }} ago
                                            </small>
                                        </div>

                                        <h6 class="card-title">
                                            <a href="{{ highlight.article.url }}" target="_blank" class="text-decoration-none">
                                                {{ highlight.article.title|truncatechars:60 }}
                                            </a>
                                        </h6>

                                        <p class="card-text small text-muted">
                                            {{ highlight.article.content|truncatechars:100 }}
                                        </p>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-rss"></i> {{ highlight.article.source.name }}
                                            </small>
                                            <button class="btn btn-sm btn-primary"
                                                    onclick="createPostFromNews('{{ highlight.article.id }}', '{{ highlight.article.title|escapejs }}')">
                                                <i class="bi bi-plus"></i> Create Post
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="text-center mt-3">
                            <a href="{% url 'news-highlights' %}" class="btn btn-outline-warning">
                                <i class="bi bi-eye"></i> View All {{ news_stats.total_highlights }} Highlights
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-newspaper text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-2">No highlights yet</h5>
                            <p class="text-muted">AI will analyze articles and highlight the best ones for posting.</p>
                            <a href="{% url 'news-dashboard' %}" class="btn btn-warning">
                                <i class="bi bi-gear"></i> Configure News Sources
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Main Dashboard -->
<div class="container mb-5">
    <!-- Social Media Accounts Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-share"></i> Connected Accounts
                    </h5>
                    <a href="{% url 'connect-accounts' %}" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle"></i> Connect Account
                    </a>
                </div>
                <div class="card-body">
                    {% if connected_accounts %}
                        <div class="row">
                            {% for account in connected_accounts %}
                            <div class="col-md-4 mb-3">
                                <div class="account-card p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="platform-icon me-3">
                                            {% if account.platform == 'instagram' %}
                                                <i class="bi bi-instagram text-danger fs-3"></i>
                                            {% elif account.platform == 'twitter' %}
                                                <i class="bi bi-twitter text-info fs-3"></i>
                                            {% elif account.platform == 'linkedin' %}
                                                <i class="bi bi-linkedin text-primary fs-3"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="mb-1">{{ account.platform|title }}</h6>
                                            <small class="text-muted">@{{ account.username }}</small>
                                            <br>
                                            <span class="badge bg-success">Connected</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-share text-muted" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-3">No accounts connected yet</h6>
                            <p class="text-muted">Connect your social media accounts to start posting</p>
                            <a href="{% url 'connect-accounts' %}" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Connect Your First Account
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Posts & Templates -->
    <div class="row">
        <!-- Recent Posts -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-images"></i> Recent Posts
                    </h5>
                    <a href="{% url 'posts' %}" class="btn btn-outline-primary btn-sm">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_posts %}
                        {% for post in recent_posts %}
                        <div class="post-item d-flex align-items-center mb-3 p-2 border rounded">
                            {% if post.image %}
                                <img src="{{ post.image.url }}" alt="Post" class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                            {% else %}
                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="bi bi-image text-muted"></i>
                                </div>
                            {% endif %}
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ post.caption|truncatechars:40 }}</h6>
                                <small class="text-muted">{{ post.created|timesince }} ago</small>
                                {% if post.posted %}
                                    <span class="badge bg-success ms-2">Posted</span>
                                {% else %}
                                    <span class="badge bg-warning ms-2">Draft</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-images text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2">No posts yet</p>
                            <a href="{% url 'ai-studio' %}" class="btn btn-primary btn-sm">Create Your First Post</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Popular Templates -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-file-text"></i> Popular Templates
                    </h5>
                    <a href="{% url 'templates' %}" class="btn btn-outline-primary btn-sm">View All</a>
                </div>
                <div class="card-body">
                    {% if popular_templates %}
                        {% for template in popular_templates %}
                        <div class="template-item mb-3 p-3 border rounded">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ template.name }}</h6>
                                    <small class="text-muted">{{ template.template_type|title }} • {{ template.industry|title }}</small>
                                    <p class="text-muted mt-1 mb-2">{{ template.content|truncatechars:60 }}</p>
                                </div>
                                <span class="badge bg-info">{{ template.usage_count }} uses</span>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" onclick="useTemplate({{ template.id }})">
                                <i class="bi bi-play"></i> Use Template
                            </button>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-file-text text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2">No templates yet</p>
                            <a href="{% url 'templates' %}" class="btn btn-primary btn-sm">Browse Templates</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}

<div class="px-4 my-5 text-center">
  <img class="d-block mx-auto mb-4 rounded" src="{% static 'images/logoo.png' %}" alt="" width="72" height="57">
  <h1 class="display-5 fw-bold mb-4 text-light">Imgsta</h1>
  <div class="col-lg-6 mx-auto">
    <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
      <a type="button" href="{% url 'login' %}" class="btn btn-danger btn-lg my-auto fw-bold p-3">Login</a>
    </div>

  </div>
</div>

{% endif %}

<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-card {
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.account-card {
    transition: all 0.2s;
}

.account-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.post-item:hover {
    background-color: #f8f9fa;
}

.template-item:hover {
    background-color: #f8f9fa;
}

.platform-icon {
    transition: transform 0.2s;
}

.platform-icon:hover {
    transform: scale(1.1);
}
</style>

<script>
function useTemplate(templateId) {
    // Redirect to AI Studio with template pre-selected
    window.location.href = `/ai-studio/?template=${templateId}`;
}

// Add smooth scrolling for better UX
document.addEventListener('DOMContentLoaded', function() {
    // Add loading states for buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.href && !this.href.includes('#')) {
                this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Loading...';
            }
        });
    });
});

// Add CSS for spinning animation
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);

// Function to create post from news article
function createPostFromNews(articleId, articleTitle) {
    // Redirect to AI studio with news article data
    const url = `/ai-studio/?news_article_id=${articleId}&title=${encodeURIComponent(articleTitle)}`;
    window.location.href = url;
}
</script>

{% endblock content %}