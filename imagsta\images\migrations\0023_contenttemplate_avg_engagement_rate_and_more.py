# Generated by Django 4.2.7 on 2025-07-19 15:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('images', '0022_category_newssource_newsfeedpreference_newsarticle_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='contenttemplate',
            name='avg_engagement_rate',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='contenttemplate',
            name='total_uses',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='contenttemplate',
            name='variables',
            field=models.JSONField(blank=True, default=list, help_text='List of variables used in template'),
        ),
        migrations.AddIndex(
            model_name='contenttemplate',
            index=models.Index(fields=['user', 'template_type'], name='images_cont_user_id_d8c173_idx'),
        ),
        migrations.AddIndex(
            model_name='contenttemplate',
            index=models.Index(fields=['industry', 'tone'], name='images_cont_industr_9b5677_idx'),
        ),
        migrations.AddIndex(
            model_name='contenttemplate',
            index=models.Index(fields=['is_public', 'is_featured'], name='images_cont_is_publ_3c9b39_idx'),
        ),
    ]
