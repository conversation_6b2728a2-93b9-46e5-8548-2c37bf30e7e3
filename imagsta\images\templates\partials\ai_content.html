{% if error %}
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle"></i>
        {{ error }}
    </div>
{% else %}
    <!-- Generated Caption -->
    {% if generated_caption %}
    <div class="mb-4">
        <h6 class="text-primary">
            <i class="bi bi-chat-quote"></i> Generated Caption
        </h6>
        <div class="bg-light p-3 rounded">
            <p class="mb-2">{{ generated_caption }}</p>
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    <i class="bi bi-info-circle"></i> Tone: {{ tone|title }}
                </small>
                <button class="btn btn-sm btn-outline-primary" 
                        onclick="copyToClipboard('{{ generated_caption|escapejs }}')">
                    <i class="bi bi-clipboard"></i> Copy
                </button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Suggested Hashtags -->
    {% if suggested_hashtags %}
    <div class="mb-4">
        <h6 class="text-success">
            <i class="bi bi-hash"></i> Suggested Hashtags
        </h6>
        <div class="bg-light p-3 rounded">
            <div class="hashtag-container">
                {% for hashtag in suggested_hashtags %}
                    <span class="badge bg-primary me-1 mb-1 hashtag-badge" 
                          onclick="toggleHashtag(this)">
                        {{ hashtag }}
                    </span>
                {% endfor %}
            </div>
            <div class="mt-2 d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    <i class="bi bi-info-circle"></i> Click hashtags to select/deselect
                </small>
                <button class="btn btn-sm btn-outline-success" 
                        onclick="copySelectedHashtags()">
                    <i class="bi bi-clipboard"></i> Copy Selected
                </button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Posting Suggestions -->
    {% if posting_suggestions %}
    <div class="mb-4">
        <h6 class="text-warning">
            <i class="bi bi-clock"></i> Optimal Posting Time
        </h6>
        <div class="bg-light p-3 rounded">
            <div class="row">
                <div class="col-6">
                    <strong>Best Day:</strong> {{ posting_suggestions.best_day }}
                </div>
                <div class="col-6">
                    <strong>Best Time:</strong> {{ posting_suggestions.best_time }}
                </div>
            </div>
            {% if posting_suggestions.alternative_times %}
            <div class="mt-2">
                <strong>Alternative Times:</strong>
                {% for time in posting_suggestions.alternative_times %}
                    <span class="badge bg-secondary me-1">{{ time }}</span>
                {% endfor %}
            </div>
            {% endif %}
            <small class="text-muted d-block mt-2">
                <i class="bi bi-lightbulb"></i> {{ posting_suggestions.reasoning }}
            </small>
        </div>
    </div>
    {% endif %}

    <!-- Performance Analysis -->
    {% if performance_analysis %}
    <div class="mb-4">
        <h6 class="text-info">
            <i class="bi bi-graph-up"></i> Performance Prediction
        </h6>
        <div class="bg-light p-3 rounded">
            <div class="row mb-2">
                <div class="col-6">
                    <strong>Engagement Score:</strong>
                    <span class="badge bg-info">{{ performance_analysis.engagement_score }}/10</span>
                </div>
                <div class="col-6">
                    <strong>Virality Potential:</strong>
                    <span class="badge bg-warning">{{ performance_analysis.virality_potential }}</span>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-6">
                    <strong>Predicted Likes:</strong> {{ performance_analysis.predicted_likes }}
                </div>
                <div class="col-6">
                    <strong>Predicted Comments:</strong> {{ performance_analysis.predicted_comments }}
                </div>
            </div>
            {% if performance_analysis.suggestions %}
            <div class="mt-2">
                <strong>AI Suggestions:</strong>
                <ul class="mb-0 mt-1">
                    {% for suggestion in performance_analysis.suggestions %}
                        <li class="small text-muted">{{ suggestion }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="d-grid gap-2">
        <button class="btn btn-success" onclick="createPostFromAI()">
            <i class="bi bi-plus-circle"></i> Create Post with This Content
        </button>
        <button class="btn btn-outline-primary" onclick="regenerateContent()">
            <i class="bi bi-arrow-clockwise"></i> Regenerate Content
        </button>
    </div>
{% endif %}

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('Copied to clipboard!', 'success');
    });
}

function toggleHashtag(element) {
    element.classList.toggle('bg-primary');
    element.classList.toggle('bg-secondary');
    element.classList.toggle('selected');
}

function copySelectedHashtags() {
    const selectedHashtags = document.querySelectorAll('.hashtag-badge.selected');
    const hashtags = Array.from(selectedHashtags).map(el => el.textContent.trim()).join(' ');
    
    if (hashtags) {
        copyToClipboard(hashtags);
    } else {
        showToast('Please select some hashtags first', 'warning');
    }
}

function createPostFromAI() {
    const caption = document.querySelector('[onclick*="generated_caption"]');
    const selectedHashtags = document.querySelectorAll('.hashtag-badge.selected');
    
    // This would integrate with the existing post creation flow
    showToast('Feature coming soon! This will create a new post with the AI content.', 'info');
}

function regenerateContent() {
    // Re-submit the form to generate new content
    document.querySelector('form').dispatchEvent(new Event('submit'));
}

function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Auto-select all hashtags initially
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.hashtag-badge').forEach(badge => {
        badge.classList.add('selected');
    });
});
</script>

<style>
.hashtag-badge {
    cursor: pointer;
    transition: all 0.2s ease;
}

.hashtag-badge:hover {
    transform: scale(1.05);
}

.hashtag-badge.selected {
    background-color: var(--bs-primary) !important;
}

.hashtag-container {
    max-height: 150px;
    overflow-y: auto;
}

.bg-light {
    background-color: rgba(248, 249, 250, 0.8) !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
