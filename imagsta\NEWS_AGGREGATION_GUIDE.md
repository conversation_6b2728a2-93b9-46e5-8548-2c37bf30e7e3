# News Aggregation System Guide

## Overview

The News Aggregation System is a comprehensive solution that automatically fetches news from multiple sources, analyzes them with AI, highlights the best content, and can automatically post to social media accounts based on category mappings.

## Features

### 🔄 Multi-Source News Fetching
- **RSS Feeds**: Fetch from any RSS/Atom feed
- **Reddit Integration**: Pull posts from subreddits
- **News API**: Integration with NewsAPI.org
- **Custom Sources**: Extensible architecture for new sources

### 🤖 AI-Powered Analysis
- **Sentiment Analysis**: Analyze article sentiment (-1 to 1 scale)
- **Engagement Prediction**: Predict social media engagement potential (0-100)
- **Trending Score**: Calculate trending potential based on recency and social metrics
- **Content Classification**: Automatic topic and keyword extraction
- **Smart Highlighting**: AI determines which articles are worth posting

### 📱 Intelligent Social Media Integration
- **Account-Category Mapping**: Assign categories to social media accounts
- **Auto-Posting**: Automatically post highlighted articles to relevant accounts
- **Content Optimization**: AI-generated captions and hashtags
- **Multi-Platform Support**: Instagram, Twitter, LinkedIn, Facebook, TikTok

### 📊 Comprehensive Dashboard
- **News Dashboard**: Overview of highlights and recent articles
- **Category Management**: Organize content by topics
- **Source Management**: Monitor and configure news sources
- **Analytics**: Track performance and engagement

## Getting Started

### 1. Setup News Sources

```bash
# Create sample news sources
python manage.py fetch_news --create-sample-sources

# Or add sources manually through Django admin
# Go to: /admin/images/newssource/add/
```

### 2. Configure Categories

Categories are automatically created with sample sources, but you can customize them:

- Go to `/admin/images/category/`
- Modify existing categories or create new ones
- Set priority scores and keywords for better AI classification

### 3. Connect Social Media Accounts

1. Navigate to `/account-categories/`
2. Connect your social media accounts
3. Assign categories to each account
4. Configure auto-posting preferences

### 4. Run the News Pipeline

```bash
# Complete pipeline: fetch, analyze, and auto-post
python manage.py news_pipeline

# Fetch news only
python manage.py fetch_news

# Analyze existing articles
python manage.py analyze_news --limit 50

# Auto-post highlighted articles
python manage.py auto_post_news --max-posts 5
```

## Management Commands

### `fetch_news`
Fetches news articles from configured sources.

```bash
# Fetch from all sources
python manage.py fetch_news

# Force fetch regardless of schedule
python manage.py fetch_news --force

# Fetch from specific source
python manage.py fetch_news --source "TechCrunch RSS"

# Create sample sources for testing
python manage.py fetch_news --create-sample-sources
```

### `analyze_news`
Analyzes articles with AI and creates highlights.

```bash
# Analyze up to 50 unprocessed articles
python manage.py analyze_news --limit 50 --unprocessed-only

# Set minimum highlight score
python manage.py analyze_news --min-score 70

# Analyze specific category
python manage.py analyze_news --category technology
```

### `auto_post_news`
Automatically posts highlighted articles to social media.

```bash
# Create up to 10 auto-posts
python manage.py auto_post_news --max-posts 10

# Dry run to see what would be posted
python manage.py auto_post_news --dry-run

# Show auto-posting statistics
python manage.py auto_post_news --stats
```

### `news_pipeline`
Runs the complete news pipeline.

```bash
# Full pipeline
python manage.py news_pipeline

# Fetch and analyze only (no auto-posting)
python manage.py news_pipeline --no-auto-post

# Analyze existing articles only
python manage.py news_pipeline --analyze-only

# Force fetch from all sources
python manage.py news_pipeline --force-fetch
```

## Configuration

### News Sources

Configure news sources in Django admin (`/admin/images/newssource/`):

- **Name**: Descriptive name for the source
- **Source Type**: RSS, API, Reddit, or Scraper
- **URL**: Feed URL or API endpoint
- **Categories**: Assign relevant categories
- **Fetch Interval**: How often to fetch (in minutes)
- **Max Articles**: Maximum articles per fetch

### Categories

Manage categories in Django admin (`/admin/images/category/`):

- **Display Name**: Human-readable name
- **Keywords**: Keywords for auto-categorization
- **Priority Score**: Multiplier for AI highlighting
- **Color & Icon**: For UI display

### Account-Category Mapping

Configure through the web interface (`/account-categories/`):

- **Auto-posting**: Enable/disable automatic posting
- **Minimum Score**: Threshold for auto-posting
- **Priority**: Order of preference for posting

## AI Analysis Details

### Highlight Score Calculation

The AI highlight score (0-100) is calculated using weighted factors:

- **Engagement Prediction** (30%): Based on content quality, length, readability
- **Trending Score** (25%): Recency, social metrics, trending keywords
- **Sentiment Score** (15%): Normalized sentiment analysis
- **Social Metrics** (15%): Existing shares, likes, comments
- **Recency** (15%): How recent the article is

### Content Analysis

Each article is analyzed for:

- **Keywords**: Important terms extracted from content
- **Entities**: Named entities (people, companies, organizations)
- **Topics**: Automatic topic classification
- **Sentiment**: Emotional tone of the content
- **Readability**: How easy the content is to read

## Dashboard Usage

### News Dashboard (`/news-dashboard/`)

- View AI-highlighted articles
- See recent news by category
- Monitor source health
- Quick post creation from articles

### News Highlights (`/news-highlights/`)

- Browse all highlighted articles
- Filter by category and score
- See AI analysis details
- Create posts from highlights

### News Sources (`/news-sources/`)

- Monitor source health and performance
- Enable/disable sources
- View fetch statistics
- Access admin configuration

### Account Categories (`/account-categories/`)

- Map categories to social accounts
- Configure auto-posting settings
- View account performance

## Automation & Scheduling

For production use, schedule the news pipeline to run regularly:

### Linux/Mac (Cron)
```bash
# Run every hour
0 * * * * cd /path/to/imagsta && python manage.py news_pipeline

# Run every 30 minutes during business hours
*/30 9-17 * * * cd /path/to/imagsta && python manage.py news_pipeline --max-posts 3
```

### Windows (Task Scheduler)
Create a scheduled task to run:
```cmd
cd C:\path\to\imagsta && python manage.py news_pipeline
```

## Troubleshooting

### Common Issues

1. **No articles fetched**
   - Check source URLs are valid
   - Verify network connectivity
   - Check source health in admin panel

2. **No highlights created**
   - Lower the minimum highlight score
   - Check if articles have content
   - Verify AI analysis is working

3. **Auto-posting not working**
   - Ensure accounts are connected and active
   - Check account-category mappings
   - Verify auto-posting is enabled

### Logs

Check Django logs for detailed error information:
```bash
tail -f imagsta/logs/django.log
```

## API Integration

### NewsAPI.org Setup

1. Get API key from https://newsapi.org/
2. Add to environment: `NEWSAPI_KEY=your_key_here`
3. Create NewsAPI source in admin panel

### OpenAI Integration

For enhanced AI analysis:

1. Get API key from OpenAI
2. Add to environment: `OPENAI_API_KEY=your_key_here`
3. AI will automatically use OpenAI for better analysis

## Performance Optimization

### Database Indexes

The system includes optimized database indexes for:
- Article queries by date and category
- Highlight score lookups
- Source health monitoring

### Caching

Consider implementing Redis caching for:
- Dashboard data
- Category listings
- Source statistics

### Background Tasks

For high-volume deployments, consider using Celery for:
- News fetching
- AI analysis
- Auto-posting

## Security Considerations

- Store API keys in environment variables
- Use HTTPS for all external API calls
- Implement rate limiting for news sources
- Validate all user inputs
- Regular security updates

## Support

For issues or questions:
1. Check the Django admin logs
2. Review the management command output
3. Check source health in the dashboard
4. Verify account connections and permissions
