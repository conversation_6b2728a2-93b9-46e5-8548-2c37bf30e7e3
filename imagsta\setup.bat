@echo off
echo ========================================
echo IMAGSTA NEWS AGGREGATION SYSTEM SETUP
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python is installed
echo.

REM Create virtual environment
echo 🔄 Creating virtual environment...
if not exist "venv" (
    python -m venv venv
    echo ✅ Virtual environment created
) else (
    echo ✅ Virtual environment already exists
)
echo.

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install requirements
echo 🔄 Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)
echo ✅ Requirements installed
echo.

REM Run deployment script
echo 🔄 Running deployment script...
python deploy.py
if errorlevel 1 (
    echo ❌ Deployment script failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 SETUP COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo To start the server:
echo 1. venv\Scripts\activate
echo 2. python manage.py runserver
echo.
echo Then visit: http://localhost:8000/news-dashboard/
echo.
pause
