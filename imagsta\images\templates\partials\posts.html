
  
<div class="album py-4 my-4 bg-dark text-white container" >
    <div class="container">

    <h1 class="display-5 fw-bold text-center text-light pt-3">Posts</h1>

    {% include 'partials/hashtags.html' %}
      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-5 mt-1">
        {% if posts %}
            {% for i in posts %}
                {% if i.posted %}
                    <div class="col">
                        <div class="card bg-danger">
                            {% comment %} <svg class="bd-placeholder-img card-img-top" width="100%" height="300" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Placeholder: Thumbnail" preserveAspectRatio="xMidYMid slice" focusable="false"><title>Placeholder</title><rect width="100%" height="100%" fill="#55595c"/><text x="40%" y="50%" fill="#eceeef" dy=".2em">Thumbnail</text></svg> {% endcomment %}
                            <img src="{{i.image.url}}" alt="">
                            <div class="card-body">
                                <div class="d-flex justify-content-center align-items-center">
                                    <div id="card" class="btn-group"  role="group">
                                        <form action="{{i.image.url}}" target="_blank" method="get">
                                            <button type="submit" class="btn btn-outline-dark btn-dark text-white p-2">view</button>
                                        </form>
                                        <button 
                                        type="button"
                                        hx-post="{% url 'upload-post' %}"
                                        hx-vals='{"id": "{{i.pk}}","image": "{{i.image.url}}", "hashtags": "#instagram #instagood #love #like #follow #photography #photooftheday #instadaily #likeforlikes #picoftheday #fashion #instalike #beautiful #bhfyp #followforfollowback #likes #art #me #photo #followme #smile #happy #style #nature #myself #insta #life #likeforfollow #india #bhfyp"}'
                                        class="btn btn-dark text-white p-2 disabled"
                                        hx-indicator="#spinner{{forloop.counter}}"
                                        
                                        >
                                            Posted!
                                        </button>

                                        <button type="button" 
                                            hx-delete="{% url 'delete-post' i.pk %}"   
                                            hx-confirm="Are you sure you want to delete this task?"
                                            hx-target="div.container"
                                            hx-swap="innerHtml"
                                            class="btn btn-outline-dark btn-dark text-white p-2"
                                            >
                                            Delete
                                        </button>
                                    </div>
                                    <div id="spinner{{forloop.counter}}" class="loader loader--style8 text-center m-1 htmx-indicat" >
                                        <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                        width="40px" height="40px" viewBox="0 0 24 30" xml:space="preserve" class="htmx-indicator">
                                        <rect x="0" y="10" width="4" height="10" fill="white" opacity="0.5">
                                            <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0s" dur="0.6s" repeatCount="indefinite" />
                                        </rect>
                                        <rect x="8" y="10" width="4" height="10" fill="white"  opacity="0.2">
                                            <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0.15s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0.15s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0.15s" dur="0.6s" repeatCount="indefinite" />
                                        </rect>
                                        <rect x="16" y="10" width="4" height="10" fill="white"  opacity="0.3">
                                            <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0.3s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0.3s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0.3s" dur="0.6s" repeatCount="indefinite" />
                                        </rect>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            
                        </div>

                    </div>
                {% else %}
                    <div class="col">
                        <div class="card">
                            {% comment %} <svg class="bd-placeholder-img card-img-top" width="100%" height="300" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Placeholder: Thumbnail" preserveAspectRatio="xMidYMid slice" focusable="false"><title>Placeholder</title><rect width="100%" height="100%" fill="#55595c"/><text x="40%" y="50%" fill="#eceeef" dy=".2em">Thumbnail</text></svg> {% endcomment %}
                            <img src="{{i.image.url}}" alt="">
                            <div class="card-body">
                                <div class="d-flex justify-content-center align-items-center">
                                    <div id="card" class="btn-group"  role="group">
                                        <form action="{{i.image.url}}" target="_blank" method="get">
                                            <button type="submit" class="btn btn-outline-dark btn-dark text-white p-2">view</button>
                                        </form>
                                        <button 
                                        type="button"
                                        hx-post="{% url 'upload-post' %}"
                                        hx-vals='{"id": "{{i.pk}}", "image": "{{i.image.url}}", "hashtags": "#instagram #instagood #love #like #follow #photography #photooftheday #instadaily #likeforlikes #picoftheday #fashion #instalike #beautiful #bhfyp #followforfollowback #likes #art #me #photo #followme #smile #happy #style #nature #myself #insta #life #likeforfollow #india #bhfyp"}'
                                        class="btn btn-outline-dark btn-dark text-white p-2"
                                        hx-indicator="#spinner{{forloop.counter}}"  
                                        hx-target=".album"
                                        hx-swap="outerHTML"
                                        >
                                            Post
                                        </button>

                                        <button type="button" 
                                            hx-delete="{% url 'delete-post' i.pk %}"   
                                            hx-confirm="Are you sure you want to delete this task?"
                                            hx-target="div.container"
                                            hx-swap="innerHtml"
                                            class="btn btn-outline-dark btn-dark text-white p-2"
                                            >
                                            Delete
                                        </button>
                                    </div>
                                    <div id="spinner{{forloop.counter}}" class="loader loader--style8 text-center m-1 htmx-indicat" >
                                        <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                        width="40px" height="40px" viewBox="0 0 24 30" xml:space="preserve" class="htmx-indicator">
                                        <rect x="0" y="10" width="4" height="10" fill="white" opacity="0.5">
                                            <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0s" dur="0.6s" repeatCount="indefinite" />
                                        </rect>
                                        <rect x="8" y="10" width="4" height="10" fill="white"  opacity="0.2">
                                            <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0.15s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0.15s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0.15s" dur="0.6s" repeatCount="indefinite" />
                                        </rect>
                                        <rect x="16" y="10" width="4" height="10" fill="white"  opacity="0.3">
                                            <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0.3s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0.3s" dur="0.6s" repeatCount="indefinite" />
                                            <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0.3s" dur="0.6s" repeatCount="indefinite" />
                                        </rect>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            
                        </div>

                    </div>
                {% endif %}
            {% endfor %}
        {% else %}
            {% for i in "x"|ljust:"10" %}
                <div class="col">
                    <div class="card">
                        {% comment %} <svg class="bd-placeholder-img card-img-top" width="100%" height="300" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Placeholder: Thumbnail" preserveAspectRatio="xMidYMid slice" focusable="false"><title>Placeholder</title><rect width="100%" height="100%" fill="#55595c"/><text x="40%" y="50%" fill="#eceeef" dy=".2em">Thumbnail</text></svg> {% endcomment %}
                        <img src="https://images.unsplash.com/photo-1610103249112-b540391c2ef2?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8cmFufGVufDB8fDB8fA%3D%3D&w=1000&q=80" alt="">
                        <div class="card-body">
                            <div class="d-flex justify-content-center align-items-center">
                                <div class="btn-group">
                                <button type="button" class="btn btn-md btn-outline-secondary">View</button>
                                <button type="button" class="btn btn-md btn-dark">Edit</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            {% endfor %}
        {% endif %}
      </div>
    </div>
</div>

