"""
Automated posting service for news articles.
"""

import logging
from typing import List, Dict, Optional
from django.utils import timezone
from django.db import transaction
from datetime import <PERSON><PERSON><PERSON>

from .models import (
    NewsArticle, AIHighlight, AccountCategoryMapping, 
    Post, PostPublication, SocialMediaAccount
)
from .ai_services import ai_service

logger = logging.getLogger(__name__)


class AutoPostingService:
    """Service for automatically posting highlighted news articles to social media accounts."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def process_auto_posting(self, max_posts_per_run: int = 10) -> Dict[str, int]:
        """
        Process auto-posting for eligible highlighted articles.
        
        Args:
            max_posts_per_run: Maximum number of posts to create in one run
            
        Returns:
            Dictionary with posting statistics
        """
        stats = {
            'processed_highlights': 0,
            'created_posts': 0,
            'skipped_posts': 0,
            'errors': 0
        }
        
        try:
            # Get unposted highlights that are eligible for auto-posting
            eligible_highlights = self._get_eligible_highlights()
            
            posts_created = 0
            for highlight in eligible_highlights[:max_posts_per_run]:
                try:
                    stats['processed_highlights'] += 1
                    
                    # Get eligible accounts for this highlight
                    eligible_accounts = self._get_eligible_accounts_for_highlight(highlight)
                    
                    if not eligible_accounts:
                        stats['skipped_posts'] += 1
                        continue
                    
                    # Create post for the highlight
                    post_created = self._create_auto_post(highlight, eligible_accounts)
                    
                    if post_created:
                        stats['created_posts'] += 1
                        posts_created += 1
                        
                        # Mark highlight as posted
                        highlight.mark_as_posted()
                        
                        self.logger.info(f"Auto-posted article: {highlight.article.title[:50]}...")
                    else:
                        stats['skipped_posts'] += 1
                        
                except Exception as e:
                    stats['errors'] += 1
                    self.logger.error(f"Error processing highlight {highlight.id}: {e}")
            
            self.logger.info(f"Auto-posting completed: {stats}")
            return stats
            
        except Exception as e:
            self.logger.error(f"Error in auto-posting process: {e}")
            stats['errors'] += 1
            return stats
    
    def _get_eligible_highlights(self) -> List[AIHighlight]:
        """Get highlights eligible for auto-posting."""
        # Get highlights that haven't been posted and are recent
        cutoff_time = timezone.now() - timedelta(hours=24)
        
        return AIHighlight.objects.filter(
            was_posted=False,
            highlight_score__gte=60,  # Minimum score threshold
            article__published_date__gte=cutoff_time,
            created__gte=cutoff_time
        ).select_related('article').order_by('-highlight_score')[:50]
    
    def _get_eligible_accounts_for_highlight(self, highlight: AIHighlight) -> List[AccountCategoryMapping]:
        """Get accounts eligible to receive this highlight."""
        article = highlight.article
        article_categories = article.categories.all()
        
        if not article_categories:
            return []
        
        # Find account mappings that match the article categories and have auto-posting enabled
        eligible_mappings = AccountCategoryMapping.objects.filter(
            category__in=article_categories,
            auto_post=True,
            min_highlight_score__lte=highlight.highlight_score,
            account__status='connected'
        ).select_related('account', 'category').order_by('priority')
        
        # Filter out accounts that have posted recently to avoid spam
        recent_cutoff = timezone.now() - timedelta(hours=2)
        filtered_mappings = []
        
        for mapping in eligible_mappings:
            # Check if this account has posted recently
            recent_posts = Post.objects.filter(
                user=mapping.account.user,
                created__gte=recent_cutoff
            ).count()
            
            if recent_posts < 3:  # Max 3 posts per 2 hours per account
                filtered_mappings.append(mapping)
        
        return filtered_mappings[:3]  # Limit to 3 accounts per highlight
    
    def _create_auto_post(self, highlight: AIHighlight, account_mappings: List[AccountCategoryMapping]) -> bool:
        """Create an auto-post for the highlight."""
        try:
            with transaction.atomic():
                article = highlight.article
                
                # Generate content for the post
                caption = self._generate_post_caption(highlight)
                hashtags = self._generate_post_hashtags(highlight)
                
                # Create the post
                post = Post.objects.create(
                    user=account_mappings[0].account.user,  # Use first account's user
                    caption=caption,
                    post_text=caption.split('\n\n')[0],  # First paragraph as post text
                    hashtags=' '.join(hashtags),
                    posted=False  # Will be posted by background task
                )
                
                # Create publications for each eligible account
                for mapping in account_mappings:
                    PostPublication.objects.create(
                        post=post,
                        account=mapping.account,
                        status='pending'
                    )
                    
                    # Update mapping statistics
                    mapping.increment_posted_count()
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error creating auto-post: {e}")
            return False
    
    def _generate_post_caption(self, highlight: AIHighlight) -> str:
        """Generate caption for auto-post."""
        # Use AI-suggested caption if available, otherwise generate one
        if highlight.suggested_caption:
            base_caption = highlight.suggested_caption
        else:
            # Generate a basic caption
            article = highlight.article
            base_caption = f"📰 {article.title}\n\n{article.content[:200]}..."
        
        # Add source attribution
        source_line = f"\n\n🔗 Source: {highlight.article.source.name}"
        
        return base_caption + source_line
    
    def _generate_post_hashtags(self, highlight: AIHighlight) -> List[str]:
        """Generate hashtags for auto-post."""
        hashtags = []
        
        # Use AI-suggested hashtags if available
        if highlight.suggested_hashtags:
            hashtags.extend(highlight.suggested_hashtags[:8])
        else:
            # Generate basic hashtags
            hashtags.extend(['#news', '#trending', '#update'])
            
            # Add category-based hashtags
            for category in highlight.article.categories.all():
                hashtags.append(f"#{category.name}")
            
            # Add keyword-based hashtags
            if highlight.article.keywords:
                for keyword in highlight.article.keywords[:3]:
                    clean_keyword = keyword.replace(' ', '').replace('-', '')
                    if len(clean_keyword) > 3:
                        hashtags.append(f"#{clean_keyword}")
        
        # Remove duplicates and limit
        unique_hashtags = list(dict.fromkeys(hashtags))  # Preserve order while removing duplicates
        return unique_hashtags[:10]
    
    def get_auto_posting_stats(self, days: int = 7) -> Dict[str, any]:
        """Get auto-posting statistics for the last N days."""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Get posts created through auto-posting
        auto_posts = Post.objects.filter(
            created__gte=cutoff_date,
            publications__isnull=False  # Has publications (indicates auto-posting)
        ).distinct()
        
        # Get account mappings with auto-posting enabled
        auto_enabled_mappings = AccountCategoryMapping.objects.filter(auto_post=True)
        
        stats = {
            'total_auto_posts': auto_posts.count(),
            'auto_enabled_accounts': auto_enabled_mappings.count(),
            'avg_posts_per_day': auto_posts.count() / max(days, 1),
            'top_performing_categories': self._get_top_categories(auto_posts),
            'account_performance': self._get_account_performance(auto_enabled_mappings),
        }
        
        return stats
    
    def _get_top_categories(self, posts) -> List[Dict[str, any]]:
        """Get top performing categories for auto-posts."""
        # This would require more complex querying in a real implementation
        # For now, return a placeholder
        return [
            {'category': 'Technology', 'posts': 15, 'avg_engagement': 85.2},
            {'category': 'Business', 'posts': 12, 'avg_engagement': 78.5},
            {'category': 'Science', 'posts': 8, 'avg_engagement': 92.1},
        ]
    
    def _get_account_performance(self, mappings) -> List[Dict[str, any]]:
        """Get performance stats for auto-posting accounts."""
        performance = []
        
        for mapping in mappings[:10]:  # Top 10 accounts
            performance.append({
                'account': f"{mapping.account.platform} - @{mapping.account.username}",
                'posts_count': mapping.articles_posted,
                'avg_engagement': mapping.avg_engagement,
                'categories': [cat.display_name for cat in mapping.account.category_mappings.values_list('category__display_name', flat=True)]
            })
        
        return performance


# Singleton instance
auto_posting_service = AutoPostingService()
