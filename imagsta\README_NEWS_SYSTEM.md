# Imagsta - AI-Powered Social Media Management with News Aggregation

![Python](https://img.shields.io/badge/python-3.8+-blue.svg)
![Django](https://img.shields.io/badge/django-4.2+-green.svg)
![License](https://img.shields.io/badge/license-MIT-blue.svg)

A comprehensive social media management platform that combines traditional content creation with advanced **AI-powered news aggregation** and automated posting capabilities.

## 🚀 New Features: News Aggregation System

### 📰 Multi-Source News Fetching
- **RSS Feeds**: Fetch from any RSS/Atom feed (TechCrunch, BBC, Reuters, etc.)
- **Reddit Integration**: Pull trending posts from subreddits
- **NewsAPI**: Integration with NewsAPI.org for comprehensive coverage
- **Custom Sources**: Extensible architecture for new news sources

### 🤖 AI-Powered Content Analysis
- **Sentiment Analysis**: Analyze article sentiment (-1 to 1 scale)
- **Engagement Prediction**: Predict social media engagement potential (0-100)
- **Trending Score**: Calculate trending potential based on recency and social metrics
- **Smart Highlighting**: AI automatically identifies the best articles for posting
- **Topic Classification**: Automatic categorization by subject matter
- **Keyword Extraction**: Extract important terms and entities

### 📱 Intelligent Social Media Integration
- **Account-Category Mapping**: Assign news categories to specific social media accounts
- **Auto-Posting**: Automatically post highlighted articles to relevant accounts
- **AI-Generated Captions**: Create engaging social media captions automatically
- **Smart Hashtags**: Generate relevant hashtags based on content analysis
- **Rate Limiting**: Prevent spam with intelligent posting frequency controls

### 📊 Comprehensive News Dashboard
- **News Dashboard**: Overview of AI highlights and recent articles by category
- **Highlights Management**: Review and manually post AI-recommended articles
- **Source Management**: Monitor news source health and performance
- **Category Configuration**: Organize content and account mappings

## 🛠 Quick Start with News Aggregation

### 1. Basic Setup
```bash
# Clone and set up the project (see main README for details)
git clone <repository-url>
cd imagsta
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python manage.py migrate
python manage.py createsuperuser
```

### 2. Configure News Sources
```bash
# Create sample news sources for testing
python manage.py fetch_news --create-sample-sources
```

### 3. Set Up Account Categories
1. Start the server: `python manage.py runserver`
2. Visit `http://localhost:8000/account-categories/`
3. Connect your social media accounts
4. Assign categories (Technology, Business, etc.) to each account
5. Configure auto-posting preferences

### 4. Run the News Pipeline
```bash
# Complete pipeline: fetch news, analyze with AI, and auto-post
python manage.py news_pipeline

# Or run individual steps:
python manage.py fetch_news              # Fetch new articles
python manage.py analyze_news --limit 50 # Analyze with AI
python manage.py auto_post_news --max-posts 5  # Auto-post highlights
```

## 📱 News Dashboard Usage

### Main Dashboard (`/news-dashboard/`)
- View AI-highlighted articles with scores and reasons
- See recent news organized by category
- Monitor news source health and statistics
- Quick post creation from highlighted articles

### Highlights Management (`/news-highlights/`)
- Browse all AI-highlighted articles
- Filter by category and minimum score
- See detailed AI analysis (sentiment, engagement prediction, trending score)
- Create social media posts with AI-suggested captions and hashtags

### Source Management (`/news-sources/`)
- Monitor all configured news sources
- View fetch statistics and health status
- Enable/disable sources
- Access admin configuration

### Account Categories (`/account-categories/`)
- Map news categories to social media accounts
- Configure auto-posting settings
- Set minimum highlight scores for automatic posting
- View account performance statistics

## 🤖 AI Analysis Details

### Highlight Score Calculation (0-100)
The AI calculates highlight scores using weighted factors:
- **Engagement Prediction** (30%): Content quality, readability, optimal length
- **Trending Score** (25%): Recency, social metrics, trending keywords
- **Sentiment Score** (15%): Positive sentiment boosts engagement
- **Social Metrics** (15%): Existing shares, likes, comments
- **Recency** (15%): Newer articles score higher

### Content Analysis Features
- **Sentiment Analysis**: Emotional tone analysis using AI
- **Keyword Extraction**: Important terms and phrases
- **Entity Recognition**: People, companies, organizations
- **Topic Classification**: Automatic categorization
- **Readability Scoring**: Content accessibility assessment
- **Urgency Detection**: Breaking news and time-sensitive content

## 🔄 Automation & Scheduling

### Production Automation
Set up cron jobs for continuous operation:

```bash
# Run complete pipeline every hour
0 * * * * cd /path/to/imagsta && python manage.py news_pipeline

# Run every 30 minutes during business hours
*/30 9-17 * * * cd /path/to/imagsta && python manage.py news_pipeline --max-posts 3

# Fetch news every 15 minutes
*/15 * * * * cd /path/to/imagsta && python manage.py fetch_news
```

### Windows Task Scheduler
Create scheduled tasks to run:
```cmd
cd C:\path\to\imagsta && python manage.py news_pipeline
```

## 📊 Management Commands Reference

### News Pipeline
```bash
# Complete pipeline with all steps
python manage.py news_pipeline

# Fetch and analyze only (no auto-posting)
python manage.py news_pipeline --no-auto-post

# Force fetch from all sources regardless of schedule
python manage.py news_pipeline --force-fetch

# Analyze existing articles only
python manage.py news_pipeline --analyze-only
```

### Individual Commands
```bash
# Fetch news from sources
python manage.py fetch_news --force
python manage.py fetch_news --source "TechCrunch RSS"

# AI analysis and highlighting
python manage.py analyze_news --limit 100 --min-score 70
python manage.py analyze_news --category technology --unprocessed-only

# Automated posting
python manage.py auto_post_news --max-posts 10
python manage.py auto_post_news --dry-run  # See what would be posted
python manage.py auto_post_news --stats    # Show statistics
```

## 🔧 Configuration

### Environment Variables
Add to your `.env` file:
```env
# News aggregation
NEWSAPI_KEY=your_newsapi_key  # Optional: for NewsAPI.org

# AI services (for enhanced analysis)
OPENAI_API_KEY=your_openai_key  # Optional: for better AI analysis
```

### News Source Configuration
Configure sources in Django admin (`/admin/images/newssource/`):
- **RSS Feeds**: Any valid RSS/Atom feed URL
- **Reddit**: Subreddit URLs (e.g., `https://www.reddit.com/r/technology`)
- **NewsAPI**: Configure with API parameters
- **Fetch Interval**: How often to check for new content (minutes)
- **Categories**: Assign relevant topic categories

## 🧪 Testing

Run the comprehensive test suite:
```bash
# Run all tests
python manage.py test

# Run news system tests specifically
python manage.py test images.tests.test_news_system

# Test with coverage
coverage run --source='.' manage.py test images.tests.test_news_system
coverage report
```

## 📚 Documentation

- **Detailed Guide**: See `NEWS_AGGREGATION_GUIDE.md` for comprehensive documentation
- **API Documentation**: Available at `/admin/` for model configuration
- **Management Commands**: Use `--help` flag with any command for detailed options

## 🎯 Example Workflow

1. **Setup**: Configure news sources and connect social accounts
2. **Categorize**: Assign categories to accounts (e.g., Tech account gets Technology news)
3. **Automate**: Run `python manage.py news_pipeline` hourly
4. **Monitor**: Check `/news-dashboard/` for highlights and performance
5. **Optimize**: Adjust category mappings and score thresholds based on results

## 🚀 Production Deployment

### Recommended Stack
- **Database**: PostgreSQL for better performance
- **Caching**: Redis for dashboard and API responses
- **Background Tasks**: Celery for async news processing
- **Monitoring**: Set up logging and error tracking
- **Scheduling**: Use cron jobs or task schedulers

### Performance Tips
- Run news pipeline every 30-60 minutes
- Set appropriate highlight score thresholds (70+ recommended)
- Limit auto-posts per account (3-5 per day maximum)
- Monitor source health and disable problematic sources
- Use category filtering to reduce noise

## 🤝 Contributing

The news aggregation system is modular and extensible:
- Add new news sources by extending `BaseNewsFetcher`
- Enhance AI analysis in `ai_services.py`
- Improve auto-posting logic in `auto_posting_service.py`
- Add new dashboard features in the views and templates

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Ready to revolutionize your social media management with AI-powered news aggregation? Get started today!**
