{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-cloud-upload"></i> Bulk Upload & Schedule
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'scheduler' %}" class="btn btn-outline-primary">
                        <i class="bi bi-calendar-week"></i> Advanced Scheduler
                    </a>
                    <a href="{% url 'templates' %}" class="btn btn-outline-info">
                        <i class="bi bi-file-text"></i> Templates
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-images"></i> Upload Multiple Posts
                    </h5>
                    <small class="text-muted">Upload up to {{ max_files }} images at once</small>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="bulkUploadForm">
                        {% csrf_token %}
                        
                        <!-- File Upload Area -->
                        <div class="mb-4">
                            <label for="images" class="form-label fw-bold">Select Images</label>
                            <div class="upload-area" id="uploadArea">
                                <input type="file" class="form-control" id="images" name="images" 
                                       multiple accept="image/*" style="display: none;">
                                <div class="upload-placeholder text-center py-5">
                                    <i class="bi bi-cloud-upload display-1 text-muted"></i>
                                    <h5 class="mt-3">Drag & Drop Images Here</h5>
                                    <p class="text-muted">or click to browse files</p>
                                    <button type="button" class="btn btn-primary" onclick="document.getElementById('images').click()">
                                        <i class="bi bi-folder2-open"></i> Browse Files
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Area -->
                        <div id="previewArea" class="mb-4" style="display: none;">
                            <h6 class="fw-bold">Selected Images</h6>
                            <div id="imagePreviewContainer" class="row">
                                <!-- Image previews will be added here -->
                            </div>
                        </div>

                        <!-- Bulk Actions -->
                        <div class="mb-4" id="bulkActions" style="display: none;">
                            <h6 class="fw-bold">Bulk Actions</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="bulkCaption" class="form-label">Apply Caption to All</label>
                                    <textarea class="form-control" id="bulkCaption" rows="3" 
                                              placeholder="This caption will be applied to all images"></textarea>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="bulkHashtags" class="form-label">Apply Hashtags to All</label>
                                    <input type="text" class="form-control" id="bulkHashtags" 
                                           placeholder="#hashtag1 #hashtag2 #hashtag3">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <button type="button" class="btn btn-outline-primary" id="applyCaptionBtn">
                                        <i class="bi bi-arrow-down"></i> Apply Caption to All
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <button type="button" class="btn btn-outline-primary" id="applyHashtagsBtn">
                                        <i class="bi bi-arrow-down"></i> Apply Hashtags to All
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Auto-Schedule Options -->
                        <div class="mb-4" id="scheduleOptions" style="display: none;">
                            <h6 class="fw-bold">Auto-Schedule Options</h6>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="scheduleType" class="form-label">Schedule Type</label>
                                    <select class="form-select" id="scheduleType">
                                        <option value="manual">Manual (No Auto-Schedule)</option>
                                        <option value="optimal">Optimal Times</option>
                                        <option value="interval">Fixed Intervals</option>
                                        <option value="custom">Custom Schedule</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3" id="intervalOptions" style="display: none;">
                                    <label for="postInterval" class="form-label">Post Every</label>
                                    <select class="form-select" id="postInterval">
                                        <option value="1">1 Hour</option>
                                        <option value="2">2 Hours</option>
                                        <option value="4">4 Hours</option>
                                        <option value="6">6 Hours</option>
                                        <option value="12">12 Hours</option>
                                        <option value="24">24 Hours</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="startDate" class="form-label">Start Date</label>
                                    <input type="datetime-local" class="form-control" id="startDate">
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                <i class="bi bi-cloud-upload"></i> Upload Posts
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tips Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="fw-bold">
                        <i class="bi bi-lightbulb"></i> Pro Tips for Bulk Upload
                    </h6>
                    <div class="row">
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> Use consistent image sizes</li>
                                <li><i class="bi bi-check-circle text-success"></i> Optimize images before upload</li>
                                <li><i class="bi bi-check-circle text-success"></i> Plan your content calendar</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> Use relevant hashtags</li>
                                <li><i class="bi bi-check-circle text-success"></i> Schedule for optimal times</li>
                                <li><i class="bi bi-check-circle text-success"></i> Maintain brand consistency</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> Preview before publishing</li>
                                <li><i class="bi bi-check-circle text-success"></i> Track performance metrics</li>
                                <li><i class="bi bi-check-circle text-success"></i> Engage with your audience</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('images');
    const previewArea = document.getElementById('previewArea');
    const previewContainer = document.getElementById('imagePreviewContainer');
    const bulkActions = document.getElementById('bulkActions');
    const scheduleOptions = document.getElementById('scheduleOptions');
    const submitBtn = document.getElementById('submitBtn');
    const scheduleType = document.getElementById('scheduleType');
    const intervalOptions = document.getElementById('intervalOptions');
    
    let selectedFiles = [];
    
    // Drag and drop functionality
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('drag-over');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('drag-over');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        handleFiles(files);
    });
    
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    fileInput.addEventListener('change', function() {
        const files = Array.from(this.files);
        handleFiles(files);
    });
    
    function handleFiles(files) {
        if (files.length > {{ max_files }}) {
            alert(`Maximum {{ max_files }} files allowed`);
            return;
        }
        
        selectedFiles = files;
        displayPreviews();
        
        if (files.length > 0) {
            previewArea.style.display = 'block';
            bulkActions.style.display = 'block';
            scheduleOptions.style.display = 'block';
            submitBtn.disabled = false;
        }
    }
    
    function displayPreviews() {
        previewContainer.innerHTML = '';
        
        selectedFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewHtml = `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card">
                            <img src="${e.target.result}" class="card-img-top" style="height: 200px; object-fit: cover;">
                            <div class="card-body p-2">
                                <input type="text" class="form-control form-control-sm mb-2" 
                                       name="captions" placeholder="Caption for this image">
                                <input type="text" class="form-control form-control-sm mb-2" 
                                       name="hashtags" placeholder="#hashtags">
                                <input type="datetime-local" class="form-control form-control-sm" 
                                       name="schedule_times">
                                <button type="button" class="btn btn-sm btn-outline-danger mt-2 remove-image" 
                                        data-index="${index}">
                                    <i class="bi bi-trash"></i> Remove
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                previewContainer.innerHTML += previewHtml;
            };
            reader.readAsDataURL(file);
        });
    }
    
    // Remove image functionality
    previewContainer.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-image') || e.target.closest('.remove-image')) {
            const index = parseInt(e.target.dataset.index || e.target.closest('.remove-image').dataset.index);
            selectedFiles.splice(index, 1);
            
            // Update file input
            const dt = new DataTransfer();
            selectedFiles.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;
            
            displayPreviews();
            
            if (selectedFiles.length === 0) {
                previewArea.style.display = 'none';
                bulkActions.style.display = 'none';
                scheduleOptions.style.display = 'none';
                submitBtn.disabled = true;
            }
        }
    });
    
    // Bulk caption application
    document.getElementById('applyCaptionBtn').addEventListener('click', function() {
        const bulkCaption = document.getElementById('bulkCaption').value;
        document.querySelectorAll('input[name="captions"]').forEach(input => {
            input.value = bulkCaption;
        });
    });
    
    // Bulk hashtags application
    document.getElementById('applyHashtagsBtn').addEventListener('click', function() {
        const bulkHashtags = document.getElementById('bulkHashtags').value;
        document.querySelectorAll('input[name="hashtags"]').forEach(input => {
            input.value = bulkHashtags;
        });
    });
    
    // Schedule type change
    scheduleType.addEventListener('change', function() {
        if (this.value === 'interval') {
            intervalOptions.style.display = 'block';
        } else {
            intervalOptions.style.display = 'none';
        }
        
        // Auto-generate schedule times based on selection
        if (this.value === 'optimal') {
            generateOptimalSchedule();
        } else if (this.value === 'interval') {
            generateIntervalSchedule();
        }
    });
    
    function generateOptimalSchedule() {
        const optimalTimes = ['09:00', '14:00', '11:00', '15:00', '13:00', '10:00', '16:00'];
        const scheduleInputs = document.querySelectorAll('input[name="schedule_times"]');
        
        scheduleInputs.forEach((input, index) => {
            const date = new Date();
            date.setDate(date.getDate() + index);
            const timeIndex = index % optimalTimes.length;
            const time = optimalTimes[timeIndex];
            
            const dateStr = date.toISOString().split('T')[0];
            input.value = `${dateStr}T${time}`;
        });
    }
    
    function generateIntervalSchedule() {
        const interval = parseInt(document.getElementById('postInterval').value);
        const startDate = new Date(document.getElementById('startDate').value || new Date());
        const scheduleInputs = document.querySelectorAll('input[name="schedule_times"]');
        
        scheduleInputs.forEach((input, index) => {
            const scheduleDate = new Date(startDate);
            scheduleDate.setHours(scheduleDate.getHours() + (interval * index));
            input.value = scheduleDate.toISOString().slice(0, 16);
        });
    }
    
    // Set default start date to now
    document.getElementById('startDate').value = new Date().toISOString().slice(0, 16);
});
</script>

<style>
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.card-img-top {
    transition: transform 0.2s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.remove-image {
    width: 100%;
}

.list-unstyled li {
    margin-bottom: 0.5rem;
}

.bg-light {
    background-color: rgba(248, 249, 250, 0.8) !important;
}
</style>
{% endblock %}
