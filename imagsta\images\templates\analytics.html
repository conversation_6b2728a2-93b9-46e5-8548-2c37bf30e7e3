{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-light">
                    <i class="bi bi-graph-up"></i> Analytics Dashboard
                </h1>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary">Last 7 Days</button>
                    <button type="button" class="btn btn-outline-primary active">Last 30 Days</button>
                    <button type="button" class="btn btn-outline-primary">Last 90 Days</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Posts</h6>
                            <h2 class="mb-0">{{ total_posts }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-collection display-4"></i>
                        </div>
                    </div>
                    <small class="text-light">
                        <i class="bi bi-arrow-up"></i> {{ recent_posts_count }} this month
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Published</h6>
                            <h2 class="mb-0">{{ posted_count }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle display-4"></i>
                        </div>
                    </div>
                    <small class="text-light">
                        {% widthratio posted_count total_posts 100 %}% success rate
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Drafts</h6>
                            <h2 class="mb-0">{{ draft_count }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-pencil-square display-4"></i>
                        </div>
                    </div>
                    <small class="text-light">
                        Ready to publish
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Engagement Rate</h6>
                            <h2 class="mb-0">{{ analytics.avg_engagement_rate }}%</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-heart display-4"></i>
                        </div>
                    </div>
                    <small class="text-light">
                        <i class="bi bi-arrow-up"></i> Above average
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="row mb-4">
        <!-- Posting Frequency Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-bar-chart"></i> Posting Frequency (Last 7 Days)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="postingChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Engagement Overview -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-pie-chart"></i> Engagement Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Likes</span>
                            <strong>{{ analytics.total_likes }}</strong>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-primary" style="width: 70%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Comments</span>
                            <strong>{{ analytics.total_comments }}</strong>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-success" style="width: 25%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Shares</span>
                            <strong>{{ analytics.total_shares }}</strong>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-warning" style="width: 5%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Insights -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-trophy"></i> Top Performing Posts
                    </h5>
                </div>
                <div class="card-body">
                    {% for post in top_posts %}
                    <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                        {% if post.image %}
                        <img src="{{ post.image.url }}" alt="Post" class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                        {% else %}
                        <div class="bg-secondary rounded me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-image text-white"></i>
                        </div>
                        {% endif %}
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ post.caption|truncatechars:50 }}</h6>
                            <small class="text-muted">{{ post.created|date:"M d, Y" }}</small>
                            <div class="mt-1">
                                <span class="badge bg-primary me-1">
                                    <i class="bi bi-heart"></i> 245
                                </span>
                                <span class="badge bg-success me-1">
                                    <i class="bi bi-chat"></i> 18
                                </span>
                                <span class="badge bg-info">
                                    <i class="bi bi-share"></i> 5
                                </span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center py-4">
                        <i class="bi bi-inbox display-4 d-block mb-2"></i>
                        No published posts yet
                    </p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history"></i> Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    {% for post in recent_posts %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            {% if post.status == 'posted' %}
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="bi bi-check text-white"></i>
                                </div>
                            {% elif post.status == 'draft' %}
                                <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="bi bi-pencil text-white"></i>
                                </div>
                            {% elif post.status == 'failed' %}
                                <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="bi bi-x text-white"></i>
                                </div>
                            {% else %}
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="bi bi-clock text-white"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ post.caption|truncatechars:40|default:"Untitled Post" }}</h6>
                            <small class="text-muted">{{ post.created|timesince }} ago</small>
                        </div>
                        <span class="badge bg-{{ post.posted|yesno:'success,warning' }}">
                            {{ post.posted|yesno:'Posted,Draft' }}
                        </span>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center py-4">
                        <i class="bi bi-clock display-4 d-block mb-2"></i>
                        No recent activity
                    </p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Insights and Recommendations -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightbulb"></i> AI Insights & Recommendations
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-primary">
                                    <i class="bi bi-clock"></i> Best Posting Time
                                </h6>
                                <p class="mb-0">Your audience is most active on <strong>Tuesdays at 2:00 PM</strong>. Consider scheduling more posts during this time.</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-success">
                                    <i class="bi bi-hash"></i> Trending Hashtags
                                </h6>
                                <p class="mb-0">Posts with <strong>#TechTrends</strong> and <strong>#Innovation</strong> are performing 40% better this week.</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-info">
                                    <i class="bi bi-image"></i> Content Type
                                </h6>
                                <p class="mb-0">Images with <strong>infographics</strong> get 60% more engagement than regular photos.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for analytics charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Posting frequency chart
const ctx = document.getElementById('postingChart').getContext('2d');
const postingChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: [
            {% for item in posting_frequency %}
                '{{ item.date|date:"M d" }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Posts',
            data: [
                {% for item in posting_frequency %}
                    {{ item.count }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(13, 110, 253, 0.8)',
            borderColor: 'rgba(13, 110, 253, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

<style>
.card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.progress {
    height: 8px;
}

.badge {
    font-size: 0.75em;
}

.bg-light {
    background-color: rgba(248, 249, 250, 0.8) !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.position-fixed {
    position: fixed !important;
}
</style>

<script>
// Advanced analytics functions
function refreshAnalytics() {
    fetch('/analytics/data/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Analytics refreshed successfully!', 'success');
                location.reload(); // Simple refresh for now
            }
        })
        .catch(error => {
            showNotification('Failed to refresh analytics', 'error');
        });
}

function exportAnalytics() {
    window.location.href = '/analytics/export/';
}

function getOptimalSchedule() {
    fetch('/optimize-schedule/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayOptimalSchedule(data);
            }
        })
        .catch(error => {
            showNotification('Failed to get optimal schedule', 'error');
        });
}

function displayOptimalSchedule(data) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🎯 Optimal Posting Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>Analysis based on ${data.posts_analyzed} posts over ${data.analysis_period}</strong>
                    </div>
                    <h6>📊 Key Insights:</h6>
                    <ul>
                        ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                    <h6>📅 Optimal Schedule for Next Week:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Day</th>
                                    <th>Best Time</th>
                                    <th>Expected Engagement</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.optimal_schedule.map(item => `
                                    <tr>
                                        <td>${item.date}</td>
                                        <td>${item.day}</td>
                                        <td><strong>${item.time}</strong></td>
                                        <td><span class="badge bg-success">${item.expected_engagement.toFixed(2)}%</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="applyOptimalSchedule()">
                        <i class="bi bi-calendar-plus"></i> Apply Schedule
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();
}

function showNotification(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.innerHTML = `
        <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : 'check-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 5000);
}

function scheduleOptimalPosts() {
    showNotification('Auto-scheduling feature coming soon!', 'info');
}

function analyzeCompetitors() {
    showNotification('Competitor analysis feature coming soon!', 'info');
}

function generateReport() {
    showNotification('Report generation feature coming soon!', 'info');
}
</script>

{% endblock %}
