from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from images.models import Search, Result, Post, NewsSource, Category, ContentTemplate
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = 'Setup project with initial data and configurations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-superuser',
            action='store_true',
            help='Create a superuser account',
        )
        parser.add_argument(
            '--username',
            type=str,
            default='admin',
            help='Superuser username (default: admin)',
        )
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='Superuser email (default: <EMAIL>)',
        )
        parser.add_argument(
            '--password',
            type=str,
            default='admin123',
            help='Superuser password (default: admin123)',
        )
        parser.add_argument(
            '--sample-data',
            action='store_true',
            help='Create sample data for testing',
        )
        parser.add_argument(
            '--setup-news',
            action='store_true',
            help='Set up news sources and categories',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Setting up Imagsta project...')
        )

        try:
            with transaction.atomic():
                if options['create_superuser']:
                    self.create_superuser(options)
                
                if options['sample_data']:
                    self.create_sample_data()
                
                self.check_configuration()
                
            self.stdout.write(
                self.style.SUCCESS('✅ Project setup completed successfully!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Setup failed: {str(e)}')
            )
            logger.error(f"Project setup failed: {e}")

    def create_superuser(self, options):
        """Create a superuser if it doesn't exist."""
        username = options['username']
        email = options['email']
        password = options['password']
        
        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.WARNING(f'⚠️  Superuser "{username}" already exists')
            )
            return
        
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Superuser "{username}" created successfully')
        )
        self.stdout.write(f'   Email: {email}')
        self.stdout.write(f'   Password: {password}')

    def create_sample_data(self):
        """Create sample data for testing."""
        self.stdout.write('📝 Creating sample data...')
        
        # Create sample searches
        sample_searches = [
            'technology news',
            'business trends',
            'world events',
            'startup innovation',
            'digital marketing'
        ]
        
        for query in sample_searches:
            search, created = Search.objects.get_or_create(query=query)
            if created:
                self.stdout.write(f'   ✅ Created search: {query}')

        self.stdout.write(
            self.style.SUCCESS('✅ Sample data created successfully')
        )

    def check_configuration(self):
        """Check if required configurations are set."""
        self.stdout.write('🔍 Checking configuration...')
        
        from django.conf import settings
        from decouple import config
        
        # Check required environment variables
        required_vars = [
            'SECRET_KEY',
            'UNSPLASH_CLIENT_ID',
            'FACEBOOK_ACCESS_TOKEN',
            'INSTAGRAM_ACCOUNT_ID',
            'IMGUR_CLIENT_ID'
        ]
        
        missing_vars = []
        for var in required_vars:
            value = config(var, default='')
            if not value:
                missing_vars.append(var)
        
        if missing_vars:
            self.stdout.write(
                self.style.WARNING(
                    f'⚠️  Missing environment variables: {", ".join(missing_vars)}'
                )
            )
            self.stdout.write(
                '   Please check your .env file and configure these variables'
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ All required configurations are set')
            )
        
        # Check database connection
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            self.stdout.write(
                self.style.SUCCESS('✅ Database connection successful')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Database connection failed: {e}')
            )
        
        # Check static files
        import os
        static_root = settings.STATIC_ROOT
        if static_root and os.path.exists(static_root):
            self.stdout.write(
                self.style.SUCCESS('✅ Static files directory exists')
            )
        else:
            self.stdout.write(
                self.style.WARNING('⚠️  Run "python manage.py collectstatic" to collect static files')
            )
        
        # Check media directory
        media_root = settings.MEDIA_ROOT
        if not os.path.exists(media_root):
            os.makedirs(media_root, exist_ok=True)
            self.stdout.write(
                self.style.SUCCESS('✅ Media directory created')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ Media directory exists')
            )

        self.stdout.write('\n📋 Next steps:')
        self.stdout.write('   1. Configure your .env file with API keys')
        self.stdout.write('   2. Run: python manage.py runserver')
        self.stdout.write('   3. Visit: http://127.0.0.1:8000')
        self.stdout.write('   4. Admin panel: http://127.0.0.1:8000/admin')
