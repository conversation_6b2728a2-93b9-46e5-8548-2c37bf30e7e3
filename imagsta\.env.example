# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration (for production)
DATABASE_URL=sqlite:///db.sqlite3

# Instagram/Facebook API Configuration
FACEBOOK_ACCESS_TOKEN=your-facebook-access-token
INSTAGRAM_ACCOUNT_ID=your-instagram-account-id
FACEBOOK_GRAPH_VERSION=v18.0

# Unsplash API Configuration
UNSPLASH_CLIENT_ID=your-unsplash-client-id

# Imgur API Configuration
IMGUR_CLIENT_ID=your-imgur-client-id

# Media and Static Files
MEDIA_ROOT=media
STATIC_ROOT=staticfiles

# Email Configuration (optional)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Cache Configuration (optional)
CACHE_URL=redis://localhost:6379/1

# Logging Level
LOG_LEVEL=INFO
