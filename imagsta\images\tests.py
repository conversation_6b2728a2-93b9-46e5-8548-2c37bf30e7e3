import os
import tempfile
from unittest.mock import patch, Mock
from django.test import Test<PERSON>ase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io
from .models import User, Search, Result, Post
from .utils import make_post, getCreds, image_to_url


class UserModelTest(TestCase):
    """Test cases for the User model."""

    def setUp(self):
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'testpass123'
        }

    def test_user_creation(self):
        """Test user creation with email."""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.check_password('testpass123'))

    def test_user_str_representation(self):
        """Test user string representation."""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), 'testuser')


class SearchModelTest(TestCase):
    """Test cases for the Search model."""

    def test_search_creation(self):
        """Test search creation and string representation."""
        search = Search.objects.create(query='test query')
        self.assertEqual(search.query, 'test query')
        self.assertEqual(search.search_count, 1)
        self.assertIn('test query', str(search))

    def test_increment_search_count(self):
        """Test incrementing search count."""
        search = Search.objects.create(query='test query')
        initial_count = search.search_count
        search.increment_search_count()
        self.assertEqual(search.search_count, initial_count + 1)

    def test_unique_query_constraint(self):
        """Test that queries must be unique."""
        Search.objects.create(query='unique query')
        with self.assertRaises(Exception):
            Search.objects.create(query='unique query')


class ResultModelTest(TestCase):
    """Test cases for the Result model."""

    def setUp(self):
        self.search = Search.objects.create(query='test search')

    def test_result_creation(self):
        """Test result creation."""
        result = Result.objects.create(
            query=self.search,
            image='https://example.com/image.jpg',
            dis_image='https://example.com/thumb.jpg',
            image_alt='Test image',
            source='unsplash'
        )
        self.assertEqual(result.query, self.search)
        self.assertEqual(result.source, 'unsplash')
        self.assertIn('test search', str(result))


class PostModelTest(TestCase):
    """Test cases for the Post model."""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create a test image
        image = Image.new('RGB', (100, 100), color='red')
        temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
        image.save(temp_file, format='JPEG')
        temp_file.close()

        with open(temp_file.name, 'rb') as f:
            self.test_image = SimpleUploadedFile(
                name='test_image.jpg',
                content=f.read(),
                content_type='image/jpeg'
            )

        os.unlink(temp_file.name)

    def test_post_creation(self):
        """Test post creation."""
        post = Post.objects.create(
            user=self.user,
            image=self.test_image,
            caption='Test caption',
            post_text='Test post text',
            hashtags='#test #hashtag'
        )
        self.assertEqual(post.user, self.user)
        self.assertEqual(post.status, 'draft')
        self.assertFalse(post.is_posted)

    def test_mark_as_posted(self):
        """Test marking post as posted."""
        post = Post.objects.create(
            user=self.user,
            image=self.test_image
        )
        post.mark_as_posted('instagram_id_123')
        self.assertEqual(post.status, 'posted')
        self.assertEqual(post.published_image_id, 'instagram_id_123')
        self.assertTrue(post.is_posted)

    def test_mark_as_failed(self):
        """Test marking post as failed."""
        post = Post.objects.create(
            user=self.user,
            image=self.test_image
        )
        post.mark_as_failed()
        self.assertEqual(post.status, 'failed')


class UtilsTest(TestCase):
    """Test cases for utility functions."""

    def test_make_post_with_valid_image(self):
        """Test make_post function with valid image."""
        # Create a test image
        image = Image.new('RGB', (500, 500), color='blue')
        result = make_post(image, 'Test text')
        self.assertIsInstance(result, Image.Image)
        self.assertEqual(result.size, (1080, 1080))

    @patch('images.utils.config')
    def test_getCreds_with_valid_config(self, mock_config):
        """Test getCreds function with valid configuration."""
        mock_config.side_effect = lambda key, default='': {
            'FACEBOOK_ACCESS_TOKEN': 'test_token',
            'INSTAGRAM_ACCOUNT_ID': 'test_account_id',
            'FACEBOOK_GRAPH_VERSION': 'v18.0'
        }.get(key, default)

        creds = getCreds()
        self.assertIsNotNone(creds)
        self.assertEqual(creds['access_token'], 'test_token')
        self.assertEqual(creds['instagram_account_id'], 'test_account_id')

    @patch('images.utils.config')
    def test_getCreds_with_missing_config(self, mock_config):
        """Test getCreds function with missing configuration."""
        mock_config.return_value = ''
        creds = getCreds()
        self.assertIsNone(creds)


class ViewsTest(TestCase):
    """Test cases for views."""

    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_index_view(self):
        """Test index view loads correctly."""
        response = self.client.get(reverse('index'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Imgsta')  # Assuming this is in the template

    def test_images_view_requires_login(self):
        """Test that images view requires authentication."""
        response = self.client.get(reverse('images'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_images_view_authenticated(self):
        """Test images view with authenticated user."""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('images'))
        self.assertEqual(response.status_code, 200)

    def test_register_view_get(self):
        """Test register view GET request."""
        response = self.client.get(reverse('register'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'username')

    def test_register_view_post_valid(self):
        """Test register view with valid data."""
        data = {
            'username': 'newuser',
            'password1': 'complexpass123',
            'password2': 'complexpass123'
        }
        response = self.client.post(reverse('register'), data)
        self.assertEqual(response.status_code, 302)  # Redirect after success
        self.assertTrue(User.objects.filter(username='newuser').exists())

    def test_register_view_post_invalid(self):
        """Test register view with invalid data."""
        data = {
            'username': 'newuser',
            'password1': 'pass',
            'password2': 'different'
        }
        response = self.client.post(reverse('register'), data)
        self.assertEqual(response.status_code, 200)  # Stay on form with errors
        self.assertFalse(User.objects.filter(username='newuser').exists())

    @patch('images.views.requests.get')
    @patch('images.views.config')
    def test_search_for_images_success(self, mock_config, mock_requests):
        """Test successful image search."""
        mock_config.return_value = 'test_client_id'
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'results': [
                {
                    'urls': {
                        'full': 'https://example.com/full.jpg',
                        'small': 'https://example.com/small.jpg'
                    },
                    'alt_description': 'Test image'
                }
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_requests.return_value = mock_response

        data = {'search': 'test query', 'feed': 'test feed'}
        response = self.client.post(reverse('search'), data)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(Search.objects.filter(query='test query').exists())

    @patch('images.views.config')
    def test_search_for_images_no_config(self, mock_config):
        """Test image search without API configuration."""
        mock_config.return_value = ''
        data = {'search': 'test query', 'feed': 'test feed'}
        response = self.client.post(reverse('search'), data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'not configured')

    def test_search_for_images_empty_query(self):
        """Test image search with empty query."""
        data = {'search': '', 'feed': 'test feed'}
        response = self.client.post(reverse('search'), data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Please enter a search query')

    def test_check_username_available(self):
        """Test username availability check."""
        data = {'username': 'availableuser'}
        response = self.client.post(reverse('check-username'), data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'available')

    def test_check_username_taken(self):
        """Test username availability check for taken username."""
        data = {'username': 'testuser'}  # This user exists from setUp
        response = self.client.post(reverse('check-username'), data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'already exists')


class APIIntegrationTest(TestCase):
    """Test cases for API integrations."""

    @patch('images.utils.requests.get')
    def test_unsplash_api_timeout(self, mock_get):
        """Test handling of Unsplash API timeout."""
        from requests.exceptions import Timeout
        mock_get.side_effect = Timeout()

        data = {'search': 'test query', 'feed': 'test feed'}
        response = self.client.post(reverse('search'), data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'timed out')

    @patch('images.utils.requests.get')
    def test_unsplash_api_rate_limit(self, mock_get):
        """Test handling of Unsplash API rate limiting."""
        mock_response = Mock()
        mock_response.status_code = 429
        mock_response.raise_for_status.side_effect = Exception("Rate limited")
        mock_get.return_value = mock_response

        data = {'search': 'test query', 'feed': 'test feed'}
        response = self.client.post(reverse('search'), data)
        self.assertEqual(response.status_code, 200)
        # Should handle the error gracefully
