"""
Management command to process scheduled posts.
Run this command periodically (e.g., every 5 minutes) to publish scheduled posts.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Process scheduled posts and publish them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without actually processing',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=50,
            help='Maximum number of posts to process in one run',
        )

    def handle(self, *args, **options):
        from images.models import ScheduledPost, PostAnalytics
        
        dry_run = options['dry_run']
        limit = options['limit']
        
        # Get posts that are due for publishing
        now = timezone.now()
        due_posts = ScheduledPost.objects.filter(
            status='pending',
            scheduled_for__lte=now
        ).select_related('post', 'user')[:limit]
        
        if not due_posts.exists():
            self.stdout.write(
                self.style.SUCCESS('No scheduled posts due for publishing')
            )
            return
        
        self.stdout.write(
            f'Found {due_posts.count()} posts due for publishing'
        )
        
        processed_count = 0
        failed_count = 0
        
        for scheduled_post in due_posts:
            if dry_run:
                self.stdout.write(
                    f'[DRY RUN] Would process: {scheduled_post.post} '
                    f'scheduled for {scheduled_post.scheduled_for}'
                )
                continue
            
            try:
                with transaction.atomic():
                    # Mark as processing
                    scheduled_post.mark_as_processing()
                    
                    # Process the post
                    success = self.process_post(scheduled_post)
                    
                    if success:
                        # Mark as posted
                        scheduled_post.mark_as_posted()
                        scheduled_post.post.posted = True
                        scheduled_post.post.save()
                        
                        # Create analytics record if it doesn't exist
                        PostAnalytics.objects.get_or_create(
                            post=scheduled_post.post,
                            defaults={
                                'likes_count': 0,
                                'comments_count': 0,
                                'shares_count': 0,
                                'reach': 0,
                                'impressions': 0
                            }
                        )
                        
                        processed_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'Successfully processed: {scheduled_post.post}'
                            )
                        )
                    else:
                        # Mark as failed
                        scheduled_post.mark_as_failed('Processing failed')
                        failed_count += 1
                        self.stdout.write(
                            self.style.ERROR(
                                f'Failed to process: {scheduled_post.post}'
                            )
                        )
                        
            except Exception as e:
                logger.error(f'Error processing scheduled post {scheduled_post.id}: {e}')
                scheduled_post.mark_as_failed(str(e))
                failed_count += 1
                self.stdout.write(
                    self.style.ERROR(
                        f'Error processing {scheduled_post.post}: {e}'
                    )
                )
        
        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nProcessing complete:\n'
                f'- Processed: {processed_count}\n'
                f'- Failed: {failed_count}\n'
                f'- Total: {processed_count + failed_count}'
            )
        )
    
    def process_post(self, scheduled_post):
        """Process a single scheduled post."""
        try:
            # For now, just mark as posted since we're not implementing
            # actual social media posting
            
            # In a real implementation, this would:
            # 1. Upload image to social media platforms
            # 2. Post content with caption and hashtags
            # 3. Store the platform-specific post IDs
            # 4. Handle any platform-specific errors
            
            # Simulate processing time
            import time
            time.sleep(0.1)
            
            # Log the "posting"
            logger.info(
                f'Posted to platforms {scheduled_post.target_platforms}: '
                f'{scheduled_post.post.caption[:50]}...'
            )
            
            return True
            
        except Exception as e:
            logger.error(f'Error posting scheduled post: {e}')
            return False
