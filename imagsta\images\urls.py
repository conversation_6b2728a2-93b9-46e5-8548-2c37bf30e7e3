from django.urls import path
from django.views.decorators.cache import cache_page
from . import views
from django.contrib.auth.views import LogoutView


#  cache_page(5*60)

urlpatterns = [
    path('',cache_page(5*60)(views.IndexView.as_view()), name='index'),
    path('images', views.ImagesView.as_view(), name='images'),
    path('login/', views.Login.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path("register/", views.RegisterView.as_view(), name="register"),
    path("search/", views.search_for_images, name="search"),
    path("posts/", views.PostsView.as_view(), name="posts"),
    path('new_feed/', views.new_feed, name="new-feed"),
    path('create_custom/', views.CustomView.as_view(), name='create-custom'),
    path('get_hashtags', views.get_hashtags, name='get-hash'),
    path('ai-studio/', views.ai_content_studio, name='ai-studio'),
    path('analytics/', views.analytics_dashboard, name='analytics'),
    path('news-dashboard/', views.news_dashboard, name='news-dashboard'),
    path('news-sources/', views.news_sources_management, name='news-sources'),
    path('news-highlights/', views.news_highlights, name='news-highlights'),
    path('news/', views.all_news, name='all-news'),
    path('account-categories/', views.account_categories_management, name='account-categories'),
    path('calendar/', views.content_calendar, name='calendar'),
    path('multi-platform/', views.multi_platform_post, name='multi-platform'),
    path('templates/', views.content_templates, name='templates'),
    path('templates/preview/<int:template_id>/', views.template_preview, name='template-preview'),
    path('bulk-upload/', views.bulk_upload, name='bulk-upload'),
    path('scheduler/', views.advanced_scheduler, name='scheduler'),
    path('templates/create/', views.create_template, name='create-template'),
    path('templates/delete/<int:template_id>/', views.delete_template, name='delete-template'),
    path('schedule-post/', views.schedule_post, name='schedule-post'),
    path('analytics/data/', views.get_analytics_data, name='analytics-data'),
    path('analytics/export/', views.export_analytics, name='export-analytics'),
    path('bulk-schedule/', views.bulk_schedule_posts, name='bulk-schedule'),
    path('optimize-schedule/', views.optimize_posting_schedule, name='optimize-schedule'),
    path('connect-accounts/', views.connect_accounts, name='connect-accounts'),
    path('connect/<str:platform>/', views.connect_platform, name='connect-platform'),
    path('disconnect/<int:account_id>/', views.disconnect_platform, name='disconnect-platform'),
    path('refresh-account/<int:account_id>/', views.refresh_account_data, name='refresh-account'),
    path('create-template/', views.create_template, name='create-template'),
    path('duplicate-template/<int:template_id>/', views.duplicate_template, name='duplicate-template'),
    path('delete-template/<int:template_id>/', views.delete_template, name='delete-template'),
    path('export-templates/', views.export_templates, name='export-templates'),
    path('import-templates/', views.import_templates, name='import-templates'),
]

htmx_urlpatterns = [
    path('check_username/', views.check_username, name='check-username'),
    path('search_images/', views.search_images, name='search-image'),
    # path('delete_image/<int:pk>/', views.delete_image, name='delete-image'),
    path('delete_post/<int:pk>/', views.delete_post, name='delete-post'),
    path('create_post/', views.image_to_post, name='create-post'),
    path('create_post1/', views.url_to_post, name='create-post1'),
    path('post', views.upload_post, name="upload-post"),
    path('load_feed', views.get_feed, name='get-feed'),
    path('generate-ai-content/', views.generate_ai_content, name='generate-ai-content'),
    path('create-post-from-news/', views.create_post_from_news, name='create-post-from-news'),
    path('toggle-news-source/', views.toggle_news_source, name='toggle-news-source'),
    path('update-account-categories/', views.update_account_categories, name='update-account-categories'),

]

urlpatterns += htmx_urlpatterns