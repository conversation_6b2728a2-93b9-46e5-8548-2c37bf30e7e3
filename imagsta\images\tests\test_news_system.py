"""
Tests for the news aggregation system.
"""

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from unittest.mock import patch, MagicMock

from images.models import (
    NewsSource, NewsArticle, Category, AIHighlight, 
    AccountCategoryMapping, SocialMediaAccount
)
from images.news_services import NewsAggregatorService, RSSFetcher
from images.ai_services import ai_service
from images.auto_posting_service import auto_posting_service

User = get_user_model()


class NewsModelsTest(TestCase):
    """Test news-related models."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.category = Category.objects.create(
            name='technology',
            display_name='Technology',
            description='Tech news and updates'
        )
        
        self.source = NewsSource.objects.create(
            name='Test RSS',
            source_type='rss',
            url='https://example.com/feed.xml',
            fetch_interval=60
        )
        self.source.categories.add(self.category)
    
    def test_category_creation(self):
        """Test category model creation and methods."""
        self.assertEqual(str(self.category), 'Technology')
        self.assertTrue(self.category.is_active)
        self.assertEqual(self.category.priority_score, 1.0)
    
    def test_news_source_creation(self):
        """Test news source model creation and methods."""
        self.assertEqual(str(self.source), 'Test RSS (rss)')
        self.assertTrue(self.source.is_active)
        self.assertEqual(self.source.get_health_status(), 'never_fetched')
    
    def test_news_source_health_status(self):
        """Test news source health status calculation."""
        # Mark as recently fetched
        self.source.mark_fetch_attempt(success=True)
        self.assertEqual(self.source.get_health_status(), 'healthy')
        
        # Mark with errors
        for _ in range(6):
            self.source.mark_fetch_attempt(success=False, error_message='Test error')
        self.assertEqual(self.source.get_health_status(), 'unhealthy')
    
    def test_news_article_creation(self):
        """Test news article model creation and methods."""
        article = NewsArticle.objects.create(
            source=self.source,
            title='Test Article',
            content='This is a test article content.',
            url='https://example.com/article1',
            published_date=timezone.now(),
            engagement_prediction=75.0,
            trending_score=80.0,
            sentiment_score=0.5
        )
        article.categories.add(self.category)
        
        self.assertEqual(str(article), 'Test Article')
        self.assertTrue(article.should_highlight(threshold=70))
        self.assertFalse(article.should_highlight(threshold=85))
        self.assertIn('Technology', article.get_category_names())
    
    def test_ai_highlight_creation(self):
        """Test AI highlight model creation and methods."""
        article = NewsArticle.objects.create(
            source=self.source,
            title='Highlighted Article',
            content='This article should be highlighted.',
            url='https://example.com/highlighted',
            published_date=timezone.now()
        )
        
        highlight = AIHighlight.objects.create(
            article=article,
            highlight_score=85.0,
            highlight_reasons=['High engagement potential', 'Trending topic'],
            recommended_platforms=['twitter', 'linkedin']
        )
        
        self.assertFalse(highlight.was_posted)
        highlight.mark_as_posted(engagement_score=90.0)
        self.assertTrue(highlight.was_posted)
        self.assertEqual(highlight.actual_engagement, 90.0)


class NewsServicesTest(TestCase):
    """Test news aggregation services."""
    
    def setUp(self):
        self.category = Category.objects.create(
            name='technology',
            display_name='Technology'
        )
        
        self.source = NewsSource.objects.create(
            name='Test RSS',
            source_type='rss',
            url='https://example.com/feed.xml',
            fetch_interval=60
        )
        self.source.categories.add(self.category)
        
        self.aggregator = NewsAggregatorService()
    
    @patch('requests.Session.get')
    def test_rss_fetcher(self, mock_get):
        """Test RSS fetching functionality."""
        # Mock RSS response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = '''<?xml version="1.0"?>
        <rss version="2.0">
            <channel>
                <item>
                    <title>Test Article</title>
                    <description>Test description</description>
                    <link>https://example.com/article1</link>
                    <pubDate>Mon, 01 Jan 2024 12:00:00 GMT</pubDate>
                </item>
            </channel>
        </rss>'''
        mock_get.return_value = mock_response
        
        fetcher = RSSFetcher(self.source)
        articles = fetcher.fetch_articles()
        
        self.assertEqual(len(articles), 1)
        self.assertEqual(articles[0]['title'], 'Test Article')
        self.assertEqual(articles[0]['url'], 'https://example.com/article1')
    
    def test_should_fetch_from_source(self):
        """Test fetch timing logic."""
        # New source should be fetched
        self.assertTrue(self.aggregator._should_fetch_from_source(self.source))
        
        # Recently fetched source should not be fetched
        self.source.last_fetch = timezone.now()
        self.source.save()
        self.assertFalse(self.aggregator._should_fetch_from_source(self.source))
        
        # Old fetch should trigger new fetch
        self.source.last_fetch = timezone.now() - timedelta(hours=2)
        self.source.save()
        self.assertTrue(self.aggregator._should_fetch_from_source(self.source))


class AIAnalysisTest(TestCase):
    """Test AI analysis functionality."""
    
    def setUp(self):
        self.category = Category.objects.create(
            name='technology',
            display_name='Technology'
        )
        
        self.source = NewsSource.objects.create(
            name='Test Source',
            source_type='rss',
            url='https://example.com/feed.xml'
        )
        
        self.article = NewsArticle.objects.create(
            source=self.source,
            title='AI Breakthrough in Machine Learning',
            content='Researchers have made a significant breakthrough in artificial intelligence and machine learning technology. This innovation could revolutionize how we approach data processing.',
            url='https://example.com/ai-breakthrough',
            published_date=timezone.now()
        )
        self.article.categories.add(self.category)
    
    def test_content_analysis(self):
        """Test basic content analysis."""
        analysis = ai_service.analyze_news_article(self.article)
        
        self.assertIn('sentiment_score', analysis)
        self.assertIn('engagement_prediction', analysis)
        self.assertIn('trending_score', analysis)
        self.assertIn('keywords', analysis)
        self.assertIn('topics', analysis)
        
        # Check that scores are in valid ranges
        self.assertGreaterEqual(analysis['sentiment_score'], -1)
        self.assertLessEqual(analysis['sentiment_score'], 1)
        self.assertGreaterEqual(analysis['engagement_prediction'], 0)
        self.assertLessEqual(analysis['engagement_prediction'], 100)
    
    def test_highlight_decision(self):
        """Test AI highlighting decision."""
        should_highlight, highlight_data = ai_service.should_highlight_article(self.article)
        
        self.assertIsInstance(should_highlight, bool)
        self.assertIn('highlight_score', highlight_data)
        self.assertIn('highlight_reasons', highlight_data)
        self.assertIn('recommended_platforms', highlight_data)
        self.assertIn('suggested_caption', highlight_data)
    
    def test_keyword_extraction(self):
        """Test keyword extraction."""
        keywords = ai_service._extract_keywords(self.article.content)
        
        self.assertIsInstance(keywords, list)
        # Should extract relevant keywords
        keyword_text = ' '.join(keywords).lower()
        self.assertTrue(any(word in keyword_text for word in ['artificial', 'intelligence', 'machine', 'learning']))


class AutoPostingTest(TestCase):
    """Test automated posting functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.category = Category.objects.create(
            name='technology',
            display_name='Technology'
        )
        
        self.account = SocialMediaAccount.objects.create(
            user=self.user,
            platform='twitter',
            username='testaccount',
            status='connected'
        )
        
        self.mapping = AccountCategoryMapping.objects.create(
            account=self.account,
            category=self.category,
            auto_post=True,
            min_highlight_score=70.0
        )
        
        self.source = NewsSource.objects.create(
            name='Test Source',
            source_type='rss',
            url='https://example.com/feed.xml'
        )
        
        self.article = NewsArticle.objects.create(
            source=self.source,
            title='Test Article for Auto-posting',
            content='This is a test article that should be auto-posted.',
            url='https://example.com/auto-post-test',
            published_date=timezone.now()
        )
        self.article.categories.add(self.category)
        
        self.highlight = AIHighlight.objects.create(
            article=self.article,
            highlight_score=85.0,
            highlight_reasons=['High engagement potential'],
            recommended_platforms=['twitter']
        )
    
    def test_eligible_highlights(self):
        """Test getting eligible highlights for auto-posting."""
        highlights = auto_posting_service._get_eligible_highlights()
        self.assertIn(self.highlight, highlights)
    
    def test_eligible_accounts(self):
        """Test getting eligible accounts for a highlight."""
        mappings = auto_posting_service._get_eligible_accounts_for_highlight(self.highlight)
        self.assertIn(self.mapping, mappings)
    
    def test_auto_post_creation(self):
        """Test creating auto-posts."""
        initial_post_count = self.user.posts.count()
        
        success = auto_posting_service._create_auto_post(self.highlight, [self.mapping])
        
        self.assertTrue(success)
        self.assertEqual(self.user.posts.count(), initial_post_count + 1)
        
        # Check that highlight is marked as posted
        self.highlight.refresh_from_db()
        self.assertTrue(self.highlight.was_posted)


class NewsViewsTest(TestCase):
    """Test news-related views."""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')
        
        self.category = Category.objects.create(
            name='technology',
            display_name='Technology'
        )
        
        self.source = NewsSource.objects.create(
            name='Test Source',
            source_type='rss',
            url='https://example.com/feed.xml'
        )
        
        self.article = NewsArticle.objects.create(
            source=self.source,
            title='Test Article',
            content='Test content',
            url='https://example.com/test',
            published_date=timezone.now()
        )
    
    def test_news_dashboard_view(self):
        """Test news dashboard view."""
        response = self.client.get('/news-dashboard/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'News Dashboard')
    
    def test_news_highlights_view(self):
        """Test news highlights view."""
        response = self.client.get('/news-highlights/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'AI Highlights')
    
    def test_news_sources_view(self):
        """Test news sources management view."""
        response = self.client.get('/news-sources/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'News Sources')
    
    def test_account_categories_view(self):
        """Test account categories view."""
        response = self.client.get('/account-categories/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Account Categories')


class ManagementCommandsTest(TestCase):
    """Test management commands."""
    
    def setUp(self):
        self.category = Category.objects.create(
            name='technology',
            display_name='Technology'
        )
    
    def test_fetch_news_command(self):
        """Test fetch_news management command."""
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('fetch_news', '--create-sample-sources', stdout=out)
        
        # Check that sample sources were created
        self.assertTrue(NewsSource.objects.filter(name__contains='TechCrunch').exists())
        self.assertIn('Sample sources created successfully', out.getvalue())
    
    def test_analyze_news_command(self):
        """Test analyze_news management command."""
        from django.core.management import call_command
        from io import StringIO
        
        # Create a test article
        source = NewsSource.objects.create(
            name='Test Source',
            source_type='rss',
            url='https://example.com/feed.xml'
        )
        
        NewsArticle.objects.create(
            source=source,
            title='Test Article',
            content='Test content for analysis',
            url='https://example.com/test',
            published_date=timezone.now()
        )
        
        out = StringIO()
        call_command('analyze_news', '--limit', '1', stdout=out)
        
        self.assertIn('Processing', out.getvalue())


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['images.tests.test_news_system'])
