import logging
import random
import time
import requests
import io
import uuid
import json
from datetime import <PERSON><PERSON><PERSON>
from django.core.files import File
from django.http.response import HttpResponse
from django.http import JsonResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.shortcuts import render, redirect
from django.contrib.auth.views import LoginView
from django.urls import reverse_lazy
from django.views.generic import FormView, TemplateView, ListView
from django.contrib.auth import get_user_model
from django.contrib import messages
from django.db.models import Count, Avg, Q, F
from django.db import models
from django.utils import timezone
from django.conf import settings
from bs4 import BeautifulSoup
from PIL import Image
from pygooglenews import GoogleNews
from decouple import config
from .models import User, Search, Result, Post, NewsSource, NewsArticle, AIHighlight, Category, AccountCategoryMapping
from .utils import getContentPublishingLimit, make_post, image_to_url, getCreds, createMediaObject, publishMedia
from .forms import RegisterForm
from .ai_services import ai_service
from .news_services import NewsAggregatorService

# Set up logging
logger = logging.getLogger(__name__)


class IndexView(TemplateView):
    template_name = 'index.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        if self.request.user.is_authenticated:
            from .models import Post, ContentTemplate, SocialMediaAccount

            # Get user statistics
            user_posts = Post.objects.filter(user=self.request.user)
            user_templates = ContentTemplate.objects.filter(user=self.request.user)
            connected_accounts = SocialMediaAccount.objects.filter(user=self.request.user)

            # User stats for dashboard
            user_stats = {
                'total_posts': user_posts.count(),
                'total_templates': user_templates.count(),
                'connected_accounts': len(connected_accounts),
            }

            # Recent posts (last 5)
            recent_posts = user_posts.order_by('-created')[:5]

            # Popular templates (most used)
            popular_templates = ContentTemplate.objects.filter(
                user=self.request.user
            ).order_by('-usage_count')[:3]

            # If user has no templates, get some public ones
            if not popular_templates.exists():
                popular_templates = ContentTemplate.objects.filter(
                    is_public=True
                ).order_by('-usage_count')[:3]

            context.update({
                'user_stats': user_stats,
                'recent_posts': recent_posts,
                'popular_templates': popular_templates,
                'connected_accounts': connected_accounts,
            })

        # Add news content for everyone (not just authenticated users)
        try:
            from .models import NewsArticle, AIHighlight, Category

            # Get recent news highlights
            recent_highlights = AIHighlight.objects.select_related(
                'article', 'article__source'
            ).order_by('-highlight_score', '-created')[:6]

            # Get recent articles by category
            categories = Category.objects.filter(is_active=True)[:4]
            news_by_category = {}

            for category in categories:
                articles = NewsArticle.objects.filter(
                    categories=category
                ).order_by('-published_date')[:3]
                if articles:
                    news_by_category[category] = articles

            # News stats
            news_stats = {
                'total_articles': NewsArticle.objects.count(),
                'total_highlights': AIHighlight.objects.count(),
                'recent_articles': NewsArticle.objects.filter(
                    created__gte=timezone.now() - timedelta(hours=24)
                ).count(),
            }

            context.update({
                'recent_highlights': recent_highlights,
                'news_by_category': news_by_category,
                'news_stats': news_stats,
                'has_news': recent_highlights.exists() or bool(news_by_category),
            })

        except Exception as e:
            logger.error(f"Error loading news for index: {e}")
            context.update({
                'recent_highlights': [],
                'news_by_category': {},
                'news_stats': {'total_articles': 0, 'total_highlights': 0, 'recent_articles': 0},
                'has_news': False,
            })

        return context

class ImagesView(LoginRequiredMixin, TemplateView):
    template_name = 'images.html'

    def get_context_data(self, **kwargs):
        context = super(ImagesView, self).get_context_data(**kwargs)
        images = list(Result.objects.all())
        random.shuffle(images)
        context['images']= images[:12]
        return context

    
class Login(LoginView):
    template_name = 'registration/login.html'

class RegisterView(FormView):
    form_class = RegisterForm
    template_name = 'registration/register.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        form.save()  # save the user
        return super().form_valid(form)


@require_http_methods(['DELETE'])
def delete_image(request, pk):
    Result.objects.get(pk=pk).delete()
    images = list(Result.objects.all())
    random.shuffle(images)

    context = {
        'images': images[:12]
    }
    return render(request, 'partials/test.html', context)

def check_username(request):
    username = request.POST.get('username')
    if get_user_model().objects.filter(username=username).exists():
        return HttpResponse("<div id='username-error' class='error'>This username already exists</div>")
    else:
        return HttpResponse("<div id='username-error' class='success'>This username is available</div>")


def search_for_images(request):
    """Search for images using Unsplash API with improved error handling and rate limiting."""
    query = request.POST.get('search', '').strip()
    feed = request.POST.get('feed', '')

    if not query:
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'Please enter a search query'
        })

    if len(query) < 2:
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'Search query must be at least 2 characters long'
        })

    try:
        search, created = Search.objects.get_or_create(query=query)

        if not created:
            # Increment search count for existing queries
            search.increment_search_count()

        if created or not search.results.exists():
            # Fetch new images from Unsplash
            client_id = config('UNSPLASH_CLIENT_ID', default='')
            if not client_id:
                logger.error("Unsplash client ID not configured")
                return render(request, 'partials/test.html', {
                    'images': [],
                    'feed': feed,
                    'error': 'Image search service not configured'
                })

            params = {
                'query': query,
                'client_id': client_id,
                'per_page': 12,
                'orientation': 'squarish',  # Better for Instagram posts
                'content_filter': 'high'    # Filter out potentially inappropriate content
            }
            url = 'https://api.unsplash.com/search/photos'

            headers = {
                'Accept-Version': 'v1',
                'User-Agent': 'Imagsta/1.0'
            }

            response = requests.get(url, params=params, headers=headers, timeout=15)
            response.raise_for_status()

            # Check rate limiting
            if response.status_code == 429:
                logger.warning("Unsplash API rate limit exceeded")
                return render(request, 'partials/test.html', {
                    'images': [],
                    'feed': feed,
                    'error': 'Too many requests. Please try again later.'
                })

            data = response.json()

            if not data.get('results'):
                return render(request, 'partials/test.html', {
                    'images': [],
                    'feed': feed,
                    'error': f'No images found for "{query}"'
                })

            images = []
            dimages = []

            for obj in data.get('results', []):
                try:
                    image_url = obj['urls']['full']
                    display_url = obj['urls']['small']
                    alt_text = obj.get('alt_description', '') or obj.get('description', '')

                    dimages.append(display_url)
                    images.append(image_url)

                    Result.objects.create(
                        query=search,
                        image=image_url,
                        dis_image=display_url,
                        image_alt=alt_text[:255],  # Truncate to field limit
                        source='unsplash'
                    )
                except KeyError as e:
                    logger.warning(f"Missing expected field in Unsplash response: {e}")
                    continue

            comp_list = list(zip(images, dimages))
        else:
            # Use existing results
            results = search.results.all()[:12]  # Limit to 12 results
            comp_list = [(r.image, r.dis_image) for r in results]

        context = {
            'images': comp_list,
            'feed': feed,
            'query': query,
            'total_results': len(comp_list)
        }
        return render(request, 'partials/test.html', context)

    except requests.exceptions.Timeout:
        logger.error("Timeout while fetching images from Unsplash")
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'Request timed out. Please try again.'
        })
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching images from Unsplash: {e}")
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'Failed to fetch images from search service'
        })
    except Exception as e:
        logger.error(f"Unexpected error in search_for_images: {e}")
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'An unexpected error occurred'
        })

def search_images(request):
    search = request.POST.get('search')
    
    results = Search.objects.filter(query=search).exists()
    
    context = {
        'results':results
    }
    return render(request, 'partials/search_result.html', context)


class PostsView(ListView):
    model = Post
    template_name = "posts.html"
    context_object_name = 'posts'

    def get_queryset(self):
        return Post.objects.filter(user=self.request.user).order_by('-id')[:21]

def image_text(request):
    # Get image and feed data for preview
    image_url = request.POST.get('image')
    feed_text = request.POST.get('feed')
    logger.debug(f"Image preview requested: {image_url}, Feed: {feed_text}")
    return render(request, 'partials/image_result.html')

def image_to_post(request):
    image_url = request.POST.get('image')
    feed = request.POST.get('feed')

    try:
        logger.info(f"Creating post from image: {image_url}")
        image = Image.open(requests.get(image_url, stream=True).raw).convert('RGB')
        post = make_post(image, feed)
        tempfile_io = io.BytesIO()
        post.save(tempfile_io, format='JPEG', quality=100)
        name = uuid.uuid4()
        image_file = File(tempfile_io, name=str(name)+'.jpeg')
        post = Post(user=request.user, image=image_file, post_text=feed)
        post.image = image_file
        post.save()
        context = {
            'post_url': post.image
        }
        return render(request, 'partials/image_result.html', context)
    except Exception as e:
        logger.error(f"Error creating post from image: {e}")
        return HttpResponse('<div class="alert alert-danger">Error creating post</div>')


def url_to_post(request):
    image_url = request.POST.get('image')
    feed = request.POST.get('feed')

    try:
        logger.info(f"Creating post from URL: {image_url}")
        image = Image.open(requests.get(image_url, stream=True).raw).convert('RGB')
        post = make_post(image, feed)
        tempfile_io = io.BytesIO()
        post.save(tempfile_io, format='JPEG', quality=100)
        name = uuid.uuid4()
        image_file = File(tempfile_io, name=str(name)+'.jpeg')
        post = Post(user=request.user, image=image_file, post_text=feed)
        post.image = image_file
        post.save()
        return HttpResponse('<button id="edit-btn" class="btn btn-outline-success" disabled type="submit">Created!</button>')
    except Exception as e:
        logger.error(f"Error creating post from URL: {e}")
        return HttpResponse('<button id="edit-btn" class="btn btn-outline-danger" disabled type="submit">Error!</button>')


def new_feed(request):
    title = request.POST.get('title')
    context = {
        'title': title,
    }
    return render(request, 'new_feed.html', context)


def upload_post(request):
    """Upload a post to Instagram with improved error handling and validation."""
    if request.method != 'POST':
        return HttpResponse('Method not allowed', status=405)

    try:
        image_path = request.POST.get('image', '').strip()
        post_id = request.POST.get('id', '').strip()
        hashtags = request.POST.get('hashtags', '').strip()
        caption = request.POST.get('caption', '').strip()

        # Validate required fields
        if not image_path or not post_id:
            messages.error(request, 'Missing required fields')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Missing required fields'
            })

        # Get the post object
        try:
            post = Post.objects.get(pk=post_id, user=request.user)
        except Post.DoesNotExist:
            messages.error(request, 'Post not found')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Post not found'
            })

        # Upload image to hosting service
        image_url = image_to_url(image_path)
        if not image_url:
            logger.error(f"Failed to upload image to hosting service: {image_path}")
            messages.error(request, 'Failed to upload image')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Failed to upload image'
            })

        logger.info(f"Image uploaded successfully: {image_url}")

        # Get Instagram API credentials
        creds = getCreds()
        if not creds:
            logger.error("Instagram API credentials not configured")
            messages.error(request, 'Instagram API not configured')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Instagram API not configured'
            })

        # Prepare Instagram post parameters
        creds['media_type'] = 'IMAGE'
        creds['media_url'] = image_url

        # Build caption with proper formatting
        full_caption = caption.strip() if caption else ''
        if hashtags:
            if full_caption:
                full_caption += '\n\n'
            full_caption += hashtags

        creds['caption'] = full_caption[:2200]  # Instagram caption limit

        # Create media object on Instagram
        media_response = createMediaObject(creds)
        if 'error' in media_response:
            logger.error(f"Failed to create Instagram media object: {media_response['error']}")
            messages.error(request, 'Failed to create Instagram media object')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Failed to create Instagram media object'
            })

        media_object_id = media_response['json_data'].get('id')
        if not media_object_id:
            logger.error("No media object ID returned from Instagram API")
            messages.error(request, 'Invalid response from Instagram API')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Invalid response from Instagram API'
            })

        logger.info(f"Instagram media object created with ID: {media_object_id}")

        # Publish the post to Instagram
        publish_response = publishMedia(media_object_id, creds)
        if 'error' in publish_response:
            logger.error(f"Failed to publish to Instagram: {publish_response['error']}")
            messages.error(request, 'Failed to publish to Instagram')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Failed to publish to Instagram'
            })

        instagram_post_id = publish_response['json_data'].get('id')
        if not instagram_post_id:
            logger.error("No Instagram post ID returned")
            messages.error(request, 'Post published but no ID returned')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Post published but no ID returned'
            })

        # Update post with success information
        post.published_image_id = instagram_post_id
        post.posted = True
        post.hashtags = hashtags
        post.caption = caption
        post.save()

        logger.info(f"Post successfully published to Instagram with ID: {instagram_post_id}")
        messages.success(request, 'Post successfully published to Instagram!')

        # Return updated posts list
        posts = Post.objects.filter(user=request.user).order_by('-created')[:21]
        return render(request, 'partials/posts.html', {'posts': posts})

    except Exception as e:
        logger.error(f"Unexpected error in upload_post: {e}")
        messages.error(request, 'An unexpected error occurred')
        return render(request, 'partials/posts.html', {
            'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
            'error': 'An unexpected error occurred'
        })


class CustomView(TemplateView):
    template_name = 'custom_post.html'

@require_http_methods(['DELETE'])
def delete_post(request, pk):
    Post.objects.get(pk=pk).delete()
    images = list(Post.objects.filter(user=request.user).order_by('-id')[:21])


    context = {
        'posts': images[:21]
    }
    return render(request, 'partials/posts.html', context)


def get_feed():

    url = "https://collider.com"
    headers = requests.utils.default_headers()

    headers.update(
        {
            'User-Agent': 'My User Agent 1.0',
        }
    )
    result = requests.get(url=url, headers=headers)

    doc = BeautifulSoup(result.text, "html.parser")
    feeds = doc.find_all("a", class_="bc-title-link")
    news = []
    for feed in feeds:
        news.append(feed.text)
    logger.debug(f"Fetched {len(news)} news items")
    return news


def get_hashtags(request):
    """Generate hashtags for a given query."""
    query = request.POST.get('hash-search', '').strip()

    if not query:
        return HttpResponse('Please enter a search term')

    try:
        # Use AI service for hashtag generation if available
        from .ai_services import ai_service
        hashtags = ai_service.generate_hashtags(query)

        if hashtags:
            return HttpResponse(' '.join(hashtags))
        else:
            # Fallback to basic hashtag generation
            basic_hashtags = [
                f"#{query.replace(' ', '')}",
                f"#{query.replace(' ', '').lower()}",
                "#trending",
                "#viral",
                "#content"
            ]
            return HttpResponse(' '.join(basic_hashtags))

    except Exception as e:
        logger.error(f"Error generating hashtags: {e}")
        # Basic fallback
        fallback = f"#{query.replace(' ', '').lower()} #trending #content"
        return HttpResponse(fallback)


def generate_ai_content(request):
    """Generate AI-powered captions and hashtags for content."""
    if request.method != 'POST':
        return HttpResponse('Method not allowed', status=405)

    try:
        image_description = request.POST.get('image_description', '').strip()
        news_context = request.POST.get('news_context', '').strip()
        tone = request.POST.get('tone', 'professional')
        industry = request.POST.get('industry', 'general')

        if not image_description and not news_context:
            return render(request, 'partials/ai_content.html', {
                'error': 'Please provide either image description or news context'
            })

        # Generate AI content
        caption = ai_service.generate_caption(
            image_description=image_description,
            news_context=news_context,
            tone=tone
        )

        hashtags = ai_service.suggest_hashtags(
            content=caption or news_context,
            industry=industry
        )

        # Get posting time suggestions
        posting_suggestions = ai_service.optimize_posting_time({})

        # Analyze content performance
        performance_analysis = ai_service.analyze_content_performance(
            caption or news_context
        )

        context = {
            'generated_caption': caption,
            'suggested_hashtags': hashtags,
            'posting_suggestions': posting_suggestions,
            'performance_analysis': performance_analysis,
            'tone': tone,
            'industry': industry
        }

        return render(request, 'partials/ai_content.html', context)

    except Exception as e:
        logger.error(f"Error in generate_ai_content: {e}")
        return render(request, 'partials/ai_content.html', {
            'error': 'An error occurred while generating content. Please try again.'
        })


def ai_content_studio(request):
    """AI Content Studio - main page for AI-powered content creation."""
    if not request.user.is_authenticated:
        return redirect('login')

    # Check if a news article was passed
    news_article_id = request.GET.get('news_article_id')
    news_article = None
    suggested_content = ""

    if news_article_id:
        try:
            from .models import NewsArticle
            news_article = NewsArticle.objects.get(id=news_article_id)
            # Pre-populate with news content
            suggested_content = f"Breaking: {news_article.title}\n\n{news_article.content[:200]}..."
        except NewsArticle.DoesNotExist:
            pass

    context = {
        'tone_options': [
            ('professional', 'Professional'),
            ('casual', 'Casual'),
            ('humorous', 'Humorous'),
            ('inspirational', 'Inspirational')
        ],
        'industry_options': [
            ('general', 'General'),
            ('tech', 'Technology'),
            ('business', 'Business'),
            ('lifestyle', 'Lifestyle'),
            ('health', 'Health & Wellness'),
            ('food', 'Food & Beverage'),
            ('travel', 'Travel'),
            ('fashion', 'Fashion'),
            ('education', 'Education'),
            ('finance', 'Finance')
        ],
        'news_article': news_article,
        'suggested_content': suggested_content,
    }

    return render(request, 'ai_studio.html', context)


def analytics_dashboard(request):
    """Analytics dashboard showing post performance and insights."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import PostAnalytics, UserAnalytics, ScheduledPost
    from datetime import datetime, timedelta

    # Get or create user analytics
    user_analytics, created = UserAnalytics.objects.get_or_create(user=request.user)
    if created or user_analytics.last_calculated < timezone.now() - timedelta(hours=1):
        user_analytics.update_totals()

    # Get user's posts
    user_posts = Post.objects.filter(user=request.user).order_by('-created')

    # Calculate basic analytics
    total_posts = user_posts.count()
    posted_count = user_posts.filter(posted=True).count()
    draft_count = user_posts.filter(posted=False).count()
    scheduled_count = ScheduledPost.objects.filter(user=request.user, status='pending').count()

    # Recent activity (last 30 days)
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_posts = user_posts.filter(created__gte=thirty_days_ago)

    # Get real analytics data
    post_analytics = PostAnalytics.objects.filter(post__user=request.user)

    # Calculate aggregate metrics
    total_likes = sum(pa.likes_count for pa in post_analytics)
    total_comments = sum(pa.comments_count for pa in post_analytics)
    total_shares = sum(pa.shares_count for pa in post_analytics)
    total_reach = sum(pa.reach for pa in post_analytics)
    total_impressions = sum(pa.impressions for pa in post_analytics)

    avg_engagement_rate = user_analytics.avg_engagement_rate

    analytics_data = {
        'total_likes': total_likes,
        'total_comments': total_comments,
        'total_shares': total_shares,
        'avg_engagement_rate': round(avg_engagement_rate, 2),
        'follower_growth': user_analytics.follower_growth_rate,
        'reach': total_reach,
        'impressions': total_impressions
    }

    # Get top performing posts
    top_posts = PostAnalytics.objects.filter(
        post__user=request.user
    ).order_by('-engagement_rate')[:5]

    # Posting frequency data for chart
    posting_frequency = []
    for i in range(7):
        date = timezone.now() - timedelta(days=i)
        count = user_posts.filter(
            created__date=date.date()
        ).count()
        posting_frequency.append({
            'date': date.strftime('%Y-%m-%d'),
            'count': count
        })

    # Engagement trend data
    engagement_trend = user_analytics.get_engagement_trend(30)

    # Performance insights
    insights = []
    if avg_engagement_rate > 5:
        insights.append("Your engagement rate is above average! Keep up the great work.")
    elif avg_engagement_rate < 2:
        insights.append("Consider posting more engaging content to improve your engagement rate.")

    if total_posts > 0 and posted_count / total_posts < 0.5:
        insights.append("You have many drafts. Consider publishing more content regularly.")

    if scheduled_count > 0:
        insights.append(f"You have {scheduled_count} posts scheduled for publication.")

    context = {
        'total_posts': total_posts,
        'posted_count': posted_count,
        'draft_count': draft_count,
        'scheduled_count': scheduled_count,
        'recent_posts_count': recent_posts.count(),
        'analytics': analytics_data,
        'top_posts': top_posts,
        'posting_frequency': posting_frequency,
        'engagement_trend': engagement_trend,
        'recent_posts': recent_posts[:10],
        'insights': insights,
        'user_analytics': user_analytics
    }

    return render(request, 'analytics.html', context)


def content_calendar(request):
    """Content calendar view for scheduling and managing posts."""
    if not request.user.is_authenticated:
        return redirect('login')

    # Get posts for the current month
    from datetime import datetime
    current_month = datetime.now().month
    current_year = datetime.now().year

    posts = Post.objects.filter(
        user=request.user,
        created__month=current_month,
        created__year=current_year
    ).order_by('created')

    # Group posts by date
    calendar_data = {}
    for post in posts:
        date_key = post.created.strftime('%Y-%m-%d')
        if date_key not in calendar_data:
            calendar_data[date_key] = []
        calendar_data[date_key].append(post)

    context = {
        'calendar_data': calendar_data,
        'current_month': datetime.now().strftime('%B %Y'),
        'posts': posts
    }

    return render(request, 'calendar.html', context)


def multi_platform_post(request):
    """Multi-platform posting interface."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .social_platforms import social_manager

    if request.method == 'POST':
        try:
            # Get form data
            content_text = request.POST.get('content_text', '')
            selected_platforms = request.POST.getlist('platforms')
            image_file = request.FILES.get('image')

            if not content_text and not image_file:
                messages.error(request, 'Please provide content text or an image')
                return redirect('multi-platform')

            # Prepare content for posting
            content = {
                'text': content_text,
                'caption': content_text  # For Instagram
            }

            # Handle image upload if provided
            if image_file:
                # Save the image temporarily
                post = Post.objects.create(
                    user=request.user,
                    image=image_file,
                    caption=content_text,
                    post_text=content_text[:130]  # Truncate for existing field
                )

                # Upload to image hosting service
                from .utils import image_to_url
                image_path = post.image.path
                image_url = image_to_url(image_path)

                if image_url:
                    content['image_url'] = image_url
                else:
                    messages.error(request, 'Failed to upload image')
                    return redirect('multi-platform')

            # Post to selected platforms
            results = social_manager.post_to_multiple_platforms(content, selected_platforms)

            # Process results
            success_count = 0
            error_messages = []

            for platform, result in results.items():
                if result.get('success'):
                    success_count += 1
                    # Update post with platform-specific IDs if image was uploaded
                    if image_file and 'post' in locals():
                        if platform == 'instagram' and result.get('post_id'):
                            post.published_image_id = result['post_id']
                            post.posted = True
                            post.save()
                else:
                    error_messages.append(f"{platform.title()}: {result.get('error', 'Unknown error')}")

            # Show results to user
            if success_count > 0:
                messages.success(request, f'Successfully posted to {success_count} platform(s)')

            if error_messages:
                for error in error_messages:
                    messages.error(request, error)

            return redirect('multi-platform')

        except Exception as e:
            logger.error(f"Multi-platform posting error: {e}")
            messages.error(request, 'An error occurred while posting')
            return redirect('multi-platform')

    # GET request - show the form
    available_platforms = social_manager.get_available_platforms()

    context = {
        'available_platforms': available_platforms,
        'platform_info': {
            'instagram': {
                'name': 'Instagram',
                'icon': 'bi-instagram',
                'color': 'danger',
                'features': ['Images', 'Captions', 'Hashtags']
            },
            'twitter': {
                'name': 'Twitter',
                'icon': 'bi-twitter',
                'color': 'info',
                'features': ['Text (280 chars)', 'Images', 'Hashtags']
            },
            'linkedin': {
                'name': 'LinkedIn',
                'icon': 'bi-linkedin',
                'color': 'primary',
                'features': ['Professional content', 'Articles', 'Images']
            }
        }
    }

    return render(request, 'multi_platform.html', context)


def content_templates(request):
    """Content templates management interface."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import ContentTemplate

    # Handle template creation
    if request.method == 'POST':
        try:
            template = ContentTemplate.objects.create(
                user=request.user,
                name=request.POST.get('name'),
                template_type=request.POST.get('template_type'),
                content=request.POST.get('content'),
                industry=request.POST.get('industry', ''),
                tone=request.POST.get('tone', ''),
                hashtags=request.POST.get('hashtags', ''),
                is_public=request.POST.get('is_public') == 'on'
            )
            messages.success(request, f'Template "{template.name}" created successfully!')
            return redirect('templates')
        except Exception as e:
            logger.error(f"Error creating template: {e}")
            messages.error(request, 'Error creating template. Please try again.')

    # Get templates from database
    templates = ContentTemplate.objects.filter(user=request.user)

    # Add public templates
    public_templates = ContentTemplate.objects.filter(is_public=True).exclude(user=request.user)

    # Filter by industry if requested
    industry_filter = request.GET.get('industry', '')
    if industry_filter:
        templates = templates.filter(industry=industry_filter)
        public_templates = public_templates.filter(industry=industry_filter)

    # Filter by type if requested
    type_filter = request.GET.get('type', '')
    if type_filter:
        templates = templates.filter(template_type=type_filter)
        public_templates = public_templates.filter(template_type=type_filter)

    # Combine user templates and public templates
    all_templates = list(templates) + list(public_templates[:10])  # Limit public templates

    # If no templates exist, create some default ones
    if not templates.exists() and not ContentTemplate.objects.filter(user=request.user).exists():
        _create_default_templates(request.user)
        templates = ContentTemplate.objects.filter(user=request.user)
        all_templates = list(templates)

    context = {
        'templates': all_templates,
        'user_templates_count': templates.count(),
        'public_templates_count': public_templates.count(),
        'industries': ContentTemplate.INDUSTRIES,
        'template_types': ContentTemplate.TEMPLATE_TYPES,
        'tones': ContentTemplate.TONES,
        'current_industry': industry_filter,
        'current_type': type_filter,
    }

    return render(request, 'content_templates.html', context)


def _create_default_templates(user):
    """Create default templates for new users."""
    from .models import ContentTemplate

    default_templates = [
        {
            'name': 'Product Launch',
            'template_type': 'campaign',
            'content': '🚀 Exciting news! We\'re launching {product_name}! {description} #NewProduct #Launch #Innovation',
            'industry': 'technology',
            'tone': 'professional',
            'hashtags': '#NewProduct #Launch #Innovation #Tech'
        },
        {
            'name': 'Behind the Scenes',
            'template_type': 'caption',
            'content': '👀 Behind the scenes at {company_name}! Here\'s how we {activity}. What would you like to see next? #BehindTheScenes',
            'industry': 'business',
            'tone': 'casual',
            'hashtags': '#BehindTheScenes #TeamWork #Process'
        },
        {
            'name': 'Motivational Monday',
            'template_type': 'post_series',
            'content': '💪 Monday Motivation: {quote} Remember, {advice}. What\'s your goal this week? #MotivationalMonday',
            'industry': 'lifestyle',
            'tone': 'inspirational',
            'hashtags': '#MotivationalMonday #Inspiration #Goals #Success'
        }
    ]

    for template_data in default_templates:
        ContentTemplate.objects.create(user=user, **template_data)


def template_preview(request, template_id):
    """Preview a template with sample variables."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    from .models import ContentTemplate

    try:
        # Get template from database
        template = ContentTemplate.objects.get(
            id=template_id,
            user=request.user
        )
    except ContentTemplate.DoesNotExist:
        # Try to get public template
        try:
            template = ContentTemplate.objects.get(id=template_id, is_public=True)
        except ContentTemplate.DoesNotExist:
            return JsonResponse({'error': 'Template not found'}, status=404)

    # Sample data for preview
    sample_data = {
        'product_name': 'Amazing Widget Pro',
        'description': 'The ultimate solution for productivity',
        'company_name': 'TechCorp',
        'activity': 'create innovative solutions',
        'quote': '"Success is not final, failure is not fatal: it is the courage to continue that counts."',
        'advice': 'every small step counts towards your bigger goals',
        'dish_name': 'Gourmet Pasta',
        'ingredients': 'fresh tomatoes, basil, and mozzarella',
        'secret': 'a touch of love',
        'event_name': 'Summer Festival',
        'location': 'Central Park',
        'date': 'July 15th'
    }

    try:
        preview_content = template.render_content(sample_data)
    except Exception as e:
        preview_content = template.content
        logger.warning(f"Error rendering template preview: {e}")

    return JsonResponse({
        'name': template.name,
        'content': template.content,
        'preview': preview_content,
        'variables': template.variables,
        'hashtags': template.hashtags,
        'industry': template.industry,
        'tone': template.tone,
        'usage_count': template.usage_count
    })


def bulk_upload(request):
    """Bulk upload and scheduling interface."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import ScheduledPost, PostAnalytics
    from datetime import datetime, timedelta

    if request.method == 'POST':
        try:
            # Handle multiple file uploads
            uploaded_files = request.FILES.getlist('images')
            captions = request.POST.getlist('captions')
            hashtags_list = request.POST.getlist('hashtags')
            schedule_times = request.POST.getlist('schedule_times')
            schedule_type = request.POST.get('schedule_type', 'manual')

            if not uploaded_files:
                messages.error(request, 'Please select at least one image to upload')
                return redirect('bulk-upload')

            if len(uploaded_files) > 10:
                messages.error(request, 'Maximum 10 files allowed per upload')
                return redirect('bulk-upload')

            created_posts = []
            scheduled_posts = []

            for i, image_file in enumerate(uploaded_files):
                # Validate file size (max 10MB)
                if image_file.size > 10 * 1024 * 1024:
                    messages.warning(request, f'File {image_file.name} is too large (max 10MB)')
                    continue

                # Validate file type
                if not image_file.content_type.startswith('image/'):
                    messages.warning(request, f'File {image_file.name} is not an image')
                    continue

                # Get corresponding data for this image
                caption = captions[i] if i < len(captions) else ''
                hashtags = hashtags_list[i] if i < len(hashtags_list) else ''
                schedule_time = schedule_times[i] if i < len(schedule_times) else None

                # Create post
                post = Post.objects.create(
                    user=request.user,
                    image=image_file,
                    caption=caption,
                    hashtags=hashtags,
                    post_text=caption[:130]  # Truncate for existing field
                )

                # Create analytics record
                PostAnalytics.objects.create(post=post)

                created_posts.append(post)

                # Handle scheduling
                if schedule_time and schedule_type != 'manual':
                    try:
                        scheduled_datetime = datetime.fromisoformat(schedule_time.replace('Z', '+00:00'))
                        scheduled_post = ScheduledPost.objects.create(
                            user=request.user,
                            post=post,
                            scheduled_for=scheduled_datetime,
                            target_platforms=['instagram']  # Default platform
                        )
                        scheduled_posts.append(scheduled_post)
                    except ValueError:
                        logger.warning(f"Invalid schedule time format: {schedule_time}")

            # Success message
            success_msg = f'Successfully uploaded {len(created_posts)} posts!'
            if scheduled_posts:
                success_msg += f' {len(scheduled_posts)} posts scheduled for publication.'

            messages.success(request, success_msg)
            return redirect('posts')

        except Exception as e:
            logger.error(f"Bulk upload error: {e}")
            messages.error(request, 'An error occurred during bulk upload')
            return redirect('bulk-upload')

    # GET request - show the form
    context = {
        'max_files': 10,  # Limit for bulk upload
        'max_file_size': '10MB',
        'supported_formats': ['JPG', 'PNG', 'GIF', 'WEBP']
    }

    return render(request, 'bulk_upload.html', context)


def advanced_scheduler(request):
    """Advanced scheduling interface with calendar integration."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import ScheduledPost, PostAnalytics, UserAnalytics
    from datetime import datetime, timedelta

    # Get user analytics for optimal timing
    user_analytics, created = UserAnalytics.objects.get_or_create(user=request.user)

    # Get scheduled posts for the next 30 days
    start_date = timezone.now()
    end_date = start_date + timedelta(days=30)

    scheduled_posts = ScheduledPost.objects.filter(
        user=request.user,
        scheduled_for__range=[start_date, end_date]
    ).order_by('scheduled_for')

    # Calculate optimal posting times based on user's historical data
    post_analytics = PostAnalytics.objects.filter(post__user=request.user)

    # Group by hour and day of week to find patterns
    hourly_performance = {}
    daily_performance = {}

    for analytics in post_analytics:
        hour = analytics.post.created.hour
        day = analytics.post.created.weekday()  # 0=Monday, 6=Sunday

        if hour not in hourly_performance:
            hourly_performance[hour] = []
        hourly_performance[hour].append(analytics.engagement_rate)

        if day not in daily_performance:
            daily_performance[day] = []
        daily_performance[day].append(analytics.engagement_rate)

    # Calculate average engagement by hour and day
    optimal_hours = []
    for hour in range(24):
        if hour in hourly_performance:
            avg_engagement = sum(hourly_performance[hour]) / len(hourly_performance[hour])
            optimal_hours.append({'hour': hour, 'engagement': round(avg_engagement, 2)})

    optimal_hours.sort(key=lambda x: x['engagement'], reverse=True)

    # Default optimal times if no data
    if not optimal_hours:
        optimal_times = [
            {'day': 'Monday', 'time': '09:00', 'engagement': 85},
            {'day': 'Tuesday', 'time': '14:00', 'engagement': 92},
            {'day': 'Wednesday', 'time': '11:00', 'engagement': 88},
            {'day': 'Thursday', 'time': '15:00', 'engagement': 90},
            {'day': 'Friday', 'time': '13:00', 'engagement': 87},
            {'day': 'Saturday', 'time': '10:00', 'engagement': 83},
            {'day': 'Sunday', 'time': '16:00', 'engagement': 89},
        ]
    else:
        # Use calculated optimal times
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        optimal_times = []
        for i, day in enumerate(days):
            best_hour = optimal_hours[i % len(optimal_hours)]['hour'] if optimal_hours else 12
            engagement = optimal_hours[i % len(optimal_hours)]['engagement'] if optimal_hours else 85
            optimal_times.append({
                'day': day,
                'time': f'{best_hour:02d}:00',
                'engagement': engagement
            })

    # Content suggestions based on day of week and user's performance
    content_suggestions = {
        'Monday': 'Motivational content, week planning, goals',
        'Tuesday': 'Educational content, tutorials, tips',
        'Wednesday': 'Behind-the-scenes, team highlights',
        'Thursday': 'Product features, announcements',
        'Friday': 'Fun content, team celebrations',
        'Saturday': 'Lifestyle content, personal stories',
        'Sunday': 'Inspirational quotes, week recap'
    }

    # Get pending posts that can be scheduled
    pending_posts = Post.objects.filter(
        user=request.user,
        posted=False
    ).exclude(
        schedule__isnull=False
    )[:10]  # Limit to 10 for performance

    # Calculate scheduling recommendations
    recommendations = []
    if pending_posts.exists():
        recommendations.append(f"You have {pending_posts.count()} posts ready to schedule.")

    if user_analytics.best_posting_hour:
        recommendations.append(f"Your best posting time is {user_analytics.best_posting_hour}:00.")

    if scheduled_posts.count() < 7:
        recommendations.append("Consider scheduling more posts to maintain consistent presence.")

    context = {
        'scheduled_posts': scheduled_posts,
        'pending_posts': pending_posts,
        'optimal_times': optimal_times,
        'content_suggestions': content_suggestions,
        'recommendations': recommendations,
        'current_month': timezone.now().strftime('%B %Y'),
        'user_analytics': user_analytics,
        'total_scheduled': scheduled_posts.count(),
        'total_pending': pending_posts.count()
    }

    return render(request, 'advanced_scheduler.html', context)


def create_template(request):
    """Create a new content template."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ContentTemplate

    try:
        template = ContentTemplate.objects.create(
            user=request.user,
            name=request.POST.get('name'),
            template_type=request.POST.get('template_type'),
            content=request.POST.get('content'),
            industry=request.POST.get('industry', ''),
            tone=request.POST.get('tone', ''),
            hashtags=request.POST.get('hashtags', ''),
            is_public=request.POST.get('is_public') == 'true'
        )

        return JsonResponse({
            'success': True,
            'template_id': template.id,
            'message': f'Template "{template.name}" created successfully!'
        })

    except Exception as e:
        logger.error(f"Error creating template: {e}")
        return JsonResponse({'error': 'Failed to create template'}, status=500)


def delete_template(request, template_id):
    """Delete a content template."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'DELETE':
        return JsonResponse({'error': 'DELETE method required'}, status=405)

    from .models import ContentTemplate

    try:
        template = ContentTemplate.objects.get(id=template_id, user=request.user)
        template_name = template.name
        template.delete()

        return JsonResponse({
            'success': True,
            'message': f'Template "{template_name}" deleted successfully!'
        })

    except ContentTemplate.DoesNotExist:
        return JsonResponse({'error': 'Template not found'}, status=404)
    except Exception as e:
        logger.error(f"Error deleting template: {e}")
        return JsonResponse({'error': 'Failed to delete template'}, status=500)


def schedule_post(request):
    """Schedule a post for future publication."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ScheduledPost
    from datetime import datetime

    try:
        post_id = request.POST.get('post_id')
        scheduled_for = request.POST.get('scheduled_for')
        platforms = request.POST.getlist('platforms')

        if not post_id or not scheduled_for:
            return JsonResponse({'error': 'Post ID and schedule time required'}, status=400)

        # Get the post
        post = Post.objects.get(id=post_id, user=request.user)

        # Parse the scheduled time
        scheduled_datetime = datetime.fromisoformat(scheduled_for.replace('Z', '+00:00'))

        # Create or update scheduled post
        scheduled_post, created = ScheduledPost.objects.get_or_create(
            post=post,
            defaults={
                'user': request.user,
                'scheduled_for': scheduled_datetime,
                'target_platforms': platforms or ['instagram']
            }
        )

        if not created:
            scheduled_post.scheduled_for = scheduled_datetime
            scheduled_post.target_platforms = platforms or ['instagram']
            scheduled_post.status = 'pending'
            scheduled_post.save()

        return JsonResponse({
            'success': True,
            'message': f'Post scheduled for {scheduled_datetime.strftime("%B %d, %Y at %I:%M %p")}'
        })

    except Post.DoesNotExist:
        return JsonResponse({'error': 'Post not found'}, status=404)
    except ValueError as e:
        return JsonResponse({'error': f'Invalid date format: {e}'}, status=400)
    except Exception as e:
        logger.error(f"Error scheduling post: {e}")
        return JsonResponse({'error': 'Failed to schedule post'}, status=500)


def get_analytics_data(request):
    """Get analytics data for charts and graphs."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    from .models import PostAnalytics, UserAnalytics
    from datetime import datetime, timedelta

    try:
        days = int(request.GET.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)

        # Get post analytics for the period
        analytics = PostAnalytics.objects.filter(
            post__user=request.user,
            post__created__gte=start_date
        ).order_by('post__created')

        # Prepare data for charts
        engagement_data = []
        reach_data = []
        dates = []

        for a in analytics:
            dates.append(a.post.created.strftime('%Y-%m-%d'))
            engagement_data.append(a.engagement_rate)
            reach_data.append(a.reach)

        # Get user analytics
        user_analytics = UserAnalytics.objects.filter(user=request.user).first()

        return JsonResponse({
            'success': True,
            'data': {
                'dates': dates,
                'engagement_rates': engagement_data,
                'reach': reach_data,
                'total_posts': len(analytics),
                'avg_engagement': user_analytics.avg_engagement_rate if user_analytics else 0,
                'total_likes': user_analytics.total_likes if user_analytics else 0,
                'total_comments': user_analytics.total_comments if user_analytics else 0
            }
        })

    except Exception as e:
        logger.error(f"Error getting analytics data: {e}")
        return JsonResponse({'error': 'Failed to get analytics data'}, status=500)


def export_analytics(request):
    """Export analytics data to CSV."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    import csv
    from django.http import HttpResponse
    from .models import PostAnalytics

    try:
        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="analytics_{request.user.username}_{timezone.now().strftime("%Y%m%d")}.csv"'

        writer = csv.writer(response)

        # Write header
        writer.writerow([
            'Post ID', 'Caption', 'Created Date', 'Likes', 'Comments',
            'Shares', 'Saves', 'Reach', 'Impressions', 'Engagement Rate'
        ])

        # Write data
        analytics = PostAnalytics.objects.filter(
            post__user=request.user
        ).select_related('post').order_by('-post__created')

        for a in analytics:
            writer.writerow([
                a.post.id,
                a.post.caption[:50] + '...' if len(a.post.caption) > 50 else a.post.caption,
                a.post.created.strftime('%Y-%m-%d %H:%M'),
                a.likes_count,
                a.comments_count,
                a.shares_count,
                a.saves_count,
                a.reach,
                a.impressions,
                f'{a.engagement_rate:.2f}%'
            ])

        return response

    except Exception as e:
        logger.error(f"Error exporting analytics: {e}")
        return JsonResponse({'error': 'Failed to export analytics'}, status=500)


def bulk_schedule_posts(request):
    """Bulk schedule multiple posts."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ScheduledPost
    from datetime import datetime
    import json

    try:
        data = json.loads(request.body)
        post_ids = data.get('post_ids', [])
        schedule_times = data.get('schedule_times', [])
        platforms = data.get('platforms', ['instagram'])

        if len(post_ids) != len(schedule_times):
            return JsonResponse({'error': 'Post IDs and schedule times must match'}, status=400)

        scheduled_count = 0
        errors = []

        for post_id, schedule_time in zip(post_ids, schedule_times):
            try:
                post = Post.objects.get(id=post_id, user=request.user)
                scheduled_datetime = datetime.fromisoformat(schedule_time.replace('Z', '+00:00'))

                scheduled_post, created = ScheduledPost.objects.get_or_create(
                    post=post,
                    defaults={
                        'user': request.user,
                        'scheduled_for': scheduled_datetime,
                        'target_platforms': platforms
                    }
                )

                if not created:
                    scheduled_post.scheduled_for = scheduled_datetime
                    scheduled_post.target_platforms = platforms
                    scheduled_post.status = 'pending'
                    scheduled_post.save()

                scheduled_count += 1

            except Post.DoesNotExist:
                errors.append(f'Post {post_id} not found')
            except ValueError as e:
                errors.append(f'Invalid date format for post {post_id}: {e}')
            except Exception as e:
                errors.append(f'Error scheduling post {post_id}: {e}')

        return JsonResponse({
            'success': True,
            'scheduled_count': scheduled_count,
            'errors': errors,
            'message': f'Successfully scheduled {scheduled_count} posts'
        })

    except Exception as e:
        logger.error(f"Error bulk scheduling posts: {e}")
        return JsonResponse({'error': 'Failed to bulk schedule posts'}, status=500)


def optimize_posting_schedule(request):
    """Get AI-powered posting schedule optimization."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    from .models import PostAnalytics, UserAnalytics
    from datetime import datetime, timedelta

    try:
        # Get user analytics
        user_analytics = UserAnalytics.objects.filter(user=request.user).first()

        # Analyze posting patterns
        post_analytics = PostAnalytics.objects.filter(
            post__user=request.user,
            post__created__gte=timezone.now() - timedelta(days=30)
        ).select_related('post')

        # Group by hour and day of week
        hourly_performance = {}
        daily_performance = {}

        for analytics in post_analytics:
            hour = analytics.post.created.hour
            day = analytics.post.created.weekday()

            if hour not in hourly_performance:
                hourly_performance[hour] = []
            hourly_performance[hour].append(analytics.engagement_rate)

            if day not in daily_performance:
                daily_performance[day] = []
            daily_performance[day].append(analytics.engagement_rate)

        # Calculate optimal times
        best_hours = []
        for hour, rates in hourly_performance.items():
            avg_rate = sum(rates) / len(rates)
            best_hours.append({'hour': hour, 'engagement': avg_rate})

        best_hours.sort(key=lambda x: x['engagement'], reverse=True)

        best_days = []
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        for day, rates in daily_performance.items():
            avg_rate = sum(rates) / len(rates)
            best_days.append({'day': day_names[day], 'engagement': avg_rate})

        best_days.sort(key=lambda x: x['engagement'], reverse=True)

        # Generate recommendations
        recommendations = []

        if best_hours:
            top_hour = best_hours[0]
            recommendations.append(f"Your best posting hour is {top_hour['hour']}:00 with {top_hour['engagement']:.2f}% avg engagement")

        if best_days:
            top_day = best_days[0]
            recommendations.append(f"Your best posting day is {top_day['day']} with {top_day['engagement']:.2f}% avg engagement")

        if user_analytics and user_analytics.avg_engagement_rate > 0:
            if user_analytics.avg_engagement_rate > 5:
                recommendations.append("Your engagement rate is above average! Keep up the great work.")
            elif user_analytics.avg_engagement_rate < 2:
                recommendations.append("Consider posting more engaging content to improve your engagement rate.")

        # Generate optimal schedule for next week
        optimal_schedule = []
        if best_hours and best_days:
            for i in range(7):  # Next 7 days
                date = timezone.now() + timedelta(days=i+1)
                day_of_week = date.weekday()

                # Find best time for this day
                best_time = best_hours[0]['hour']  # Default to overall best hour
                for day_data in best_days:
                    if day_names[day_of_week] == day_data['day']:
                        # Use this day's best time if available
                        break

                optimal_schedule.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'day': day_names[day_of_week],
                    'time': f'{best_time:02d}:00',
                    'expected_engagement': best_hours[0]['engagement']
                })

        return JsonResponse({
            'success': True,
            'best_hours': best_hours[:5],  # Top 5 hours
            'best_days': best_days,
            'recommendations': recommendations,
            'optimal_schedule': optimal_schedule,
            'analysis_period': '30 days',
            'posts_analyzed': post_analytics.count()
        })

    except Exception as e:
        logger.error(f"Error optimizing posting schedule: {e}")
        return JsonResponse({'error': 'Failed to optimize posting schedule'}, status=500)


def connect_accounts(request):
    """Social media account connection interface."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import SocialMediaAccount

    # Get user's connected accounts
    connected_accounts = SocialMediaAccount.objects.filter(user=request.user)

    # Available platforms
    available_platforms = [
        {
            'id': 'instagram',
            'name': 'Instagram',
            'icon': 'bi-instagram',
            'color': 'text-danger',
            'description': 'Share photos and stories with your Instagram followers',
            'features': ['Photo posts', 'Stories', 'Reels', 'IGTV'],
            'connected': connected_accounts.filter(platform='instagram').exists()
        },
        {
            'id': 'twitter',
            'name': 'Twitter',
            'icon': 'bi-twitter',
            'color': 'text-info',
            'description': 'Tweet and engage with your Twitter audience',
            'features': ['Tweets', 'Threads', 'Media posts', 'Polls'],
            'connected': connected_accounts.filter(platform='twitter').exists()
        },
        {
            'id': 'linkedin',
            'name': 'LinkedIn',
            'icon': 'bi-linkedin',
            'color': 'text-primary',
            'description': 'Share professional content with your LinkedIn network',
            'features': ['Posts', 'Articles', 'Company pages', 'Professional updates'],
            'connected': connected_accounts.filter(platform='linkedin').exists()
        },
        {
            'id': 'facebook',
            'name': 'Facebook',
            'icon': 'bi-facebook',
            'color': 'text-primary',
            'description': 'Connect with friends and share on Facebook',
            'features': ['Posts', 'Stories', 'Pages', 'Groups'],
            'connected': connected_accounts.filter(platform='facebook').exists()
        },
        {
            'id': 'tiktok',
            'name': 'TikTok',
            'icon': 'bi-tiktok',
            'color': 'text-dark',
            'description': 'Create and share short-form videos on TikTok',
            'features': ['Videos', 'Effects', 'Sounds', 'Hashtag challenges'],
            'connected': connected_accounts.filter(platform='tiktok').exists()
        },
        {
            'id': 'youtube',
            'name': 'YouTube',
            'icon': 'bi-youtube',
            'color': 'text-danger',
            'description': 'Upload and manage your YouTube content',
            'features': ['Videos', 'Shorts', 'Community posts', 'Live streaming'],
            'connected': connected_accounts.filter(platform='youtube').exists()
        }
    ]

    context = {
        'connected_accounts': connected_accounts,
        'available_platforms': available_platforms,
        'total_connected': connected_accounts.count(),
    }

    return render(request, 'connect_accounts.html', context)


def connect_platform(request, platform):
    """Initiate OAuth connection for a specific platform."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    from .models import SocialMediaAccount

    # Check if platform is supported
    supported_platforms = ['instagram', 'twitter', 'linkedin', 'facebook', 'tiktok', 'youtube']
    if platform not in supported_platforms:
        return JsonResponse({'error': 'Unsupported platform'}, status=400)

    # Check if already connected
    existing_account = SocialMediaAccount.objects.filter(
        user=request.user,
        platform=platform
    ).first()

    if existing_account:
        return JsonResponse({'error': 'Platform already connected'}, status=400)

    # For demo purposes, create a mock connection
    # In production, this would initiate OAuth flow
    try:
        # Create mock account
        account = SocialMediaAccount.objects.create(
            user=request.user,
            platform=platform,
            username=f"{request.user.username}_{platform}",
            display_name=f"{request.user.username} on {platform.title()}",
            status='connected',
            follower_count=random.randint(100, 10000),
            following_count=random.randint(50, 1000),
            posts_count=random.randint(10, 500),
            platform_user_id=f"demo_{platform}_{request.user.id}",
            platform_data={
                'demo_account': True,
                'connected_via': 'imagsta_demo',
                'permissions': ['read', 'write']
            }
        )

        messages.success(request, f'{platform.title()} account connected successfully!')

        return JsonResponse({
            'success': True,
            'message': f'{platform.title()} connected successfully!',
            'account': {
                'id': account.id,
                'platform': account.platform,
                'username': account.username,
                'status': account.status
            }
        })

    except Exception as e:
        logger.error(f"Error connecting {platform}: {e}")
        return JsonResponse({'error': f'Failed to connect {platform}'}, status=500)


def disconnect_platform(request, account_id):
    """Disconnect a social media account."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import SocialMediaAccount

    try:
        account = SocialMediaAccount.objects.get(
            id=account_id,
            user=request.user
        )

        platform_name = account.platform.title()
        account.delete()

        messages.success(request, f'{platform_name} account disconnected successfully!')

        return JsonResponse({
            'success': True,
            'message': f'{platform_name} disconnected successfully!'
        })

    except SocialMediaAccount.DoesNotExist:
        return JsonResponse({'error': 'Account not found'}, status=404)
    except Exception as e:
        logger.error(f"Error disconnecting account {account_id}: {e}")
        return JsonResponse({'error': 'Failed to disconnect account'}, status=500)


def refresh_account_data(request, account_id):
    """Refresh data for a connected social media account."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    from .models import SocialMediaAccount
    import random

    try:
        account = SocialMediaAccount.objects.get(
            id=account_id,
            user=request.user
        )

        # Mock data refresh (in production, this would call platform APIs)
        account.update_metrics(
            follower_count=account.follower_count + random.randint(-10, 50),
            following_count=account.following_count + random.randint(-5, 20),
            posts_count=account.posts_count + random.randint(0, 5)
        )

        return JsonResponse({
            'success': True,
            'message': 'Account data refreshed successfully!',
            'data': {
                'follower_count': account.follower_count,
                'following_count': account.following_count,
                'posts_count': account.posts_count,
                'last_sync': account.last_sync.isoformat()
            }
        })

    except SocialMediaAccount.DoesNotExist:
        return JsonResponse({'error': 'Account not found'}, status=404)
    except Exception as e:
        logger.error(f"Error refreshing account {account_id}: {e}")
        return JsonResponse({'error': 'Failed to refresh account data'}, status=500)


def create_template(request):
    """Create a new content template."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ContentTemplate
    import json

    try:
        name = request.POST.get('name', '').strip()
        content = request.POST.get('content', '').strip()
        template_type = request.POST.get('template_type', 'caption')
        industry = request.POST.get('industry', 'general')
        tone = request.POST.get('tone', 'professional')
        hashtags = request.POST.get('hashtags', '').strip()
        is_public = request.POST.get('is_public') == 'on'

        if not name or not content:
            return JsonResponse({'error': 'Name and content are required'}, status=400)

        # Create template
        template = ContentTemplate.objects.create(
            user=request.user,
            name=name,
            content=content,
            template_type=template_type,
            industry=industry,
            tone=tone,
            hashtags=hashtags,
            is_public=is_public,
            usage_count=0
        )

        return JsonResponse({
            'success': True,
            'message': 'Template created successfully!',
            'template_id': template.id
        })

    except Exception as e:
        logger.error(f"Error creating template: {e}")
        return JsonResponse({'error': 'Failed to create template'}, status=500)


def duplicate_template(request, template_id):
    """Duplicate an existing template."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ContentTemplate

    try:
        original_template = ContentTemplate.objects.get(
            id=template_id,
            user=request.user
        )

        # Create duplicate
        duplicate = ContentTemplate.objects.create(
            user=request.user,
            name=f"{original_template.name} (Copy)",
            content=original_template.content,
            template_type=original_template.template_type,
            industry=original_template.industry,
            tone=original_template.tone,
            hashtags=original_template.hashtags,
            is_public=False,  # Duplicates are private by default
            usage_count=0
        )

        return JsonResponse({
            'success': True,
            'message': 'Template duplicated successfully!',
            'template_id': duplicate.id
        })

    except ContentTemplate.DoesNotExist:
        return JsonResponse({'error': 'Template not found'}, status=404)
    except Exception as e:
        logger.error(f"Error duplicating template {template_id}: {e}")
        return JsonResponse({'error': 'Failed to duplicate template'}, status=500)


def delete_template(request, template_id):
    """Delete a template."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'DELETE':
        return JsonResponse({'error': 'DELETE method required'}, status=405)

    from .models import ContentTemplate

    try:
        template = ContentTemplate.objects.get(
            id=template_id,
            user=request.user
        )

        template_name = template.name
        template.delete()

        return JsonResponse({
            'success': True,
            'message': f'Template "{template_name}" deleted successfully!'
        })

    except ContentTemplate.DoesNotExist:
        return JsonResponse({'error': 'Template not found'}, status=404)
    except Exception as e:
        logger.error(f"Error deleting template {template_id}: {e}")
        return JsonResponse({'error': 'Failed to delete template'}, status=500)


def export_templates(request):
    """Export user's templates as JSON."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    from .models import ContentTemplate
    import json
    from django.http import HttpResponse

    try:
        templates = ContentTemplate.objects.filter(user=request.user)

        export_data = {
            'export_date': timezone.now().isoformat(),
            'user': request.user.username,
            'templates': []
        }

        for template in templates:
            export_data['templates'].append({
                'name': template.name,
                'content': template.content,
                'template_type': template.template_type,
                'industry': template.industry,
                'tone': template.tone,
                'hashtags': template.hashtags,
                'is_public': template.is_public,
                'usage_count': template.usage_count,
                'created': template.created.isoformat()
            })

        response = HttpResponse(
            json.dumps(export_data, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = f'attachment; filename="imagsta_templates_{request.user.username}.json"'

        return response

    except Exception as e:
        logger.error(f"Error exporting templates: {e}")
        return JsonResponse({'error': 'Failed to export templates'}, status=500)


def import_templates(request):
    """Import templates from JSON file."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ContentTemplate
    import json

    try:
        uploaded_file = request.FILES.get('file')
        if not uploaded_file:
            return JsonResponse({'error': 'No file uploaded'}, status=400)

        if not uploaded_file.name.endswith('.json'):
            return JsonResponse({'error': 'File must be a JSON file'}, status=400)

        # Read and parse JSON
        file_content = uploaded_file.read().decode('utf-8')
        data = json.loads(file_content)

        if 'templates' not in data:
            return JsonResponse({'error': 'Invalid file format'}, status=400)

        imported_count = 0

        for template_data in data['templates']:
            # Check if template with same name already exists
            existing = ContentTemplate.objects.filter(
                user=request.user,
                name=template_data.get('name', '')
            ).exists()

            if existing:
                # Add suffix to avoid duplicates
                template_data['name'] = f"{template_data['name']} (Imported)"

            # Create template
            ContentTemplate.objects.create(
                user=request.user,
                name=template_data.get('name', 'Imported Template'),
                content=template_data.get('content', ''),
                template_type=template_data.get('template_type', 'caption'),
                industry=template_data.get('industry', 'general'),
                tone=template_data.get('tone', 'professional'),
                hashtags=template_data.get('hashtags', ''),
                is_public=False,  # Imported templates are private by default
                usage_count=0
            )
            imported_count += 1

        return JsonResponse({
            'success': True,
            'message': f'Successfully imported {imported_count} templates!',
            'count': imported_count
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON file'}, status=400)
    except Exception as e:
        logger.error(f"Error importing templates: {e}")
        return JsonResponse({'error': 'Failed to import templates'}, status=500)


# News Dashboard Views

def news_dashboard(request):
    """Main news dashboard showing highlights and feeds."""
    try:
        # Get user's category preferences
        user_categories = []
        if hasattr(request.user, 'news_preferences'):
            user_categories = request.user.news_preferences.preferred_categories.all()

        # Get recent highlights
        highlights_query = AIHighlight.objects.select_related('article', 'article__source').order_by('-highlight_score', '-created')

        # Filter by user's preferred categories if set
        if user_categories:
            highlights_query = highlights_query.filter(article__categories__in=user_categories).distinct()

        highlights = highlights_query[:10]

        # Get recent articles by category
        categories = Category.objects.filter(is_active=True).prefetch_related('articles')

        category_articles = {}
        for category in categories:
            articles = category.articles.filter(
                published_date__gte=timezone.now() - timedelta(days=7)
            ).order_by('-published_date')[:5]

            if articles:
                category_articles[category] = articles

        # Get news sources status
        sources = NewsSource.objects.filter(is_active=True).annotate(
            recent_articles_count=models.Count(
                'articles',
                filter=models.Q(articles__published_date__gte=timezone.now() - timedelta(hours=24))
            )
        )

        # Get user's connected accounts with categories
        connected_accounts = []
        if hasattr(request.user, 'social_accounts'):
            accounts = request.user.social_accounts.filter(status='connected')
            for account in accounts:
                account_categories = account.category_mappings.select_related('category').all()
                connected_accounts.append({
                    'account': account,
                    'categories': [mapping.category for mapping in account_categories]
                })

        # Statistics
        stats = {
            'total_articles': NewsArticle.objects.filter(
                published_date__gte=timezone.now() - timedelta(days=1)
            ).count(),
            'highlighted_articles': highlights.count(),
            'active_sources': sources.filter(
                last_fetch__gte=timezone.now() - timedelta(hours=6)
            ).count(),
            'categories_count': categories.count(),
        }

        context = {
            'highlights': highlights,
            'category_articles': category_articles,
            'sources': sources,
            'connected_accounts': connected_accounts,
            'stats': stats,
            'user_categories': user_categories,
        }

        return render(request, 'news_dashboard.html', context)

    except Exception as e:
        logger.error(f"Error in news dashboard: {e}")
        messages.error(request, 'Error loading news dashboard')
        return redirect('index')


@login_required
def news_highlights(request):
    """View for managing news highlights."""
    try:
        # Get filter parameters
        category_filter = request.GET.get('category')
        min_score = request.GET.get('min_score', 60)

        # Build query
        highlights_query = AIHighlight.objects.select_related(
            'article', 'article__source'
        ).prefetch_related('article__categories')

        if category_filter:
            highlights_query = highlights_query.filter(
                article__categories__name=category_filter
            )

        try:
            min_score = float(min_score)
            highlights_query = highlights_query.filter(highlight_score__gte=min_score)
        except ValueError:
            min_score = 60

        highlights = highlights_query.order_by('-highlight_score', '-created')[:50]

        # Get categories for filter
        categories = Category.objects.filter(is_active=True).order_by('display_name')

        context = {
            'highlights': highlights,
            'categories': categories,
            'current_category': category_filter,
            'current_min_score': min_score,
        }

        return render(request, 'news_highlights.html', context)

    except Exception as e:
        logger.error(f"Error in news highlights: {e}")
        messages.error(request, 'Error loading highlights')
        return redirect('news-dashboard')


@login_required
def news_sources_management(request):
    """View for managing news sources."""
    try:
        sources = NewsSource.objects.all().prefetch_related('categories').annotate(
            articles_count=models.Count('articles'),
            recent_articles_count=models.Count(
                'articles',
                filter=models.Q(articles__published_date__gte=timezone.now() - timedelta(days=7))
            )
        ).order_by('name')

        categories = Category.objects.filter(is_active=True).order_by('display_name')

        context = {
            'sources': sources,
            'categories': categories,
        }

        return render(request, 'news_sources.html', context)

    except Exception as e:
        logger.error(f"Error in news sources management: {e}")
        messages.error(request, 'Error loading news sources')
        return redirect('news-dashboard')


@login_required
@require_http_methods(["POST"])
def create_post_from_news(request):
    """Create a social media post from a news article."""
    try:
        article_id = request.POST.get('article_id')
        account_ids = request.POST.getlist('account_ids')
        custom_caption = request.POST.get('custom_caption', '').strip()

        if not article_id:
            return JsonResponse({'error': 'Article ID required'}, status=400)

        # Get the article and its highlight
        try:
            article = NewsArticle.objects.get(id=article_id)
            highlight = getattr(article, 'highlight', None)
        except NewsArticle.DoesNotExist:
            return JsonResponse({'error': 'Article not found'}, status=404)

        # Use custom caption or suggested caption
        caption = custom_caption or (highlight.suggested_caption if highlight else article.title)

        # Get hashtags
        hashtags = []
        if highlight and highlight.suggested_hashtags:
            hashtags = highlight.suggested_hashtags
        else:
            # Generate basic hashtags
            hashtags = ['#news', '#update', '#trending']
            if article.topics:
                hashtags.extend([f"#{topic}" for topic in article.topics[:3]])

        hashtags_text = ' '.join(hashtags)
        full_caption = f"{caption}\n\n{hashtags_text}\n\nSource: {article.url}"

        # Create post
        post = Post.objects.create(
            user=request.user,
            caption=full_caption,
            post_text=caption,
            hashtags=hashtags_text,
        )

        # If specific accounts selected, create publications
        if account_ids:
            from .models import SocialMediaAccount, PostPublication
            accounts = SocialMediaAccount.objects.filter(
                id__in=account_ids,
                user=request.user,
                status='connected'
            )

            for account in accounts:
                PostPublication.objects.create(
                    post=post,
                    account=account,
                    status='pending'
                )

        # Mark highlight as posted if exists
        if highlight:
            highlight.mark_as_posted()

        messages.success(request, f'Post created successfully from article: {article.title[:50]}...')

        return JsonResponse({
            'success': True,
            'post_id': post.id,
            'message': 'Post created successfully'
        })

    except Exception as e:
        logger.error(f"Error creating post from news: {e}")
        return JsonResponse({'error': 'Failed to create post'}, status=500)


@login_required
@require_http_methods(["POST"])
def toggle_news_source(request):
    """Toggle news source active status."""
    try:
        source_id = request.POST.get('source_id')

        if not source_id:
            return JsonResponse({'error': 'Source ID required'}, status=400)

        try:
            source = NewsSource.objects.get(id=source_id)
        except NewsSource.DoesNotExist:
            return JsonResponse({'error': 'Source not found'}, status=404)

        # Toggle active status
        source.is_active = not source.is_active
        source.save()

        status_text = 'activated' if source.is_active else 'deactivated'

        return JsonResponse({
            'success': True,
            'is_active': source.is_active,
            'message': f'Source {status_text} successfully'
        })

    except Exception as e:
        logger.error(f"Error toggling news source: {e}")
        return JsonResponse({'error': 'Failed to toggle source'}, status=500)


def all_news(request):
    """Simple view to show all news articles without login requirement."""
    try:
        # Get all articles with highlights
        articles = NewsArticle.objects.select_related('source').prefetch_related(
            'categories', 'highlight'
        ).order_by('-published_date')[:50]

        # Get highlights
        highlights = AIHighlight.objects.select_related('article', 'article__source').order_by(
            '-highlight_score'
        )[:20]

        # Get categories
        categories = Category.objects.filter(is_active=True)

        context = {
            'articles': articles,
            'highlights': highlights,
            'categories': categories,
            'total_articles': NewsArticle.objects.count(),
            'total_highlights': AIHighlight.objects.count(),
        }

        return render(request, 'all_news.html', context)

    except Exception as e:
        logger.error(f"Error in all_news view: {e}")
        return render(request, 'all_news.html', {
            'articles': [],
            'highlights': [],
            'categories': [],
            'total_articles': 0,
            'total_highlights': 0,
            'error': str(e)
        })


@login_required
def account_categories_management(request):
    """View for managing account-category mappings."""
    try:
        # Get user's social media accounts
        accounts = request.user.social_accounts.filter(status='connected').prefetch_related(
            'category_mappings__category'
        )

        # Get all categories
        categories = Category.objects.filter(is_active=True).order_by('display_name')

        # Prepare account data with current mappings
        account_data = []
        for account in accounts:
            current_mappings = {
                mapping.category.id: mapping for mapping in account.category_mappings.all()
            }

            account_data.append({
                'account': account,
                'current_mappings': current_mappings,
                'mapped_categories': [mapping.category for mapping in current_mappings.values()]
            })

        context = {
            'account_data': account_data,
            'categories': categories,
        }

        return render(request, 'account_categories.html', context)

    except Exception as e:
        logger.error(f"Error in account categories management: {e}")
        messages.error(request, 'Error loading account categories')
        return redirect('news-dashboard')


@login_required
@require_http_methods(["POST"])
def update_account_categories(request):
    """Update account-category mappings."""
    try:
        account_id = request.POST.get('account_id')
        category_ids = request.POST.getlist('category_ids')

        if not account_id:
            return JsonResponse({'error': 'Account ID required'}, status=400)

        # Get the account
        try:
            account = request.user.social_accounts.get(id=account_id, status='connected')
        except:
            return JsonResponse({'error': 'Account not found'}, status=404)

        # Clear existing mappings
        account.category_mappings.all().delete()

        # Create new mappings
        created_count = 0
        for category_id in category_ids:
            try:
                category = Category.objects.get(id=category_id, is_active=True)
                AccountCategoryMapping.objects.create(
                    account=account,
                    category=category,
                    priority=created_count + 1,  # Set priority based on order
                )
                created_count += 1
            except Category.DoesNotExist:
                continue

        return JsonResponse({
            'success': True,
            'message': f'Updated categories for {account.platform} account',
            'mapped_count': created_count
        })

    except Exception as e:
        logger.error(f"Error updating account categories: {e}")
        return JsonResponse({'error': 'Failed to update categories'}, status=500)